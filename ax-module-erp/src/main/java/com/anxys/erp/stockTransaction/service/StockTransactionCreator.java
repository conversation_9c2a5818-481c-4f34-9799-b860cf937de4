package com.anxys.erp.stockTransaction.service;

import com.anxys.erp.order.salesOrder.domain.SalesOrder;
import com.anxys.erp.order.salesOrder.service.ErpNoRedisDAO;
import com.anxys.erp.order.salesOrder.service.SalesOrderService;
import com.anxys.erp.order.salesOrderItem.domain.SalesOrderItem;
import com.anxys.erp.order.salesOrderItem.service.SalesOrderItemService;
import com.anxys.erp.purchaseOrder.domain.PurchaseOrder;
import com.anxys.erp.purchaseOrder.service.PurchaseOrderService;
import com.anxys.erp.purchaseOrderItem.domain.PurchaseOrderItem;
import com.anxys.erp.purchaseOrderItem.service.PurchaseOrderItemService;
import com.anxys.erp.salesReturnOrder.domain.SalesReturnOrder;
import com.anxys.erp.salesReturnOrder.service.SalesReturnOrderService;
import com.anxys.erp.salesReturnOrderItem.domain.SalesReturnOrderItem;
import com.anxys.erp.salesReturnOrderItem.service.SalesReturnOrderItemService;
import com.anxys.erp.stockCheck.domain.StockCheck;
import com.anxys.erp.stockCheck.service.StockCheckService;
import com.anxys.erp.stockCheckItem.domain.StockCheckItem;
import com.anxys.erp.stockCheckItem.service.StockCheckItemService;
import com.anxys.erp.stockTransaction.domain.StockTransaction;
import com.anxys.erp.stockTransaction.domain.form.*;
import com.anxys.erp.stockTransaction.enums.AuditStatusEnum;
import com.anxys.erp.stockTransaction.enums.ProcessStatusEnum;
import com.anxys.erp.stockTransaction.enums.StockTransactionRelatedTypeEnum;
import com.anxys.erp.stockTransaction.enums.StockTransactionTypeEnum;
import com.anxys.erp.stockTransactionItem.domain.StockTransactionItem;
import com.anxys.erp.warehouse.service.WarehouseService;
import com.anxys.framework.common.exception.BusinessException;
import com.anxys.mfg.productionOrder.domain.ProductionOrder;
import com.anxys.mfg.productionOrder.service.ProductionOrderService;
import com.anxys.mfg.productionOrderItem.domain.ProductionOrderItem;
import com.anxys.mfg.productionOrderItem.service.ProductionOrderItemService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 库存流水创建器
 * 基于方法重载实现统一的库存流水创建接口
 * 
 * <AUTHOR>
 * @since 2025-06-27
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class StockTransactionCreator {
    
    private final StockTransactionBuilderHelper builderHelper;
    private final ErpNoRedisDAO erpNoRedisDAO;
    private final PurchaseOrderService purchaseOrderService;
    private final PurchaseOrderItemService purchaseOrderItemService;
    private final SalesOrderService salesOrderService;
    private final SalesOrderItemService salesOrderItemService;
    private final WarehouseService warehouseService;
    private final ProductionOrderService productionOrderService;
    private final ProductionOrderItemService productionOrderItemService;
    private final SalesReturnOrderService salesReturnOrderService;
    private final SalesReturnOrderItemService salesReturnOrderItemService;
    private final StockCheckService stockCheckService;
    private final StockCheckItemService stockCheckItemService;
    // ==================== 统一接口 - 方法重载 ====================
    
    /**
     * 创建库存流水 - 采购入库
     */
    @Transactional(rollbackFor = Exception.class)
    public Long createStockTransaction(PurchaseStockInParam param) {
        log.info("处理采购入库流水，采购订单ID: {}", param.getPurchaseOrderId());
        
        // 1. 获取采购订单信息
        PurchaseOrder order = purchaseOrderService.queryById(param.getPurchaseOrderId());
        validateOrder(order, "采购订单");
        
        // 2. 生成流水单号
        String transactionNo = erpNoRedisDAO.generate(ErpNoRedisDAO.STOCK_IN_NO_PREFIX);
        
        // 3. 先构建流水明细
        List<StockTransactionItem> items = param.getStockInItems().stream()
                .map(item -> {
                    // 获取采购订单明细信息
                    PurchaseOrderItem orderItem = purchaseOrderItemService.queryById(item.getItemId());
                    
                    // 构建库存流水明细（暂时使用原始数量）
                    return buildBaseTransactionItem(
                            transactionNo,
                            orderItem.getWarehouseId(),
                            orderItem.getWarehouseName(),
                            orderItem.getSkuId(),
                            orderItem.getSkuCode(),
                            orderItem.getSpecSummary(),
                            orderItem.getProductId(),
                            orderItem.getProductCode(),
                            orderItem.getProductName(),
                            orderItem.getUnit(),
                            orderItem.getUnitPrice(),
                            item.getQuantity(), // 暂时使用原始数量
                            order.getRemark() != null ? order.getRemark() : "采购入库"
                    );
                })
                .collect(Collectors.toList());
        
        // 4. 统一处理数量符号 - 关键改进点
        normalizeQuantitySign(StockTransactionTypeEnum.IN_PURCHASE, items);
        
        // 5. 使用封装方法构建主表（自动计算总量）
        StockTransaction transaction = buildBaseTransaction(
                transactionNo,
                StockTransactionTypeEnum.IN_PURCHASE,
                StockTransactionRelatedTypeEnum.PURCHASE,
                order.getId(),
                order.getOrderNo(),
                order.getPurchaserId(),
                order.getPurchaserName(),
                items,
                order.getRemark() != null ? order.getRemark() : "采购入库"
        );
        
        // 7. 保存数据
        return builderHelper.saveTransactionWithItems(transaction, items);
    }

    /**
     * 创建库存流水 - 销售出库
     */
    @Transactional(rollbackFor = Exception.class)
    public Long createStockTransaction(SalesStockOutParam param) {
        log.info("处理销售出库流水，销售订单ID: {}", param.getSaleOrderId());
        
        // 1. 获取销售订单信息
        SalesOrder order = salesOrderService.queryById(param.getSaleOrderId());
        validateOrder(order, "销售订单");

        // 2. 生成流水单号
        String transactionNo = erpNoRedisDAO.generate(ErpNoRedisDAO.STOCK_OUT_NO_PREFIX);
        
        // 3. 先构建流水明细
        List<StockTransactionItem> items = param.getStockOutItems().stream()
                .map(item -> {
                    // 获取销售订单明细信息
                    SalesOrderItem orderItem = salesOrderItemService.queryById(item.getItemId());

                    // 构建库存流水明细（暂时使用原始数量）
                    return buildBaseTransactionItem(
                            transactionNo,
                            item.getWarehouseId(),
                            warehouseService.queryById(item.getWarehouseId()).getWarehouseName(),
                            orderItem.getSkuId(),
                            orderItem.getSkuCode(),
                            orderItem.getSpecSummary(),
                            orderItem.getProductId(),
                            orderItem.getProductCode(),
                            orderItem.getProductName(),
                            orderItem.getUnit(),
                            0.0,
                            item.getQuantity(), // 暂时使用原始数量
                            order.getRemark() != null ? order.getRemark() : "销售发货"
                    );
                })
                .collect(Collectors.toList());
        
        // 4. 统一处理数量符号 - 关键改进点
        normalizeQuantitySign(StockTransactionTypeEnum.OUT_SALES, items);
        
        // 5. 使用封装方法构建主表（自动计算总量）
        StockTransaction transaction = buildBaseTransaction(
                transactionNo,
                StockTransactionTypeEnum.OUT_SALES,
                StockTransactionRelatedTypeEnum.ORDER,
                order.getId(),
                order.getOrderNo(),
                order.getCustomerId(),
                order.getCustomerName(),
                items,
                order.getRemark() != null ? order.getRemark() : "销售发货"
        );
        
        // 7. 保存数据
        return builderHelper.saveTransactionWithItems(transaction, items);
    }

    /**
     * 创建库存流水 - 生产入库
     */
    @Transactional(rollbackFor = Exception.class)
    public Long createStockTransaction(ProductionStockInParam param) {
        log.info("处理生产入库流水，生产工单ID: {}", param.getProductionOrderId());
        // 1. 获取生产工单信息
        ProductionOrder order = productionOrderService.queryById(param.getProductionOrderId());
        validateOrder(order, "生产工单");

        // 2. 生成流水单号
        String transactionNo = erpNoRedisDAO.generate(ErpNoRedisDAO.STOCK_IN_NO_PREFIX);

        // 3. 先构建流水明细
        List<StockTransactionItem> items = param.getStockInItems().stream()
                .map(item -> {
                    // 获取生产工单明细信息
                    ProductionOrderItem orderItem = productionOrderItemService.queryById(item.getItemId());
                    
                    // 构建库存流水明细（暂时使用原始数量）
                    return buildBaseTransactionItem(
                            transactionNo,
                            orderItem.getWarehouseId(),
                            warehouseService.queryById(orderItem.getWarehouseId()).getWarehouseName(),
                            orderItem.getSkuId(),
                            orderItem.getSkuCode(),
                            orderItem.getSpecSummary(),
                            orderItem.getProductId(),
                            orderItem.getProductCode(),
                            orderItem.getProductName(),
                            orderItem.getUnit(),
                            0.0,
                            item.getQuantity(), // 暂时使用原始数量
                            order.getRemark() != null ? order.getRemark() : "生产入库"
                    );
                })
                .collect(Collectors.toList());

        // 4. 统一处理数量符号
        normalizeQuantitySign(StockTransactionTypeEnum.IN_PRODUCTION, items);
        
        // 5. 使用封装方法构建主表（自动计算总量）
        StockTransaction transaction = buildBaseTransaction(
                transactionNo,
                StockTransactionTypeEnum.IN_PRODUCTION,
                StockTransactionRelatedTypeEnum.PRODUCTION,
                order.getId(),
                order.getOrderNo(),
                order.getOperatorId(),
                order.getOperatorName(),
                items,
                order.getRemark() != null ? order.getRemark() : "生产入库"
        );

        // 6. 保存数据
        return builderHelper.saveTransactionWithItems(transaction, items);
    }

    /**
     * 创建库存流水 - 销售退货入库
     */
    @Transactional(rollbackFor = Exception.class)
    public Long createStockTransaction(SaleReturnStockInParam param) {
        log.info("处理销售退货入库流水，销售退货单ID: {}", param.getSaleReturnOrderId());
        // 1. 获取销售退货单信息
        SalesReturnOrder order = salesReturnOrderService.queryById(param.getSaleReturnOrderId());
        validateOrder(order, "销售退货单");

        // 2. 生成流水单号
        String transactionNo = erpNoRedisDAO.generate(ErpNoRedisDAO.STOCK_IN_NO_PREFIX);

        // 3. 先构建流水明细
        List<StockTransactionItem> items = param.getStockInItems().stream()
                .map(item -> {
                    // 获取销售退货单明细信息
                    SalesReturnOrderItem orderItem = salesReturnOrderItemService.queryById(item.getItemId());

                    // 构建库存流水明细（暂时使用原始数量）
                    return buildBaseTransactionItem(
                            transactionNo,
                            orderItem.getWarehouseId(),
                            warehouseService.queryById(orderItem.getWarehouseId()).getWarehouseName(),
                            orderItem.getSkuId(),
                            orderItem.getSkuCode(),
                            orderItem.getSpecSummary(),
                            orderItem.getProductId(),
                            orderItem.getProductCode(),
                            orderItem.getProductName(),
                            orderItem.getUnit(),
                            0.0,
                            Double.valueOf(orderItem.getReturnQuantity()), // 暂时使用原始数量
                            order.getRemark() != null ? order.getRemark() : "销售退货入库"
                    );
                })
                .collect(Collectors.toList());

        // 4. 统一处理数量符号
        normalizeQuantitySign(StockTransactionTypeEnum.IN_RETURN, items);
        
        // 5. 使用封装方法构建主表（自动计算总量）
        StockTransaction transaction = buildBaseTransaction(
                transactionNo,
                StockTransactionTypeEnum.IN_RETURN,
                StockTransactionRelatedTypeEnum.ORDER,
                order.getOriginalOrderId(),
                order.getOriginalOrderNo(),
                order.getCustomerId(),
                order.getCustomerName(),
                items,
                order.getRemark() != null ? order.getRemark() : "销售退货入库"
        );

        // 6. 保存数据
        return builderHelper.saveTransactionWithItems(transaction, items);
    }

    @Transactional(rollbackFor = Exception.class)
    public Long createStockTransaction(StockCheckInParam param) {
        log.info("处理盘点单流水，盘点单ID: {}", param.getStockCheckId());
        // 1. 获取盘点单信息
        StockCheck order = stockCheckService.queryById(param.getStockCheckId());
        validateOrder(order, "盘点单");

        // 2. 生成流水单号
        String transactionNo = erpNoRedisDAO.generate(ErpNoRedisDAO.STOCK_CHECK_NO_PREFIX);

        // 3. 先构建流水明细
        List<StockTransactionItem> items = param.getStockCheckItems().stream()
                .map(item -> {
                    // 获取盘点单明细信息
                    StockCheckItem orderItem = stockCheckItemService.queryById(item.getItemId());

                    // 构建库存流水明细（暂时使用原始数量）
                    return buildBaseTransactionItem(
                            transactionNo,
                            order.getWarehouseId(),
                            order.getWarehouseName(),
                            orderItem.getSkuId(),
                            orderItem.getSkuCode(),
                            orderItem.getSpecSummary(),
                            orderItem.getProductId(),
                            orderItem.getProductCode(),
                            orderItem.getProductName(),
                            orderItem.getUnit(),
                            0.0,
                            item.getQuantity(), // 使用参数中的数量，而不是盘点明细中的实际库存
                            order.getRemark() != null ? order.getRemark() : "库存盘点"
                    );
                })
                .collect(Collectors.toList());

        // 4. 判断正负符号
        normalizeQuantitySign(StockTransactionTypeEnum.IN_ADJUST, items);

        // 5. 使用封装方法构建主表（自动计算总量）
        StockTransaction transaction = buildBaseTransaction(
                transactionNo,
                StockTransactionTypeEnum.IN_ADJUST,
                StockTransactionRelatedTypeEnum.ORDER,
                order.getId(),
                order.getCheckNo(),
                order.getCheckerId(),
                order.getCheckerName(),
                items,
                order.getRemark() != null ? order.getRemark() : "库存盘点"
        );

        // 6. 保存数据
        return builderHelper.saveTransactionWithItems(transaction, items);
    }

    // ==================== 辅助方法 ====================
    
    private void validateOrder(Object order, String orderType) {
        if (order == null) {
            throw new BusinessException(orderType + "不存在");
        }
    }

    /**
     * 统一处理数量符号
     * 入库为正数，出库为负数
     */
    private void normalizeQuantitySign(StockTransactionTypeEnum transactionType, List<StockTransactionItem> items) {
        int quantitySign = transactionType.getQuantitySign(); // 入库+1，出库-1
        
        items.forEach(item -> {
            // 确保数量为绝对值，然后应用符号
            double absoluteQuantity = Math.abs(item.getQuantity());
            item.setQuantity(absoluteQuantity * quantitySign);
        });
        
        log.info("处理{}条明细的数量符号，操作类型：{}，符号：{}", 
            items.size(), transactionType.getDescription(), quantitySign > 0 ? "正数" : "负数");
    }

    /**
     * 构建通用的库存流水主表基础信息
     * 从明细列表自动计算总数量和明细条数，实现完全的自动化构建
     */
    private StockTransaction buildBaseTransaction(
            String transactionNo, 
            StockTransactionTypeEnum transactionType,
            StockTransactionRelatedTypeEnum relatedType,
            Long relatedId,
            String relatedNo,
            Long operatorId,
            String operatorName,
            List<StockTransactionItem> items,
            String remark) {
        
        // 从已处理数量符号的明细中自动计算总数量
        double totalQuantity = items.stream()
                .mapToDouble(StockTransactionItem::getQuantity)
                .sum();
        
        return StockTransaction.builder()
                .transactionNo(transactionNo)
                .transactionType(transactionType.getCode())
                .transactionDate(LocalDate.now())
                .relatedType(relatedType.getCode())
                .relatedId(relatedId)
                .relatedNo(relatedNo)
                .operatorId(operatorId)
                .operatorName(operatorName)
                .totalQuantity(totalQuantity)
                .plannedQuantity(totalQuantity)
                .remainingQuantity(totalQuantity)
                .itemCount(items.size())
                .processStatus(ProcessStatusEnum.PENDING.getCode())
                .auditStatus(AuditStatusEnum.PENDING.getCode())
                .processedQuantity(0.0)
                .createTime(LocalDateTime.now())
                .remark(remark)
                .build();
    }
    
    /**
     * 构建通用的库存流水明细基础信息
     */
    private StockTransactionItem buildBaseTransactionItem(
            String transactionNo,
            Long warehouseId,
            String warehouseName,
            Long skuId,
            String skuCode,
            String specSummary,
            Long productId,
            String productCode,
            String productName,
            String unit,
            Double unitCost,
            Double quantity,
            String remark) {
        
        return StockTransactionItem.builder()
                .transactionNo(transactionNo)
                .warehouseId(warehouseId)
                .warehouseName(warehouseName)
                .skuId(skuId)
                .skuCode(skuCode)
                .specSummary(specSummary)
                .productId(productId)
                .productCode(productCode)
                .productName(productName)
                .unit(unit)
                .unitCost(unitCost)
                .quantity(quantity)
                .totalAmount(0.0)
                .beforeStock(0.0)
                .afterStock(quantity)
                .remark(remark)
                .deletedFlag(false)
                .createTime(LocalDateTime.now())
                .build();
    }

}