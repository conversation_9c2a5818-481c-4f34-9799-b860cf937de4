package com.anxys.erp.stockTransaction.service;

import com.anxys.erp.stock.service.StockService;
import com.anxys.erp.stockTransaction.dao.StockTransactionMapper;
import com.anxys.erp.stockTransaction.domain.StockTransaction;
import com.anxys.erp.stockTransaction.domain.excel.StockTransactionExcel;
import com.anxys.erp.stockTransaction.domain.form.*;
import com.anxys.erp.stockTransaction.domain.vo.StockTransactionVO;
import com.anxys.erp.stockTransaction.enums.AuditStatusEnum;
import com.anxys.erp.stockTransaction.enums.StockTransactionTypeEnum;
import com.anxys.erp.stockTransactionItem.dao.StockTransactionItemMapper;
import com.anxys.erp.stockTransactionItem.domain.StockTransactionItem;
import com.anxys.erp.stockTransactionItem.domain.form.StockTransactionItemAddParam;
import com.anxys.erp.stockTransactionItem.domain.form.StockTransactionItemUpdateParam;
import com.anxys.erp.stockTransactionItem.service.StockTransactionItemService;
import com.anxys.erp.warehouse.service.WarehouseService;
import com.anxys.framework.common.code.UserErrorCode;
import com.anxys.framework.common.domain.PageResult;
import com.anxys.framework.common.exception.BusinessException;
import com.anxys.framework.common.utils.BeanUtil;
import com.anxys.framework.common.utils.PageUtil;
import com.anxys.framework.common.utils.object.BeanUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

import static com.anxys.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.anxys.framework.common.utils.collection.CollectionUtils.convertSet;

/**
 * 库存流水服务层
 *
 * @since 2025-06-09 16:59:22
 */
@Slf4j
@Service
@AllArgsConstructor
public class StockTransactionService {

    private final StockTransactionMapper stockTransactionMapper;

    private final WarehouseService warehouseService;

    private final StockTransactionItemMapper stockTransactionItemMapper;

    private final StockService stockService;

    private final StockTransactionItemService stockTransactionItemService;

    private final StockTransactionCreator stockTransactionCreator;

    /**
     * 分页查询
     *
     * @param queryParam 查询参数
     * @return 分页结果
     */
    public PageResult<StockTransactionVO> page(StockTransactionPageParam queryParam) {
        Page<StockTransactionVO> page = PageUtil.convert2PageQuery(queryParam.getPageParam());
        List<StockTransactionVO> PageVo = stockTransactionMapper.page(page, queryParam);
        return PageUtil.convert2PageResult(page, PageVo);
    }

    /**
     * 列表查询
     *
     * @param queryParam 查询参数
     * @return 列表结果
     */
    public List<StockTransactionVO> list(StockTransactionQueryParam queryParam) {
        return stockTransactionMapper.list(queryParam);
    }

    /**
     * 添加
     *
     * @param addParam 添加参数
     * @return 新创建的对象VO
     */
    @Transactional(rollbackFor = Exception.class)
    public StockTransactionVO insert(StockTransactionAddParam addParam) {
        StockTransaction stockTransaction = BeanUtil.copy(addParam, StockTransaction.class);
        stockTransactionMapper.insert(stockTransaction);
        // 返回新创建的对象
        return BeanUtil.copy(stockTransaction, StockTransactionVO.class);
    }

    /**
     * 更新
     *
     * @param updateParam 更新参数
     */
    @Transactional(rollbackFor = Exception.class)
    public void update(StockTransactionUpdateParam updateParam) {
        StockTransaction stockTransaction = BeanUtil.copy(updateParam, StockTransaction.class);
        stockTransactionMapper.updateById(stockTransaction);
    }

    /**
     * 批量删除
     *
     * @param idList ID列表
     */
    @Transactional(rollbackFor = Exception.class)
    public void batchDelete(List<Long> idList) {
        if (!idList.isEmpty()) {
            stockTransactionMapper.deleteByIds(idList, true);
        }
    }

    /**
     * 单个删除
     *
     * @param id ID
     */
    @Transactional(rollbackFor = Exception.class)
    public void delete(Long id) {
        stockTransactionMapper.deleteById(id);
    }

    /**
     * 通过ID查询
     *
     * @param id ID
     * @return 详情
     */
    public StockTransactionVO queryById(Long id) {
        StockTransaction stockTransaction = stockTransactionMapper.selectById(id);
        if (stockTransaction == null) {
            throw new BusinessException(UserErrorCode.DATA_NOT_EXIST);
        }
        return BeanUtil.copy(stockTransaction, StockTransactionVO.class);
    }

    /**
     * 导出Excel
     *
     * @param queryParam 查询参数
     * @return Excel数据列表
     */
    public List<StockTransactionExcel> exportExcel(StockTransactionQueryParam queryParam) {
        return stockTransactionMapper.exportExcel(queryParam);
    }

    /**
     * 库存流水更新
     *
     * @since 2025-06-11 15:26:15
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateTransaction(StockTransactionSaveParam updateParam) {

        if (updateParam.getStockTransaction().getId() == null) {
            throw new BusinessException("流水单id不能为空");
        }
        // 1.1 校验库存流水项的有效性
        List<StockTransactionItemAddParam> stockItems = validateStockInItems(updateParam.getItems());

        // 2.2 重新创建流水记录
        StockTransactionUpdateParam stockTransaction = new StockTransactionUpdateParam();
        BeanUtils.copyProperties(updateParam.getStockTransaction(), stockTransaction);
        calculateTotal(stockItems, stockTransaction);

        // 2.3 更新主表
        update(stockTransaction);

        // 2.4 更新具体流水
        stockItems.forEach(item -> {
            StockTransactionItemUpdateParam stockTransactionItem = BeanUtil.copy(item, StockTransactionItemUpdateParam.class);
            stockTransactionItemService.update(stockTransactionItem);
        });

    }

    /**
     * 库存流水审核状态更新
     *
     * @param id        库存流水ID
     * @param newStatus 新的审核状态
     * @since 2025-06-11 15:26:15
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateAuditStatus(Long id, String newStatus) {
        // 1. 使用枚举验证审核状态合法性
        AuditStatusEnum.validateAuditStatus(newStatus);

        // 2. 校验库存流水记录存在
        StockTransaction stockTransaction = validateStockExists(id);
        String currentStatus = stockTransaction.getAuditStatus();

        // 3. 使用枚举验证状态转换合法性
        AuditStatusEnum.validateStatusTransition(currentStatus, newStatus);

        // 4. 更新状态
        stockTransaction.setAuditStatus(newStatus);
        int updateCount = stockTransactionMapper.updateById(stockTransaction);
        if (updateCount == 0) {
            throw exception("审核失败");
        }

        // 5. 记录日志
        log.info("库存流水[{}]审核状态从[{}]更新为[{}]", stockTransaction.getTransactionNo(), currentStatus, newStatus);

        // 6. 根据业务需求实现库存变更逻辑
        if ("APPROVED".equals(newStatus)) {
            // 审核通过时，更新实际库存
            updateStockByTransaction(stockTransaction);
        }
    }

    /**
     * 根据库存流水更新实际库存
     *
     * @param stockTransaction 库存流水主表信息
     */
    @Transactional(rollbackFor = Exception.class)
    protected void updateStockByTransaction(StockTransaction stockTransaction) {
        // 查询该流水单的所有明细
        List<StockTransactionItem> items = stockTransactionItemMapper.selectByTransactionId(stockTransaction.getId());

        if (CollectionUtils.isEmpty(items)) {
            log.warn("流水单[{}]没有找到明细数据", stockTransaction.getTransactionNo());
            return;
        }

        // 1. 获取交易类型（用于日志和检查）
        StockTransactionTypeEnum transactionTypeEnum;
        try {
            transactionTypeEnum = StockTransactionTypeEnum.fromCode(stockTransaction.getTransactionType());
        } catch (IllegalArgumentException e) {
            log.error("未知的库存流水类型: {}", stockTransaction.getTransactionType());
            throw new BusinessException("未知的库存流水类型: " + stockTransaction.getTransactionType());
        }
        
        String operationType = transactionTypeEnum.isInbound() ? "入库" : "出库";



        // 2. 对出库操作进行库存充足性检查
        if (transactionTypeEnum.isOutbound()) {
            List<String> insufficientItems = stockService.checkStockSufficiency(items, stockTransaction.getTransactionType());
            if (!insufficientItems.isEmpty()) {
                String errorMsg = "库存不足，无法完成出库操作：\n" + String.join("\n", insufficientItems);
                log.error("流水单[{}]库存充足性检查失败: {}", stockTransaction.getTransactionNo(), errorMsg);
                throw new BusinessException(errorMsg);
            }
        }

        log.info("开始处理流水单[{}]的库存更新，操作类型：{}，共{}条明细", stockTransaction.getTransactionNo(), operationType, items.size());

        // 4. 更新库存（数量符号已正确）
        for (StockTransactionItem item : items) {
            try {
                // 库存更新逻辑保持不变
                Double beforeStock = stockService.queryStockBySkuAndWarehouse(item.getSkuId(), item.getWarehouseId());
                item.setBeforeStock(beforeStock != null ? beforeStock : 0.0);

                Double afterStock = stockService.updateStockCountIncrement(item);

                item.setAfterStock(afterStock);
                stockTransactionItemMapper.updateById(item);

                log.info("{}成功 - SKU[{}] 仓库[{}] 变动数量[{}] 变动前库存[{}] 变动后库存[{}]", operationType, item.getSkuId(), item.getWarehouseId(), item.getQuantity(), item.getBeforeStock(), afterStock);

            } catch (Exception e) {
                log.error("{}失败 - SKU[{}] 仓库[{}] 变动数量[{}], 错误信息: {}", operationType, item.getSkuId(), item.getWarehouseId(), item.getQuantity(), e.getMessage());
                throw new BusinessException("库存更新失败: " + e.getMessage());
            }
        }

        log.info("流水单[{}]库存更新完成，{}操作共处理{}条明细", stockTransaction.getTransactionNo(), operationType, items.size());
    }

    private StockTransaction validateStockExists(Long id) {
        StockTransaction stockTransaction = stockTransactionMapper.selectById(id);
        if (stockTransaction == null) {
            throw exception("流水单不存在");
        }
        return stockTransaction;
    }

    // 统计明细总数量、总金额、明细条数
    private static void calculateTotal(List<StockTransactionItemAddParam> stockItems, StockTransaction stockTransaction) {
        double totalQuantity = stockItems.stream().mapToDouble(item -> item.getQuantity() != null ? item.getQuantity() : 0.0).sum();
        double totalAmount = 0.0; // 暂停使用
        int itemCount = stockItems.size();
        stockTransaction.setTotalQuantity(totalQuantity);
        stockTransaction.setTotalAmount(totalAmount);
        stockTransaction.setItemCount(itemCount);
    }

    private List<StockTransactionItemAddParam> validateStockInItems(List<StockTransactionItemAddParam> list) {
        if (CollectionUtils.isEmpty(list)) {
            throw new BusinessException("库存流水项列表不能为空");
        }
        // 1.2 校验仓库存在
        warehouseService.validWarehouseList(convertSet(list, StockTransactionItemAddParam::getWarehouseId));
        return list;
    }


    @Transactional(rollbackFor = Exception.class)
    public Long generatePurchaseStockInOrder(PurchaseStockInParam stockInParam) {
        return stockTransactionCreator.createStockTransaction(stockInParam);
    }

    @Transactional(rollbackFor = Exception.class)
    public Long generateSaleStockOutOrder(SalesStockOutParam stockOutParam) {
        return stockTransactionCreator.createStockTransaction(stockOutParam);
    }

    @Transactional(rollbackFor = Exception.class)
    public Long generateProductionStockInOrder(ProductionStockInParam stockInParam) {
        return stockTransactionCreator.createStockTransaction(stockInParam);
    }

    @Transactional(rollbackFor = Exception.class)
    public Long generateSaleReturnStockInOrder(SaleReturnStockInParam stockInParam) {
        return stockTransactionCreator.createStockTransaction(stockInParam);
    }

    @Transactional(rollbackFor = Exception.class)
    public Long generateStockCheckOrder(StockCheckInParam stockCheckInParam) {
        return stockTransactionCreator.createStockTransaction(stockCheckInParam);
    }
}

