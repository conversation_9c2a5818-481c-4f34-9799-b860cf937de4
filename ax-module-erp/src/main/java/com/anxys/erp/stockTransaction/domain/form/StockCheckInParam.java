package com.anxys.erp.stockTransaction.domain.form;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.List;

@Data
@AllArgsConstructor
public class StockCheckInParam {
    @Schema(description = "盘点单ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long stockCheckId;
    @Schema(description = "盘点单明细", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<StockCheckItem> stockCheckItems;

    @Data
    @AllArgsConstructor
    public static class StockCheckItem {
        @Schema(description = "盘点单明细ID", requiredMode = Schema.RequiredMode.REQUIRED)
        private Long itemId;
        @Schema(description = "盘点数量", requiredMode = Schema.RequiredMode.REQUIRED)
        private Double quantity;
        
    }

}


