package com.anxys.erp.stockTransaction.service;

import com.anxys.erp.stockTransaction.dao.StockTransactionMapper;
import com.anxys.erp.stockTransaction.domain.StockTransaction;
import com.anxys.erp.stockTransactionItem.dao.StockTransactionItemMapper;
import com.anxys.erp.stockTransactionItem.domain.StockTransactionItem;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;


/**
 * 库存流水构建
 * 
 * <AUTHOR>
 * @since 2025-01-23
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class StockTransactionBuilderHelper {
    
    private final StockTransactionMapper stockTransactionMapper;
    private final StockTransactionItemMapper stockTransactionItemMapper;
    
    /**
     * 统一保存库存流水和明细
     * 
     * @param transaction 主表数据
     * @param items 明细列表
     * @return 创建的流水单ID
     */
    @Transactional(rollbackFor = Exception.class)
    public Long saveTransactionWithItems(StockTransaction transaction, List<StockTransactionItem> items) {
        // 1. 保存主表
        stockTransactionMapper.insert(transaction);
        
        // 2. 设置明细的transaction_id
        items.forEach(item -> item.setTransactionId(transaction.getId()));
        
        // 3. 批量保存明细
        if (!items.isEmpty()) {
            stockTransactionItemMapper.batchInsert(items);
        }
        
        log.info("成功保存库存流水：单号[{}] 明细[{}]条", 
                transaction.getTransactionNo(), items.size());
        
        return transaction.getId();
    }
    

} 