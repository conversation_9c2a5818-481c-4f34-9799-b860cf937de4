import type { BaseModel } from '@/api/base-model/base-model'
import type { PageParam, PageQueryParam, PageResult } from '@/api/base-model/page-model'
import type { ResponseModel } from '@/api/base-model/response-model'
import type { FormExpose } from '@/components/base/Form/type'
import type { Key } from './interface/Common'
import type { Table, TableColumn, TableQueryParam } from './interface/Table'
import { sentry } from '@/lib/sentry'
import { confirm, message } from '@/utils/message-util'
import { reactive, ref, toRaw } from 'vue'
import { ActionExecutor } from './utils/ActionExecutor'

/**
 * 表格处理器类
 * 负责处理表格相关的操作和状态管理
 */
export class TableService<
  Result extends BaseModel,
  QueryParam extends PageQueryParam,
> implements Table<Result, QueryParam> {
  /**
   * 行唯一标识，可被子类重写
   */
  get rowKey(): string {
    return 'id'
  }

  /**
   * 表格滚动配置，可被子类重写
   */
  get scroll(): object {
    return { x: 1300 }
  }

  /**
   * 表格列配置，必须被子类重写
   */
  get columns(): TableColumn<Result>[] {
    if (this._columns.value.length === 0) {
      console.warn('表格列未定义，请使用 setColumns 方法设置')
    }
    return this._columns.value
  }

  /**
   * 设置表格列配置
   */
  set columns(value: TableColumn<Result>[]) {
    this._columns.value = value
  }

  /**
   * 表格数据
   */
  private _tableData = shallowRef<Result[]>([])
  /**
   * 分页结果
   */
  private _pageResult = ref<PageResult<Result>>({ list: [], total: 0 })

  /**
   * 总条数
   */
  get total(): number {
    return this._pageResult.value?.total || 0
  }

  /**
   * 选中的行Key表格
   */
  private _selectedRowKeyList = ref<Key[]>([])
  /**
   * 选中的行对象表格
   */
  private _selectedRows = ref<Result[]>([])
  /**
   * 导入Modal是否可见
   */
  private _importModalOpen = ref<boolean>(false)
  /**
   * 上传的文件
   */
  private _uploadFile = ref<File | null>(null)

  /**
   * 查询参数 - 响应式对象
   */
  queryParam: TableQueryParam<QueryParam, Result>

  /**
   * 表单引用
   */
  tableFormRef = ref<FormExpose<Result>>()

  /**
   * 获取表格数据
   */
  get tableData(): Result[] {
    return this._tableData.value as Result[]
  }

  /**
   * 获取分页结果
   */
  get pageResult(): PageResult<Result> {
    return this._pageResult.value as PageResult<Result>
  }

  /**
   * 设置选中的行
   */
  set selectedRows(value: Result[]) {
    this._selectedRows.value = value
  }

  get selectedRows(): Result[] {
    return this._selectedRows.value as Result[]
  }

  /**
   * 设置选中的行Key
   */
  set selectedRowKeys(value: Key[]) {
    this._selectedRowKeyList.value = value
  }

  /**
   * 获取选中的行Key表格
   */
  get selectedRowKeys(): Key[] {
    return this._selectedRowKeyList.value
  }

  /**
   * 获取导入Modal是否可见
   */
  get importModalOpen(): boolean {
    return this._importModalOpen.value
  }

  /**
   * 获取上传的文件
   */
  get uploadFile(): File | null {
    return this._uploadFile.value
  }

  /**
   * 获取页面参数
   */
  get pageParam(): PageParam {
    return this.queryParam.pageParam
  }

  get hasUploadFile(): boolean {
    return !!this._uploadFile.value
  }

  /**
   * 构造函数
   * @param businessName 业务名称
   * @param _columns 表格列配置
   * @param api API对象
   * @param api.queryPage 查询分页数据
   * @param api.delete 删除数据
   * @param api.batchDelete 批量删除数据
   * @param api.import 导入数据
   * @param api.export 导出数据
   * @param api.downloadTemplate 下载模板
   * @param initialQueryParam 初始查询参数
   */
  constructor(
    private businessName: string,
    private _columns = ref<TableColumn<Result>[]>([]),
    private api: {
      queryPage: ((param: QueryParam) => Promise<ResponseModel<PageResult<Result>>>) | undefined
      delete?: ((id: number) => Promise<ResponseModel<string>>) | undefined
      batchDelete?: ((ids: number[]) => Promise<ResponseModel<string>>) | undefined
      import?: ((formData: FormData) => Promise<ResponseModel<string>>) | undefined
      export?: ((param: QueryParam) => void) | undefined
      downloadTemplate?: (() => void) | undefined
    },
    initialQueryParam: Partial<Result> = {},
  ) {
    // 创建初始查询参数
    this.queryParam = this.createDefaultQueryParam(initialQueryParam)
    // 自动加载数据
    this.queryPage()
  }

  /**
   * 获取表格数据的非响应式版本
   */
  getRawTableData = (): Result[] => {
    return toRaw(this._tableData.value) as Result[]
  }

  updateQueryParam = (queryParam: Partial<QueryParam>): void => {
    // 使用 Object.assign 保持响应式，只更新 Result 部分的字段
    Object.assign(this.queryParam, queryParam)
  }

  /**
   * 重置查询条件
   */
  resetQuery = (): void => {
    // 保留当前页大小
    const pageSize = this.queryParam.pageParam.pageSize
    // 创建新的默认参数
    const defaultParam = this.createDefaultQueryParam({})
    // 清空当前 queryParam 的所有属性
    Object.keys(this.queryParam).forEach((key) => {
      delete this.queryParam[key as keyof typeof this.queryParam]
    })
    // 重新赋值，保持响应式
    Object.assign(this.queryParam, defaultParam)
    // 恢复页大小
    this.queryParam.pageParam.pageSize = pageSize
    // 重置为第一页
    this.queryParam.pageParam.pageNum = 1
    // 查询数据
    this.queryPage()
  }

  /**
   * 搜索
   */
  onSearch = (): void => {
    this.queryParam.pageParam.pageNum = 1
    this.queryPage()
  }

  /**
   * 查询分页数据
   */
  async queryPage(): Promise<void> {
    if (!this.api.queryPage) {
      message.error(`${this.businessName}服务缺少查询API`)
      return
    }

    await ActionExecutor.execute(
      async () => {
        const queryResult = await this.api.queryPage!(this.queryParam as QueryParam)
        if (queryResult.data) {
          this._pageResult.value = queryResult.data
          this._tableData.value = queryResult.data.list || []
        }
      },
      `获取${this.businessName}表格失败`,
    )
  }

  /**
   * 选择行变化
   */
  onSelectChange = (selectedRows: Result[]): void => {
    this._selectedRows.value = selectedRows
    this._selectedRowKeyList.value = selectedRows.map(row => row.id as Key)
  }

  onSelectChangeByKey = (selectedRowKeys: Key[]): void => {
    this._selectedRowKeyList.value = selectedRowKeys
    this._selectedRows.value = this.tableData.filter(row => selectedRowKeys.includes(row.id as Key))
  }

  /**
   * 分页变化处理
   */
  onPageChange = (page: number, pageSize?: number): void => {
    this.queryParam.pageParam.pageNum = page
    if (pageSize) {
      this.queryParam.pageParam.pageSize = pageSize
    }
    this.queryPage()
  }

  /**
   * 删除实体
   */
  deleteEntity = (id: Key, confirmMessage?: string): void => {
    if (!id) {
      message.error(`记录缺少ID`)
      return
    }

    if (!this.api.delete) {
      message.error(`缺少API，无法删除${this.businessName}`)
      return
    }

    // 如果没有传入确认消息，则根据业务名称生成
    const confirmText = confirmMessage || `确定要删除该${this.businessName}数据吗？`

    confirm.delete(confirmText, async () => {
      await this.singleDelete(id)
    })
  }

  /**
   * 单个删除实体
   */
  private async singleDelete(id: Key): Promise<void> {
    if (!id || !this.api.delete) {
      return
    }

    // 确保record.id不为undefined
    const entityId = id

    await ActionExecutor.execute(
      async () => {
        await this.api.delete!(entityId as number)
        message.success(`删除${this.businessName}成功`)
        this.queryPage()
      },
      `删除${this.businessName}失败`,
    )
  }

  /**
   * 确认批量删除实体
   */
  batchDeleteEntities = (confirmMessageOrEvent?: string | Event): void => {
    if (this._selectedRowKeyList.value.length === 0) {
      message.warning('请选择要删除的数据')
      return
    }

    if (!this.api.batchDelete) {
      message.error(`${this.businessName}服务缺少批量删除API`)
      return
    }
    // 如果没有传参数或传入的是事件对象，则自动生成确认消息
    const confirmMessage = typeof confirmMessageOrEvent === 'string'
      ? confirmMessageOrEvent
      : `确定要删除选中的 ${this._selectedRowKeyList.value.length} 条${this.businessName}数据吗？`

    confirm.delete(confirmMessage, async () => {
      await this.batchDelete()
    })
  }

  /**
   * 批量删除实体
   */
  private async batchDelete(): Promise<void> {
    if (this._selectedRowKeyList.value.length === 0 || !this.api.batchDelete) {
      return
    }

    await ActionExecutor.execute(
      async () => {
        await this.api.batchDelete!(this._selectedRowKeyList.value as number[])
        message.success(`批量删除${this.businessName}成功`)
        this._selectedRowKeyList.value = []
        this.queryPage()
      },
      `批量删除${this.businessName}失败`,
    )
  }

  /**
   * 显示导入模态框
   */
  showImportModal = (): void => {
    if (!this.api.import) {
      message.error(`${this.businessName}服务缺少导入API`)
      return
    }
    this._importModalOpen.value = true
    this._uploadFile.value = null
  }

  /**
   * 上传文件
   */
  handleImportFile = (file: File): void => {
    this._uploadFile.value = file
  }

  /**
   * 验证上传文件
   * @param file 待验证的文件
   * @returns 文件是否有效
   */
  validateUploadFile = (file: File): boolean => {
    // 接受的文件类型
    const accept = '.xls,.xlsx'
    // 文件大小限制(MB)
    const maxSize = 10

    const isAcceptType = accept.split(',').some((type) => {
      return file.name.toLowerCase().endsWith(type.replace('.', '').toLowerCase())
    })

    if (!isAcceptType) {
      message.error(`只能上传${accept}格式的文件!`)
      return false
    }

    const isLtMaxSize = file.size / 1024 / 1024 < maxSize
    if (!isLtMaxSize) {
      message.error(`文件大小不能超过${maxSize}MB!`)
      return false
    }

    return true
  }

  /**
   * 上传前检查（用于上传组件的beforeUpload属性）
   * @param file 待上传的文件
   * @returns 是否继续上传
   */
  beforeUpload = (file: File): boolean => {
    // 使用验证方法验证文件
    if (!this.validateUploadFile(file)) {
      return false
    }

    // 验证通过，处理文件
    this.handleImportFile(file)
    return false // 阻止组件默认上传行为
  }

  /**
   * 导入数据
   */
  async onImport(eventOrFile?: MouseEvent | File): Promise<void> {
    if (!this.api.import) {
      message.error(`${this.businessName}服务缺少导入API`)
      return
    }

    // 判断参数类型，忽略 MouseEvent
    let fileToUse: File | null

    if (eventOrFile instanceof File) {
      fileToUse = eventOrFile
    }
    else {
      fileToUse = this._uploadFile.value
    }

    if (!fileToUse) {
      message.warning('请选择要导入的文件')
      return
    }

    const formData = new FormData()
    formData.append('file', fileToUse)

    await ActionExecutor.execute(
      async () => {
        await this.api.import!(formData)
        message.success(`导入${this.businessName}成功`)
        this._importModalOpen.value = false
        await this.queryPage()
      },
      `导入${this.businessName}失败`,
    )
  }

  /**
   * 设置导入模态框显示状态
   * @param value 是否显示
   */
  setImportModalOpen = (value: boolean): void => {
    this._importModalOpen.value = value
  }

  /**
   * 下载模板
   */
  downloadTemplate = (): void => {
    if (!this.api.downloadTemplate) {
      message.error(`${this.businessName}服务缺少下载模板API`)
      return
    }

    try {
      this.api.downloadTemplate()
      message.success('正在下载模板')
    }
    catch (error) {
      sentry.captureError(error)
      message.error('下载模板失败')
    }
  }

  /**
   * 导出数据
   */
  onExport = async (): Promise<void> => {
    if (!this.api.export) {
      message.error(`${this.businessName}服务缺少导出API`)
      return
    }

    await ActionExecutor.execute(
      async () => {
        this.api.export!(this.queryParam as QueryParam)
        message.success(`导出${this.businessName}成功`)
      },
      `导出${this.businessName}失败`,
    )
  }

  /**
   * 创建默认查询参数
   */
  private createDefaultQueryParam(initialQueryParam: Partial<Result>): TableQueryParam<QueryParam, Result> {
    // 创建默认的 pageParam
    const defaultPageParam: PageParam = {
      pageNum: 1,
      pageSize: 10,
    }

    // 构建完整的查询参数
    const queryParam = reactive({
      pageParam: defaultPageParam,
      ...initialQueryParam,
    }) as TableQueryParam<QueryParam, Result>

    // 返回响应式对象
    return queryParam
  }
}
