<script lang="ts" setup>
import type { StockTransactionResult } from '@/api/business/stock-transaction/model/stock-transaction-types'
// 只导入同级目录组件
import StockTransactionDetail from './components/StockTransactionDetail.vue'
import StockTransactionForm from './components/StockTransactionForm.vue'
import { stockTransactionService } from './service/StockTransactionService'

// 提供 service
stockTransactionService.provide()

// 审核处理逻辑
function handleAudit(record: StockTransactionResult) {
  // 这里可以根据需要添加审核弹窗或直接调用审核API
  // 示例：直接审核通过
  stockTransactionService.auditStockTransaction(record.id!, 'APPROVED', '系统自动审核')
}

// 拒绝处理逻辑
function handleReject(record: StockTransactionResult) {
  // 拒绝审核
  stockTransactionService.auditStockTransaction(record.id!, 'REJECTED', '系统拒绝审核')
}
</script>

<template>
  <!---------- 查询参数 begin ----------->
  <Card>
    <Form :ref="stockTransactionService.tableFormRef" v-privilege="'stock-transaction:query'">
      <Grid>
        <GridCol>
          <FormItem label="流水单号" name="transactionNo">
            <InputText allow-clear />
          </FormItem>
        </GridCol>
        <GridCol>
          <FormItem label="流水类型" name="transactionType">
            <DictSelect
              key-code="stock_transaction_type"
              placeholder="请选择流水类型"
            />
          </FormItem>
        </GridCol>
        <GridCol>
          <FormItem label="关联单据类型" name="relatedType">
            <DictSelect
              key-code="related_doc_type"
              placeholder="请选择关联单据类型"
            />
          </FormItem>
        </GridCol>
        <GridCol>
          <FormItem label="关联单据号" name="relatedNo">
            <InputText allow-clear />
          </FormItem>
        </GridCol>
        <GridCol>
          <FormItem label="操作人" name="operatorName">
            <InputText allow-clear />
          </FormItem>
        </GridCol>
        <GridCol>
          <FormItem label="审核状态" name="auditStatus">
            <DictSelect
              key-code="audit_status"
              placeholder="请选择审核状态"
            />
          </FormItem>
        </GridCol>
        <GridCol>
          <FormItem label="流水备注" name="remark">
            <InputText allow-clear />
          </FormItem>
        </GridCol>
        <GridCol>
          <FormItem label="流水日期" name="transactionDateRange">
            <DateRangePicker />
          </FormItem>
        </GridCol>
        <GridCol>
          <FormItem label="创建时间" name="createTimeRange">
            <DateRangePicker />
          </FormItem>
        </GridCol>
      </Grid>
    </Form>
  </Card>
  <!---------- 查询参数 end ----------->

  <Card :hoverable="true">
    <!---------- 表格操作行 begin ----------->
    <FlexRow>
      <FlexRow>
        <AxTableOperateButtons
          add-config="stock-transaction:add"
          batch-delete-config="stock-transaction:delete"
          import-config="stock-transaction:import"
          export-config="stock-transaction:export"
        />
      </FlexRow>
      <ButtonGroup>
        <IconButton label="查询" icon-type="SearchOutlined" type="primary" @click="stockTransactionService.onSearch" />
        <IconButton label="重置" icon-type="ReloadOutlined" @click="stockTransactionService.resetQuery" />
      </ButtonGroup>
    </FlexRow>
    <!---------- 表格操作行 end ----------->
    <br>
    <!-- 使用组件化的表格 -->
    <AxTable min-width="180rem">
      <template #bodyCell="{ column, record }">
        <!-- 只为操作列添加模板插槽 -->
        <template v-if="column.dataIndex === 'action'">
          <FlexRow justify="start">
            <IconAnchor
              v-privilege="'stock-transaction:view'"
              label="查看"
              icon-type="EyeOutlined"
              @click="stockTransactionService.openDetailView(record.id!)"
            />
            <IconAnchor
              v-privilege="'stock-transaction:edit'"
              label="编辑"
              icon-type="EditOutlined"
              @click="stockTransactionService.openEditForm(record.id!)"
            />
            <IconAnchor
              v-privilege="'stock-transaction:audit'"
              label="审核"
              icon-type="CheckOutlined"
              color="blue"
              @click="handleAudit(record)"
            />
            <IconAnchor
              v-privilege="'stock-transaction:reject'"
              label="拒绝"
              icon-type="CloseOutlined"
              color="orange"
              @click="handleReject(record)"
            />
            <IconAnchor
              v-privilege="'stock-transaction:delete'"
              label="删除"
              icon-type="DeleteOutlined"
              color="red"
              @click="stockTransactionService.deleteEntity(record.id!)"
            />
          </FlexRow>
        </template>
      </template>
    </AxTable>
  </Card>

  <!-- 导入弹窗 -->
  <AxImportExcelModal />
  <!-- 库存流水单表单 -->
  <StockTransactionForm />
  <!-- 库存流水单详情 -->
  <StockTransactionDetail />
</template>
