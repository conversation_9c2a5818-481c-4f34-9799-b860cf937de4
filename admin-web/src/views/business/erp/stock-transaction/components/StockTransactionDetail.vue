<script lang="ts" setup>
import type { PageParam } from '@/api/base-model/page-model'
import type { StockTransactionItemResult } from '@/api/business/stock-transaction-item/model/stock-transaction-item-types'
import type { StockTransactionService } from '../service/StockTransactionService'
import { Button } from '@/components/base/Button'
import { CustomText as Text } from '@/components/base/CustomText'
import { Descriptions, DescriptionsItem } from '@/components/base/Descriptions'
import { DictTag } from '@/components/utils/Dict'
import { CRUD_KEY } from '@/service/composite/TableCrudService'
import { formatDateTime } from '@/utils/format'
import {
  stockTransactionItemService,
} from '@/views/business/erp/stock-transaction-item/service/StockTransactionItemService'
import { computed, inject, reactive, ref, watch } from 'vue'
import { formatCurrency, formatStock, getAuditStatusColor, getTransactionTypeColor } from '../columns'

// 获取服务实例
const stockTransactionService = inject<StockTransactionService>(CRUD_KEY)!

// Tab状态
const activeTab = ref(0)

// 计算Tab标题显示
const basicInfoTabTitle = computed(() => {
  return '基本信息'
})

const itemListTabTitle = computed(() => {
  const count = stockTransactionService.detailData.itemCount || 0
  return count > 0 ? `明细项列表 (${count}条)` : '明细项列表'
})

// 明细项数据状态
const itemData = ref<StockTransactionItemResult[]>([])
const itemLoading = ref(false)
const itemError = ref<string>('')
const itemTotal = ref(0)

// 分页参数
const pagination = reactive({
  current: 1,
  pageSize: 20,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number, range: [number, number]) => `共 ${total} 条记录，当前显示 ${range[0]}-${range[1]} 条`,
  pageSizeOptions: ['10', '20', '50', '100'],
})

// 是否使用分页（根据数据量动态决定）
const usePagination = computed(() => {
  return itemTotal.value > 20
})

// 加载明细项数据（支持分页）
async function loadItemData(page?: number, pageSize?: number) {
  if (!stockTransactionService.detailData.id)
    return

  // 更新分页参数
  if (page)
    pagination.current = page
  if (pageSize)
    pagination.pageSize = pageSize

  itemLoading.value = true
  itemError.value = ''

  try {
    let response

    if (usePagination.value) {
      // 使用分页查询
      const pageParam: PageParam = {
        pageNum: pagination.current,
        pageSize: pagination.pageSize,
      }
      response = await stockTransactionItemService.getItemsByTransactionId(
        stockTransactionService.detailData.id,
        pageParam,
      )

      if (response.success && response.data) {
        itemData.value = response.data.list || []
        itemTotal.value = response.data.total || 0
        pagination.current = response.data.pageNum || 1
        pagination.pageSize = response.data.pageSize || 20
      }
      else {
        itemError.value = response.msg || '分页查询明细项数据失败'
      }
    }
    else {
      // 不分页，获取全部数据
      response = await stockTransactionItemService.getAllItemsByTransactionId(
        stockTransactionService.detailData.id,
      )

      if (response.success) {
        itemData.value = response.data || []
        itemTotal.value = itemData.value.length
      }
      else {
        itemError.value = response.msg || '查询明细项数据失败'
      }
    }
  }
  catch (error: unknown) {
    console.error('加载明细项数据失败:', error)
    itemError.value = error instanceof Error ? error.message : '加载数据时发生未知错误'
  }
  finally {
    itemLoading.value = false
  }
}

// 分页变化处理
function handlePageChange(page: number, pageSize: number) {
  loadItemData(page, pageSize)
}

// 重新加载数据
function reloadItemData() {
  pagination.current = 1
  loadItemData()
}

// 当切换到明细项Tab时加载数据
function handleTabChange(event: { index: number }) {
  activeTab.value = event.index
  if (event.index === 1 && itemData.value.length === 0 && !itemError.value) {
    loadItemData()
  }
}

// 监听详情弹窗打开状态，重置Tab和数据
watch(() => stockTransactionService.detailOpen, (newValue) => {
  if (newValue) {
    // 弹窗打开时重置Tab状态
    activeTab.value = 0
    itemData.value = []
    itemError.value = ''
    itemTotal.value = 0
    pagination.current = 1
  }
})

// 计算表格的分页配置
const tablePageConfig = computed(() => {
  if (!usePagination.value) {
    return undefined
  }

  return {
    first: (pagination.current - 1) * pagination.pageSize,
    rows: pagination.pageSize,
    totalRecords: itemTotal.value,
    rowsPerPageOptions: [10, 20, 50, 100],
  }
})

// 分页事件处理
function onPage(event: { first: number, rows: number, page: number }) {
  const newPage = event.page + 1
  const newPageSize = event.rows
  handlePageChange(newPage, newPageSize)
}
</script>

<template>
  <Dialog
    v-model:visible="stockTransactionService.detailOpen"
    :header="stockTransactionService.detailTitle"
    :show-footer="false"
    class="w-[1200px]"
  >
    <div class="stock-transaction-detail min-h-96">
      <TabView
        v-model:active-index="activeTab"
        class="detail-tabs w-full"
        @tab-change="handleTabChange"
      >
        <!-- 基本信息Tab -->
        <TabPanel :header="basicInfoTabTitle">
          <div class="tab-content p-4 min-h-80">
            <Descriptions
              :column="2"
              :colon="false"
              bordered
              size="small"
              class="detail-descriptions bg-white rounded-lg"
            >
              <!-- 基本信息 -->
              <DescriptionsItem label="流水单号">
                <Text :value="stockTransactionService.detailData.transactionNo" />
              </DescriptionsItem>
              <DescriptionsItem label="流水类型">
                <DictTag
                  :color="getTransactionTypeColor(stockTransactionService.detailData.transactionType)"
                  key-code="stock_transaction_type"
                  :value-code="stockTransactionService.detailData.transactionType"
                />
              </DescriptionsItem>

              <DescriptionsItem label="流水日期">
                <Text :value="stockTransactionService.detailData.transactionDate" />
              </DescriptionsItem>
              <DescriptionsItem label="操作人">
                <Text :value="stockTransactionService.detailData.operatorName" />
              </DescriptionsItem>

              <!-- 关联信息 -->
              <DescriptionsItem label="关联单据类型">
                <DictTag
                  v-if="stockTransactionService.detailData.relatedType"
                  key-code="related_doc_type"
                  :value-code="stockTransactionService.detailData.relatedType"
                />
                <Text v-else value="--" />
              </DescriptionsItem>
              <DescriptionsItem label="关联单据号">
                <Text :value="stockTransactionService.detailData.relatedNo" />
              </DescriptionsItem>

              <!-- 数量信息 -->
              <DescriptionsItem label="总数量">
                <Text :value="stockTransactionService.detailData.totalQuantity" />
              </DescriptionsItem>
              <DescriptionsItem label="明细条数">
                <Text :value="stockTransactionService.detailData.itemCount" />
              </DescriptionsItem>
              <DescriptionsItem label="计划处理数量">
                <Text :value="stockTransactionService.detailData.plannedQuantity" />
              </DescriptionsItem>

              <DescriptionsItem label="已处理数量">
                <Text :value="stockTransactionService.detailData.processedQuantity" />
              </DescriptionsItem>
              <DescriptionsItem label="剩余未处理数量">
                <Text :value="stockTransactionService.detailData.remainingQuantity" />
              </DescriptionsItem>

              <!-- 状态信息 -->
              <DescriptionsItem label="审核状态">
                <DictTag
                  :color="getAuditStatusColor(stockTransactionService.detailData.auditStatus)"
                  key-code="audit_status"
                  :value-code="stockTransactionService.detailData.auditStatus"
                />
              </DescriptionsItem>
              <DescriptionsItem label="处理状态">
                <DictTag
                  v-if="stockTransactionService.detailData.processStatus"
                  key-code="process_status"
                  :value-code="stockTransactionService.detailData.processStatus"
                />
                <Text v-else value="--" />
              </DescriptionsItem>

              <!-- 时间信息 -->
              <DescriptionsItem label="创建时间">
                <Text :value="formatDateTime(stockTransactionService.detailData.createTime)" />
              </DescriptionsItem>
              <DescriptionsItem label="更新时间">
                <Text :value="formatDateTime(stockTransactionService.detailData.updateTime)" />
              </DescriptionsItem>

              <!-- 备注信息 -->
              <DescriptionsItem label="流水备注" :span="2">
                <Text :value="stockTransactionService.detailData.remark" />
              </DescriptionsItem>
            </Descriptions>
          </div>
        </TabPanel>

        <!-- 明细项列表Tab -->
        <TabPanel :header="itemListTabTitle">
          <div class="tab-content p-4 min-h-80">
            <!-- 错误状态 -->
            <div v-if="itemError && !itemLoading" class="error-state flex flex-col items-center justify-center py-16 text-center">
              <div class="error-icon text-6xl mb-4 opacity-50">
                ⚠️
              </div>
              <div class="error-text text-lg font-medium text-red-600 mb-2">
                {{ itemError }}
              </div>
              <Button severity="primary" size="small" :loading="itemLoading" @click="reloadItemData">
                重新加载
              </Button>
            </div>

            <!-- 空状态 -->
            <div v-else-if="itemData.length === 0 && !itemLoading && !itemError" class="empty-state flex flex-col items-center justify-center py-16 text-center">
              <div class="empty-icon text-6xl mb-4 opacity-50">
                📦
              </div>
              <div class="empty-text text-lg font-medium text-gray-600 mb-2">
                暂无明细项数据
              </div>
              <div class="empty-description text-sm text-gray-400">
                该流水单还没有明细项记录
              </div>
            </div>

            <!-- 数据状态 -->
            <div v-else class="item-table-container space-y-4">
              <!-- 数据统计信息 -->
              <div v-if="itemTotal > 0" class="data-stats flex items-center justify-between bg-gray-50 px-4 py-2 rounded-lg border">
                <span class="stats-text text-sm text-gray-600">
                  共找到 <span class="stats-number font-medium text-blue-600">{{ itemTotal }}</span> 条明细记录
                  <span v-if="usePagination" class="stats-pagination text-gray-500">
                    ，当前第 {{ pagination.current }} 页
                  </span>
                </span>
                <Button
                  size="small"
                  text
                  :loading="itemLoading"
                  @click="reloadItemData"
                >
                  刷新
                </Button>
              </div>

              <!-- 表格 -->
              <div class="item-table-spin min-h-48">
                <DataTable
                  :value="itemData"
                  :loading="itemLoading"
                  scroll-height="400px"
                  :striped-rows="true"
                  :show-gridlines="true"
                  class="item-detail-table shadow-sm rounded-lg overflow-hidden"
                >
                  <Column header="序号" style="width: 60px">
                    <template #body="{ index }">
                      <div class="text-center">
                        {{ index + 1 }}
                      </div>
                    </template>
                  </Column>

                  <Column header="仓库信息" style="width: 180px">
                    <template #body="{ data }">
                      <div class="flex flex-col gap-1">
                        <div class="font-medium text-gray-900">
                          {{ data.warehouseName || '--' }}
                        </div>
                        <div class="text-xs text-gray-500">
                          {{ data.warehouseCode || '--' }}
                        </div>
                      </div>
                    </template>
                  </Column>

                  <Column header="商品信息" style="width: 220px">
                    <template #body="{ data }">
                      <div class="flex flex-col gap-1">
                        <div class="font-medium text-gray-900">
                          {{ data.productName || '--' }}
                        </div>
                        <div class="text-xs text-gray-500">
                          {{ data.productCode || '--' }}
                        </div>
                      </div>
                    </template>
                  </Column>

                  <Column header="SKU规格" style="width: 180px">
                    <template #body="{ data }">
                      <div class="flex flex-col gap-1">
                        <div class="text-sm text-gray-900">
                          {{ data.specSummary || '--' }}
                        </div>
                        <div class="text-xs text-gray-500">
                          {{ data.skuCode || '--' }}
                        </div>
                      </div>
                    </template>
                  </Column>

                  <Column header="变动数量" style="width: 120px">
                    <template #body="{ data }">
                      <div class="flex flex-col items-center gap-1">
                        {{ data.quantity }}
                        <div class="text-xs text-gray-500">
                          {{ data.unit || '--' }}
                        </div>
                      </div>
                    </template>
                  </Column>

                  <Column header="成本信息" style="width: 140px">
                    <template #body="{ data }">
                      <div class="flex flex-col gap-1 text-right">
                        <div class="font-medium text-gray-900">
                          {{ formatCurrency(data.unitCost, 4) }}
                        </div>
                        <div class="text-xs text-gray-500">
                          {{ data.totalAmount ? formatCurrency(data.totalAmount) : '--' }}
                        </div>
                      </div>
                    </template>
                  </Column>

                  <Column header="库存变动" style="width: 140px">
                    <template #body="{ data }">
                      <div class="flex flex-col gap-1 text-center">
                        <div class="text-sm text-gray-900">
                          {{ formatStock(data.beforeStock) }} → {{ formatStock(data.afterStock) }}
                        </div>
                        <div class="text-xs text-gray-500">
                          变动前 → 变动后
                        </div>
                      </div>
                    </template>
                  </Column>

                  <Column header="备注" style="width: 150px">
                    <template #body="{ data }">
                      <div class="text-sm text-gray-600" :title="data.remark || '--'">
                        {{ data.remark || '--' }}
                      </div>
                    </template>
                  </Column>
                </DataTable>

                <!-- 分页器 -->
                <Paginator
                  v-if="tablePageConfig"
                  :first="tablePageConfig.first"
                  :rows="tablePageConfig.rows"
                  :total-records="tablePageConfig.totalRecords"
                  :rows-per-page-options="tablePageConfig.rowsPerPageOptions"
                  class="mt-4"
                  @page="onPage"
                />
              </div>
            </div>
          </div>
        </TabPanel>
      </TabView>
    </div>
  </Dialog>
</template>
