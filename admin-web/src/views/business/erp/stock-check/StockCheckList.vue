<script lang="ts" setup>
import StockCheckDetail from './components/StockCheckDetail.vue'
import StockCheckForm from './components/StockCheckForm.vue'
import { stockCheckService } from './service/StockCheckService'

stockCheckService.provide()
</script>

<template>
  <!---------- 查询参数 begin ----------->
  <Card>
    <Form v-privilege="'stockCheck:query'" layout="vertical">
      <Grid>
        <GridCol>
          <FormItem label="盘点单号">
            <InputText v-model="stockCheckService.queryParam.checkNo" placeholder="请输入盘点单号" />
          </FormItem>
        </GridCol>
        <GridCol>
          <FormItem label="仓库名称">
            <InputText v-model="stockCheckService.queryParam.warehouseName" placeholder="请输入仓库名称" />
          </FormItem>
        </GridCol>
        <GridCol>
          <FormItem label="盘点状态">
            <DictSelect v-model="stockCheckService.queryParam.checkStatus" key-code="check_status" placeholder="请选择盘点状态" />
          </FormItem>
        </GridCol>
        <GridCol>
          <FormItem label="创建时间">
            <DateRangePicker v-model:from="stockCheckService.queryParam.createTimeFrom" v-model:to="stockCheckService.queryParam.createTimeTo" />
          </FormItem>
        </GridCol>
      </Grid>
    </Form>
  </Card>
  <!---------- 查询参数 end ----------->
  <Card :hoverable="true">
    <!---------- 表格操作行 begin ----------->
    <FlexRow>
      <AxTableOperateButtons add-config="stockCheck:add" batch-delete-config="stockCheck:delete" import-config="stockCheck:import" export-config="stockCheck:export" />
      <ButtonGroup>
        <IconButton label="查询" icon-type="SearchOutlined" type="primary" @click="stockCheckService.onSearch" />
        <IconButton label="重置" icon-type="ReloadOutlined" @click="stockCheckService.resetQuery" />
      </ButtonGroup>
    </FlexRow>
    <!---------- 表格操作行 end ----------->
    <br>
    <AxTable>
      <template #bodyCell="{ column, record }">
        <!-- 只为操作列添加模板插槽 -->
        <template v-if="column.dataIndex === 'action'">
          <FlexRow justify="start">
            <IconAnchor
              v-privilege="'stockCheck:view'" label="查看" icon-type="EyeOutlined"
              @click="stockCheckService.openDetailView(record.id!)"
            />
            <IconAnchor
              v-privilege="'stockCheck:edit'" label="编辑" icon-type="EditOutlined"
              @click="stockCheckService.openEditForm(record.id!)"
            />
            <IconAnchor
              v-privilege="'stockCheck:delete'" label="删除" icon-type="DeleteOutlined" color="red"
              @click="stockCheckService.deleteEntity(record.id!)"
            />
          </FlexRow>
        </template>
      </template>
    </AxTable>
  </Card>
  <!-- 导入弹窗 -->
  <AxImportExcelModal />
  <!-- 库存盘点表单 -->
  <StockCheckForm />
  <!-- 库存盘点详情 -->
  <StockCheckDetail />
</template>
