<script setup lang="ts">
import BrandDetail from './components/BrandDetail.vue'
import BrandForm from './components/BrandForm.vue'
import { brandService } from './service/BrandService'

brandService.provide()
</script>

<template>
  <!---------- 查询参数 begin ----------->
  <Card>
    <Form :ref="brandService.tableFormRef" v-privilege="'brand:query'">
      <Grid>
        <GridCol>
          <FormItem label="品牌编码" name="brandCode">
            <InputText allow-clear />
          </FormItem>
        </GridCol>
        <GridCol>
          <FormItem label="品牌名称" name="brandName">
            <InputText allow-clear />
          </FormItem>
        </GridCol>
        <GridCol>
          <FormItem label="状态" name="status">
            <DictSelect dict-code="enable_status" allow-clear />
          </FormItem>
        </GridCol>
        <GridCol>
          <FormItem label="创建时间" name="createTime">
            <DateRangePicker />
          </FormItem>
        </GridCol>
      </Grid>
    </Form>
  </Card>
  <!---------- 查询参数 end ----------->

  <Card :hoverable="true">
    <!---------- 表格操作行 begin ----------->
    <FlexRow>
      <FlexRow>
        <AxTableOperateButtons
          add-config="brand:add"
          batch-delete-config="brand:delete"
          import-config="brand:import"
          export-config="brand:export"
        />
      </FlexRow>
      <ButtonGroup>
        <IconButton label="查询" icon-type="SearchOutlined" type="primary" @click="brandService.onSearch" />
        <IconButton label="重置" icon-type="ReloadOutlined" @click="brandService.resetQuery" />
      </ButtonGroup>
    </FlexRow>
    <!---------- 表格操作行 end ----------->
    <br>
    <!-- 使用组件化的表格 -->
    <AxTable min-width="150rem">
      <template #bodyCell="{ column, record }">
        <!-- 只为操作列添加模板插槽 -->
        <template v-if="column.dataIndex === 'action'">
          <FlexRow justify="start">
            <IconAnchor
              v-privilege="'brand:view'" label="查看" icon-type="EyeOutlined"
              @click="brandService.openDetailView(record.id)"
            />
            <IconAnchor
              v-privilege="'brand:edit'" label="编辑" icon-type="EditOutlined"
              @click="brandService.openEditForm(record.id)"
            />
            <IconAnchor
              v-privilege="'brand:delete'" label="删除" icon-type="DeleteOutlined" color="red"
              @click="brandService.deleteEntity(record.id)"
            />
          </FlexRow>
        </template>
      </template>
    </AxTable>
  </Card>

  <!-- 导入弹窗 -->
  <AxImportExcelModal />
  <!-- 弹窗组件 -->
  <BrandDetail />
  <BrandForm />
</template>
