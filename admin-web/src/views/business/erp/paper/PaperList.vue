<script lang="ts" setup>
import PaperDetail from './components/PaperDetail.vue'
import PaperForm from './components/PaperForm.vue'
import { paperService } from './service/PaperService'

paperService.provide()
</script>

<template>
  <!---------- 查询参数 begin ----------->
  <Card>
    <Form v-privilege="'paper:query'" layout="vertical">
      <Grid>
        <GridCol>
          <FormItem label="描述">
            <InputText v-model="paperService.queryParam.description" placeholder="请输入描述" />
          </FormItem>
        </GridCol>
        <GridCol>
          <FormItem label="克重">
            <DictSelect v-model="paperService.queryParam.weight" key-code="paper_weight" placeholder="请选择克重" />
          </FormItem>
        </GridCol>
        <GridCol>
          <FormItem label="楞形">
            <DictSelect
              v-model="paperService.queryParam.corrugatedShape" key-code="corrugated_shape"
              placeholder="请选择楞形"
            />
          </FormItem>
        </GridCol>
        <GridCol>
          <FormItem label="创建时间">
            <DateRangePicker v-model:from="paperService.queryParam.createTimeFrom" v-model:to="paperService.queryParam.createTimeTo" />
          </FormItem>
        </GridCol>
      </Grid>
    </Form>
  </Card>
  <!---------- 查询参数 end ----------->
  <Card :hoverable="true">
    <!---------- 表格操作行 begin ----------->
    <FlexRow>
      <AxTableOperateButtons add-config="paper:add" batch-delete-config="paper:delete" import-config="paper:import" export-config="paper:export" />
      <ButtonGroup>
        <IconButton label="查询" icon-type="SearchOutlined" type="primary" @click="paperService.onSearch" />
        <IconButton label="重置" icon-type="ReloadOutlined" @click="paperService.resetQuery" />
      </ButtonGroup>
    </FlexRow>
    <!---------- 表格操作行 end ----------->
    <br>
    <AxTable>
      <template #bodyCell="{ column, record }">
        <!-- 只为操作列添加模板插槽 -->
        <template v-if="column.dataIndex === 'action'">
          <FlexRow justify="start">
            <IconAnchor
              v-privilege="'paper:view'" label="查看" icon-type="EyeOutlined"
              @click="paperService.openDetailView(record.id!)"
            />
            <IconAnchor
              v-privilege="'paper:edit'" label="编辑" icon-type="EditOutlined"
              @click="paperService.openEditForm(record.id!)"
            />
            <IconAnchor
              v-privilege="'paper:delete'" label="删除" icon-type="DeleteOutlined" color="red"
              @click="paperService.deleteEntity(record.id!)"
            />
          </FlexRow>
        </template>
      </template>
    </AxTable>
  </Card>
  <!-- 导入弹窗 -->
  <AxImportExcelModal />
  <!-- 纸张表单 -->
  <PaperForm />
  <!-- 纸张详情 -->
  <PaperDetail />
</template>
