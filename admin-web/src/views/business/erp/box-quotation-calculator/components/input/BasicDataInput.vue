<script setup lang="ts">
import { ref } from 'vue'
import SectionTitle from '../../core/SectionTitle.vue'
import SaveButton from '../button/SaveButton.vue'
import AiAssistant from '../chat/AiAssistant.vue'
import BoxTypeSelector from './BoxTypeSelector.vue'
import BusinessTypeSelector from './BusinessTypeSelector.vue'
import ColorPrintSelector from './ColorPrintSelector.vue'
import PaperBoardSelector from './paper-board-select/PaperBoardSelector.vue'
import PrintingProcessSelector from './PrintingProcessSelector.vue'

defineOptions({
  name: 'BasicDataInput',
})

const showAiAssistant = ref(false)

function openAiAssistant() {
  showAiAssistant.value = true
}
</script>

<template>
  <div class="">
    <div class="space-y-8px">
      <SectionTitle title="纸箱报价计算">
        <template #actions>
          <Button
            severity="primary"
            size="small"
            class="mr-2 p-button-rounded"
            @click="openAiAssistant"
          >
            <template #icon>
              <Icon icon-type="QuestionCircleOutlined" />
            </template>
          </Button>
          <SaveButton />
        </template>
      </SectionTitle>
      <!-- 业务类型 -->
      <BusinessTypeSelector />

      <!-- 盒型 -->
      <BoxTypeSelector />

      <!-- 纸板供应商及纸张选择 -->
      <PaperBoardSelector />

      <!-- 印刷原纸选择器 -->
      <ColorPrintSelector />

      <!-- 印刷工艺选择器 -->
      <PrintingProcessSelector />
    </div>

    <!-- AI 助手弹窗 -->
    <AiAssistant v-model:visible="showAiAssistant" />
  </div>
</template>
