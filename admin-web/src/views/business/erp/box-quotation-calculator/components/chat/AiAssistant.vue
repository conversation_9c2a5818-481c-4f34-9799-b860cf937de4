<script setup lang="ts">
import { chatStreamApi } from '@/api/business/chat/chat-stream-api'
import Icon from '@/components/base/Icon/Icon.vue'
import ProgressSpinner from 'primevue/progressspinner'
import { ref } from 'vue'

defineOptions({
  name: 'AiAssistant',
})

const props = defineProps<{
  visible: boolean
}>()

const emit = defineEmits(['update:visible'])

// 聊天记录，只保留本轮会话
const messages = ref<{ id: string, content: string, role: 'ai' | 'local', loading?: boolean }[]>([])
// 输入内容
const inputContent = ref('')
// 会话ID，直接用随机生成
const sessionId = ref(`session_${Math.random().toString(36).substring(2, 10)}`)
// 加载状态
const loading = ref(false)

// 聊天气泡设置
const roles = {
  ai: {
    placement: 'start',
    typing: { step: 5, interval: 20 },
    styles: {
      content: {
        borderRadius: '16px',
      },
    },
  },
  local: {
    placement: 'end',
    variant: 'shadow',
  },
} as const

// 发送消息
async function handleSend(content: string) {
  if (!content.trim())
    return

  // 添加用户消息
  const msgUserId = Date.now().toString()
  messages.value.push({
    id: msgUserId,
    content,
    role: 'local',
  })

  // 添加AI正在输入的消息
  const aiId = Date.now().toString() + 1
  messages.value.push({
    id: aiId,
    content: '',
    role: 'ai',
    loading: true,
  })

  loading.value = true
  inputContent.value = ''

  try {
    // 创建EventSource实例，流式接收AI回复
    const eventSource = chatStreamApi.createEventSourceChat(
      content,
      sessionId.value,
      // 接收消息回调
      (data: string) => {
        const lastMessage = messages.value[messages.value.length - 1]
        if (lastMessage && lastMessage.id === aiId) {
          lastMessage.content += data
          lastMessage.loading = false
        }
      },
      // 连接打开回调
      () => {
        const lastMessage = messages.value[messages.value.length - 1]
        if (lastMessage && lastMessage.id === aiId) {
          lastMessage.content = ''
        }
      },
      // 错误处理回调
      () => {
        eventSource.close()
        const lastMessage = messages.value[messages.value.length - 1]
        if (lastMessage && !lastMessage.content) {
          lastMessage.content = 'AI回复出错，请稍后重试'
        }
        lastMessage.loading = false
        loading.value = false
      },
    )
    // 响应完成后关闭loading
    eventSource.addEventListener('error', () => {
      loading.value = false
    })
  }
  catch {
    loading.value = false
    // 显示错误消息
    const lastMessage = messages.value[messages.value.length - 1]
    if (lastMessage && lastMessage.id.endsWith('1')) {
      lastMessage.content = '抱歉，发生错误，请稍后再试。'
      lastMessage.loading = false
    }
  }
}

// 关闭弹窗
function handleClose() {
  emit('update:visible', false)
}
</script>

<template>
  <Dialog
    :visible="props.visible"
    title="纸箱报价AI助手"
    width="650px"
    :footer="null"
    destroy-on-close
    :mask-closable="false"
    @cancel="handleClose"
    @update:visible="(val: boolean) => emit('update:visible', val)"
  >
    <div class="ai-assistant-container" :class="{ 'loading-overlay': loading }">
      <div v-if="loading" class="loading-spinner">
        <ProgressSpinner style="width: 30px; height: 30px" stroke-width="4" />
        <span>AI正在思考...</span>
      </div>
      <!-- 欢迎信息 -->
      <div v-if="messages.length === 0" class="welcome-container">
        <h3>你好，我是纸箱报价AI助手</h3>
        <p>我可以回答关于纸箱报价计算的问题，帮助你提高工作效率！</p>
      </div>

      <!-- 聊天记录 -->
      <div v-else class="message-container">
        <div
          v-for="message in messages"
          :key="message.id"
          class="message" :class="[message.role === 'ai' ? 'message-ai' : 'message-user']"
        >
          <div class="message-content">
            {{ message.content }}
            <ProgressSpinner v-if="message.loading" style="width: 16px; height: 16px" />
          </div>
        </div>
      </div>

      <!-- 输入框 -->
      <div class="input-container">
        <div class="input-wrapper">
          <InputText
            v-model="inputContent"
            placeholder="请输入您的问题..."
            :disabled="loading"
            @keyup.enter="() => handleSend(inputContent)"
          />
          <Button
            :disabled="!inputContent.trim() || loading"
            @click="() => handleSend(inputContent)"
          >
            <Icon icon-type="UploadOutlined" />
          </Button>
        </div>
      </div>
    </div>
  </Dialog>
</template>

<style scoped>
.ai-assistant-container {
  display: flex;
  flex-direction: column;
  height: 550px;
  position: relative;
}

.loading-overlay {
  position: relative;
}

.loading-spinner {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  z-index: 10;
  background: rgba(255, 255, 255, 0.9);
  padding: 20px;
  border-radius: 8px;
}

.message-container {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
  margin-bottom: 16px;
  background-color: rgba(0, 0, 0, 0.01);
  border-radius: 8px;
}

.welcome-container {
  text-align: center;
  padding: 40px 20px;
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.welcome-container h3 {
  margin-bottom: 12px;
  color: #333;
}

.welcome-container p {
  color: #666;
  margin: 0;
}

.message {
  margin-bottom: 16px;
  display: flex;
}

.message-ai {
  justify-content: flex-start;
}

.message-user {
  justify-content: flex-end;
}

.message-content {
  max-width: 70%;
  padding: 12px 16px;
  border-radius: 12px;
  background: #f5f5f5;
  display: flex;
  align-items: center;
  gap: 8px;
}

.message-user .message-content {
  background: #1890ff;
  color: white;
}

.input-container {
  padding: 0 16px 16px;
}

.input-wrapper {
  display: flex;
  gap: 8px;
  align-items: center;
}
</style>
