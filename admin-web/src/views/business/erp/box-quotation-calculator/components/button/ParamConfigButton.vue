<script setup lang="ts">
import { onMounted, ref } from 'vue'
// 移除 PrimeVue 原生组件导入，这些组件已在 components.d.ts 中全局注册
import NumericKeypadInput from '../../core/NumericKeypadInput.vue'
import { useCalculatorConfig } from '../../stores/calculatorConfig'

const configStore = useCalculatorConfig()
const modalVisible = ref(false)
const loading = ref(false)

// 刷新配置参数
async function refreshParams() {
  try {
    loading.value = true
    await configStore.fetchParams()
  }
  catch {
    // 错误已在 store 中处理
  }
  finally {
    loading.value = false
  }
}

async function handleOpenModal() {
  try {
    // 每次打开弹窗都重新请求数据
    modalVisible.value = true
    await refreshParams()
  }
  catch {
    // 错误已在 store 中处理
  }
}

async function handleSave() {
  try {
    loading.value = true
    await configStore.saveParams(configStore.allConfigs)
    modalVisible.value = false
  }
  catch {
    // 错误已在 store 中处理
  }
  finally {
    loading.value = false
  }
}

function handleCloseModal() {
  modalVisible.value = false
}

// 组件挂载时初始化参数
onMounted(refreshParams)
</script>

<template>
  <Button
    v-tooltip="'配置计算器参数'"
    text
    @click="handleOpenModal"
  >
    <template #icon>
      <Icon icon-type="SettingOutlined" />
    </template>
  </Button>

  <Dialog
    v-model:visible="modalVisible"
    header="计算器配置"
    modal
    :style="{ width: '700px' }"
    :closable="true"
    @hide="handleCloseModal"
  >
    <template #footer>
      <Button
        label="取消"
        severity="secondary"
        @click="handleCloseModal"
      />
      <Button
        label="保存"
        :loading="loading"
        @click="handleSave"
      />
    </template>
    <DataTable
      :value="configStore.allConfigs"
      size="small"
      show-gridlines
    >
      <Column
        field="name"
        header="参数名称"
        style="width: 30%; white-space: nowrap"
      >
        <template #body="{ data }">
          <span class="whitespace-nowrap">{{ data.name }}</span>
        </template>
      </Column>
      <Column
        header="默认值"
        style="width: 30%; text-align: center"
      >
        <template #body="{ data }">
          <NumericKeypadInput
            v-model:value="data.value"
            :min="data.min"
            :max="data.max"
            :step="data.step"
            :precision="data.decimalPlaces"
            :use-step-controls="true"
          />
        </template>
      </Column>
      <Column
        header="步长"
        style="width: 30%; text-align: center"
      >
        <template #body="{ data }">
          <NumericKeypadInput
            v-model:value="data.step"
            :min="0"
            :max="data.max"
            :step="data.decimalPlaces === 0 ? 1 : data.decimalPlaces === 1 ? 0.1 : 0.01"
            :precision="data.decimalPlaces"
            :use-step-controls="true"
          />
        </template>
      </Column>
      <Column
        header="最小值"
        style="width: 30%; text-align: center"
      >
        <template #body="{ data }">
          <NumericKeypadInput
            v-model:value="data.min"
            :min="0"
            :max="data.max"
            :step="data.step"
            :precision="data.decimalPlaces"
            :use-step-controls="true"
          />
        </template>
      </Column>
      <Column
        header="最大值"
        style="width: 30%; text-align: center"
      >
        <template #body="{ data }">
          <NumericKeypadInput
            v-model:value="data.max"
            :min="data.min"
            :max="Number.MAX_SAFE_INTEGER"
            :step="data.step"
            :precision="data.decimalPlaces"
            :use-step-controls="true"
          />
        </template>
      </Column>
    </DataTable>
  </Dialog>
</template>
