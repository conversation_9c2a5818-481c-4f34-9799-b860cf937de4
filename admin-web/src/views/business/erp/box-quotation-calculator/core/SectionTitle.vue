<script setup lang="ts">
defineOptions({
  name: 'SectionTitle',
})

defineProps<{
  title: string
  description?: string
}>()
</script>

<template>
  <div class="">
    <div class="flex items-center justify-between">
      <div>
        <h2 class="text-2xl font-bold text-primary mb-2 pl-4 border-l-4px border-primary">
          {{ title }}
        </h2>
        <p v-show="description" class="text-sm text-muted-color">
          {{ description }}
        </p>
      </div>
      <div class="flex items-center">
        <slot name="actions" />
      </div>
    </div>
    <Divider class="my-4" />
  </div>
</template>
