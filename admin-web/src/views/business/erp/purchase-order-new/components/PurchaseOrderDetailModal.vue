<script lang="ts" setup>
import { formatMoney } from '@/utils/format'
import { PURCHASE_ORDER_CREATE_KEY, type PurchaseOrderCreateService } from '../service/purchaseOrderCreateService'
import { warehouseSearchServiceEx } from '../service/warehouseServiceEx'
import { createItemListColumns } from './columns'
import PurchaseOrderDetail from './PurchaseOrderDetail.vue'

// 注入采购订单服务
const purchaseOrderService = inject<PurchaseOrderCreateService>(PURCHASE_ORDER_CREATE_KEY)!

// 提供仓库搜索服务
warehouseSearchServiceEx.provide()

// 创建采购订单明细表格列配置
const itemListColumns = createItemListColumns(purchaseOrderService)
</script>

<template>
  <Dialog
    v-model:visible="purchaseOrderService.detailModalOpen.value"
    modal
    header="采购订单详情"
    class="w-11/12"
    @hide="purchaseOrderService.closeDetailView"
  >
    <div v-if="purchaseOrderService.purchaseDetailLoading.value" class="text-center py-8">
      <i class="pi pi-spin pi-spinner text-2xl" />
    </div>

    <div v-else-if="purchaseOrderService.purchaseDetailData.value" class="purchase-order-detail-modal">
      <!-- 采购订单基本信息 -->
      <PurchaseOrderDetail />

      <!-- 采购订单明细 -->
      <div class="mt-6">
        <Title :level="3" value="采购订单明细" class="mb-4" />

        <DataTable
          v-if="purchaseOrderService.purchaseDetailData.value?.itemList && purchaseOrderService.purchaseDetailData.value.itemList.length > 0"
          :value="purchaseOrderService.purchaseDetailData.value.itemList"
          :scrollable="true"
          scroll-height="400px"
          size="small"
          striped-rows
          data-key="id"
        >
          <Column
            v-for="column in itemListColumns"
            :key="column.dataIndex"
            :field="column.dataIndex"
            :header="column.title"
            :style="{ minWidth: column.width || '120px' }"
          />
        </DataTable>

        <div v-else class="text-center py-8 text-gray-500">
          暂无采购订单明细
        </div>
      </div>

      <!-- 金额汇总 -->
      <div v-if="purchaseOrderService.purchaseDetailData.value?.itemList && purchaseOrderService.purchaseDetailData.value.itemList.length > 0" class="mt-6 p-4 bg-gray-50 rounded">
        <Title :level="4" value="金额汇总" class="mb-3" />

        <FlexRow justify="between" class="mb-2">
          <CustomText value="商品小计：" />
          <CustomText :value="formatMoney(purchaseOrderService.purchaseDetailData.value.subtotalAmount)" />
        </FlexRow>

        <FlexRow justify="between" class="mb-2">
          <CustomText value="折扣金额：" />
          <CustomText :value="formatMoney(purchaseOrderService.purchaseDetailData.value.discountAmount)" class="text-red-500" />
        </FlexRow>

        <FlexRow justify="between" class="mb-2">
          <CustomText value="税额：" />
          <CustomText :value="formatMoney(purchaseOrderService.purchaseDetailData.value.taxAmount)" />
        </FlexRow>

        <FlexRow justify="between" class="mb-2">
          <CustomText value="运费：" />
          <CustomText :value="formatMoney(purchaseOrderService.purchaseDetailData.value.shippingFee)" />
        </FlexRow>

        <div class="border-t border-gray-300 pt-2 mt-2">
          <FlexRow justify="between" class="mb-2">
            <CustomText strong value="最终金额：" />
            <CustomText strong :value="formatMoney(purchaseOrderService.purchaseDetailData.value.finalAmount)" class="text-xl text-blue-600" />
          </FlexRow>

          <FlexRow justify="between" class="mb-2">
            <CustomText value="已付金额：" />
            <CustomText :value="formatMoney(purchaseOrderService.purchaseDetailData.value.paidAmount)" class="text-green-600" />
          </FlexRow>

          <FlexRow justify="between">
            <CustomText strong value="剩余未付：" />
            <CustomText
              strong
              :value="formatMoney((purchaseOrderService.purchaseDetailData.value.finalAmount || 0) - (purchaseOrderService.purchaseDetailData.value.paidAmount || 0))"
              class="text-lg text-orange-500"
            />
          </FlexRow>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="mt-6 text-center">
        <IconButton
          label="关闭"
          icon-type="CloseOutlined"
          @click="purchaseOrderService.closeDetailView"
        />
      </div>
    </div>

    <div v-else class="text-center py-8 text-gray-500">
      暂无数据
    </div>
  </Dialog>
</template>

<style scoped>
.purchase-order-detail-modal {
  max-height: 80vh;
  overflow-y: auto;
}

.mt-6 {
  margin-top: 24px;
}

.mb-4 {
  margin-bottom: 16px;
}

.mb-3 {
  margin-bottom: 12px;
}

.mb-2 {
  margin-bottom: 8px;
}

.pt-2 {
  padding-top: 8px;
}

.mt-2 {
  margin-top: 8px;
}

.py-8 {
  padding-top: 32px;
  padding-bottom: 32px;
}

.p-4 {
  padding: 16px;
}

.bg-gray-50 {
  background-color: #f9fafb;
}

.rounded {
  border-radius: 6px;
}

.text-center {
  text-align: center;
}

.text-gray-500 {
  color: #6b7280;
}

.text-red-500 {
  color: #ef4444;
}

.text-green-600 {
  color: #059669;
}

.text-blue-600 {
  color: #2563eb;
}

.text-orange-500 {
  color: #f97316;
}

.text-xl {
  font-size: 1.25rem;
}

.text-lg {
  font-size: 1.125rem;
}

.border-t {
  border-top-width: 1px;
}

.border-gray-300 {
  border-color: #d1d5db;
}
</style>
