<script lang="ts" setup>
import { formatMoney } from '@/utils/format'
// TODO: 需要使用 PrimeVue 的 DataTable 和 Paginator 替换
// import { Pagination, Table } from 'ant-design-vue'
import { inject } from 'vue'
import { PURCHASE_ORDER_CREATE_KEY, type PurchaseOrderCreateService } from '../service/purchaseOrderCreateService'
import { supplierSearchServiceFormEx } from '../service/supplierSearchServiceFormEx'
import { createSelectedSkuColumns, createSkuColumns } from './columns'

// 注入订单服务
const orderSkuService = inject<PurchaseOrderCreateService>(PURCHASE_ORDER_CREATE_KEY)!

// 提供供应商搜索服务
supplierSearchServiceFormEx.provide()

// 创建SKU列配置
const skuColumns = createSkuColumns(orderSkuService)
// 创建已选商品表格列配置
const selectedSkuColumns = createSelectedSkuColumns(orderSkuService)
</script>

<template>
  <Dialog
    v-model:visible="orderSkuService.createModalOpen.value"
    header="新建采购单"
    class="w-[95%]"
  >
    <div class="relative h-75vh flex flex-col gap-4">
      <!-- 顶部供应商选择区域 -->
      <div class="h-20 border-b border-gray-200 flex-shrink-0">
        <Form layout="vertical">
          <FormItem label="供应商名称" class="w-[20vw]">
            <SupplierSearch />
          </FormItem>
        </Form>
      </div>

      <!-- 中间内容区域 -->
      <div class="flex-1 flex gap-4 min-h-0">
        <!-- 左侧：商品选择区域 -->
        <div class="relative" style="flex: 2; min-width: 400px;">
          <FlexRow justify="between" align="start" class="flex-shrink-0 h-12">
            <Title :level="1" value="商品选择" />
            <div class="flex gap-2">
              <InputText
                v-model:value="orderSkuService.skuSearchKeyword"
                placeholder="搜索商品名称或编码"
                class="w-64"
                @input="(e) => orderSkuService.debouncedSearchSku(e.target.value)"
              />
            </div>
          </FlexRow>

          <!-- 商品列表 -->
          <div class="flex-1 mb-4 min-h-0">
            <Table
              :columns="skuColumns"
              :data-source="orderSkuService.skuListData.value"
              :loading="orderSkuService.skuListLoading.value"
              class="h-[400px] w-full"
              :pagination="false"
              :scroll="{ y: 350 }"
              size="small"
              row-key="id"
            />
          </div>

          <!-- 分页 -->
          <div class="flex-shrink-0">
            <Pagination
              v-model:current="orderSkuService.skuPagination.current"
              v-model:page-size="orderSkuService.skuPagination.pageSize"
              :total="orderSkuService.skuPagination.total"
              :show-size-changer="true"
              :show-quick-jumper="true"
              :show-total="(total: number, range: number[]) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`"
              @change="orderSkuService.onSkuPageChange"
              @show-size-change="orderSkuService.onSkuPageChange"
            />
          </div>
        </div>

        <!-- 右侧：订单明细区域 -->
        <div class="relative h-full" style="flex: 1; min-width: 300px;">
          <FlexRow justify="between" align="start" class="flex-shrink-0 h-12">
            <Title :level="1" value="采购明细" />
            <IconButton
              label="一键清空"
              icon-type="ClearOutlined"
              size="small"
              :disabled="orderSkuService.purchaseOrderItemList.value.length === 0"
              @click="orderSkuService.clearSelectedSkus"
            />
          </FlexRow>

          <!-- 已选商品列表 -->
          <div class="flex-1 mb-4 min-h-0">
            <Table
              :columns="selectedSkuColumns"
              :data-source="orderSkuService.purchaseOrderItemList.value"
              class="h-[300px] w-full"
              :pagination="false"
              :scroll="{ y: 280 }"
              size="small"
              row-key="id"
            />
          </div>

          <!-- 总计 -->
          <div class="border-t border-gray-200 pt-4 flex-shrink-0">
            <FlexRow justify="between" class="mb-2">
              <CustomText strong value="总计：" />
              <CustomText strong :value="formatMoney(orderSkuService.totalAmount.value)" class="text-xl text-red-500" />
            </FlexRow>

            <!-- 已付金额 -->
            <FlexRow justify="between" class="mb-2">
              <CustomText value="已付金额：" />
              <CustomText :value="formatMoney(orderSkuService.purchaseFormData.paidAmount)" class="text-green-600" />
            </FlexRow>

            <!-- 剩余未付 -->
            <FlexRow justify="between" class="mb-4">
              <CustomText strong value="剩余未付：" />
              <CustomText strong :value="formatMoney(orderSkuService.remainingAmount.value)" class="text-lg text-orange-500" />
            </FlexRow>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <Button
        label="取消"
        severity="danger"
        @click="() => {
          supplierSearchServiceFormEx.resetListQuery()
          orderSkuService.handleCloseModal()
        }"
      />
      <Button
        label="提交采购单"
        severity="success"
        :disabled="!orderSkuService.canSubmit.value"
        @click="orderSkuService.handleSubmitOrder"
      />
    </template>
  </Dialog>
</template>

<style scoped>
.h-75vh {
  height: 75vh;
}

.flex {
  display: flex;
}

.flex-col {
  flex-direction: column;
}

.flex-1 {
  flex: 1;
}

.flex-shrink-0 {
  flex-shrink: 0;
}

.gap-4 {
  gap: 1rem;
}

.gap-2 {
  gap: 0.5rem;
}

.h-20 {
  height: 5rem;
}

.h-12 {
  height: 3rem;
}

.min-h-0 {
  min-height: 0;
}

.min-width-400px {
  min-width: 400px;
}

.min-width-300px {
  min-width: 300px;
}

.w-64 {
  width: 16rem;
}

.mb-4 {
  margin-bottom: 1rem;
}

.mb-2 {
  margin-bottom: 0.5rem;
}

.mr-2 {
  margin-right: 0.5rem;
}

.pt-4 {
  padding-top: 1rem;
}

.pt-6 {
  padding-top: 1.5rem;
}

.border-t {
  border-top-width: 1px;
}

.border-b {
  border-bottom-width: 1px;
}

.border-gray-200 {
  border-color: #e5e7eb;
}

.text-xl {
  font-size: 1.25rem;
}

.text-lg {
  font-size: 1.125rem;
}

.text-red-500 {
  color: #ef4444;
}

.text-green-600 {
  color: #059669;
}

.text-orange-500 {
  color: #f97316;
}

.relative {
  position: relative;
}
</style>
