<script lang="ts" setup>
import PurchaseOrderCreateModal from './components/PurchaseOrderCreateModal.vue'
import PurchaseOrderDetailModal from './components/PurchaseOrderDetailModal.vue'
import { purchaseOrderCreateService } from './service/purchaseOrderCreateService'
import { purchaseOrderService } from './service/purchaseOrderService'
import { supplierSearchServiceEx } from './service/supplierSearchServiceEx'

// 提供 service
purchaseOrderService.provide()
purchaseOrderCreateService.provide()
supplierSearchServiceEx.provide()
</script>

<template>
  <!---------- 查询参数 begin ----------->
  <Card>
    <Form v-privilege="'purchaseOrder:query'" layout="vertical">
      <Grid>
        <GridCol>
          <FormItem label="订单编号">
            <InputText v-model="purchaseOrderService.queryParam.orderNo" placeholder="请输入订单编号" />
          </FormItem>
        </GridCol>
        <GridCol>
          <FormItem label="供应商名称">
            <SupplierSearch />
          </FormItem>
        </GridCol>
        <GridCol>
          <FormItem label="付款方式">
            <DictSelect v-model:value="purchaseOrderService.queryParam.paymentMethod" key-code="payment_method" placeholder="请选择付款方式" />
          </FormItem>
        </GridCol>
        <GridCol>
          <FormItem label="采购日期">
            <DateRangePicker v-model:from="purchaseOrderService.queryParam.orderDateFrom" v-model:to="purchaseOrderService.queryParam.orderDateTo" />
          </FormItem>
        </GridCol>
        <GridCol>
          <FormItem label="预期交货日期">
            <DateRangePicker v-model:from="purchaseOrderService.queryParam.expectedDeliveryDateFrom" v-model:to="purchaseOrderService.queryParam.expectedDeliveryDateTo" />
          </FormItem>
        </GridCol>
        <GridCol>
          <FormItem label="采购类型">
            <DictSelect v-model:value="purchaseOrderService.queryParam.orderType" key-code="purchase_order_type" placeholder="请选择采购类型" />
          </FormItem>
        </GridCol>
        <GridCol>
          <FormItem label="优先级">
            <DictSelect v-model:value="purchaseOrderService.queryParam.priorityLevel" key-code="priority_level" placeholder="请选择优先级" />
          </FormItem>
        </GridCol>
        <GridCol>
          <FormItem label="采购来源">
            <DictSelect v-model:value="purchaseOrderService.queryParam.orderSource" key-code="purchase_order_source" placeholder="请选择采购来源" />
          </FormItem>
        </GridCol>
        <GridCol>
          <FormItem label="付款状态">
            <DictSelect v-model:value="purchaseOrderService.queryParam.paymentStatus" key-code="payment_status" placeholder="请选择付款状态" />
          </FormItem>
        </GridCol>
        <GridCol>
          <FormItem label="发票状态">
            <DictSelect v-model:value="purchaseOrderService.queryParam.invoiceStatus" key-code="invoice_status" placeholder="请选择发票状态" />
          </FormItem>
        </GridCol>
        <GridCol>
          <FormItem label="订单状态" type="none">
            <DictRadio v-model:value="purchaseOrderService.queryParam.orderStatus" key-code="purchase_order_status" @change="purchaseOrderService.onSearch" />
          </FormItem>
        </GridCol>
      </Grid>
    </Form>
  </Card>
  <!---------- 查询参数 end ----------->
  <Card>
    <!---------- 表格操作行 begin ----------->
    <FlexRow>
      <FlexRow>
        <IconButton
          v-privilege="'purchaseOrder:add'"
          label="新建采购单"
          icon-type="PlusOutlined"
          type="primary"
          @click="purchaseOrderCreateService.openCreateModal"
        />
        <TableOperateButtons batch-delete-config="purchaseOrder:delete" import-config="purchaseOrder:import" export-config="purchaseOrder:export" />
      </FlexRow>
      <ButtonGroup>
        <IconButton label="查询" icon-type="SearchOutlined" type="primary" @click="purchaseOrderService.onSearch" />
        <IconButton label="重置" icon-type="ReloadOutlined" @click="purchaseOrderService.resetQuery" />
      </ButtonGroup>
    </FlexRow>
    <!---------- 表格操作行 end ----------->
    <br>
    <!-- 使用组件化的表格 -->
    <AxTable>
      <template #bodyCell="{ column, record }">
        <!-- 只为操作列添加模板插槽 -->
        <template v-if="column.dataIndex === 'action'">
          <FlexRow justify="start">
            <IconAnchor
              v-privilege="'purchaseOrder:view'" label="详情" icon-type="EyeOutlined"
              @click="purchaseOrderCreateService.openDetailView(record.id!)"
            />
            <IconAnchor
              v-privilege="'purchaseOrder:delete'" label="删除" icon-type="DeleteOutlined" color="red"
              @click="purchaseOrderService.deleteEntity(record.id!)"
            />
          </FlexRow>
        </template>
      </template>
    </AxTable>
  </Card>
  <!-- 导入弹窗 -->
  <AxImportExcelModal />
  <!-- 新建/编辑采购单弹窗 -->
  <PurchaseOrderCreateModal />
  <!-- 采购单详情 -->
  <PurchaseOrderDetailModal />
</template>
