<script setup lang="ts">
import StockTransactionItemDetail from './components/StockTransactionItemDetail.vue'
import StockTransactionItemForm from './components/StockTransactionItemForm.vue'
import { stockTransactionItemService } from './service/StockTransactionItemService'

// 服务初始化
stockTransactionItemService.provide()
</script>

<template>
  <!---------- 查询参数 begin ----------->
  <Card>
    <Form :ref="stockTransactionItemService.tableFormRef" v-privilege="'stock-transaction-item:query'">
      <Grid>
        <GridCol>
          <FormItem label="流水单号" name="transactionNo">
            <InputText allow-clear />
          </FormItem>
        </GridCol>
        <GridCol>
          <FormItem label="仓库名称" name="warehouseName">
            <InputText allow-clear />
          </FormItem>
        </GridCol>
        <GridCol>
          <FormItem label="商品名称" name="productName">
            <InputText allow-clear />
          </FormItem>
        </GridCol>
        <GridCol>
          <FormItem label="SKU编码" name="skuCode">
            <InputText allow-clear />
          </FormItem>
        </GridCol>
        <GridCol>
          <FormItem label="创建时间" name="createTimeFrom">
            <DatePicker allow-clear />
          </FormItem>
        </GridCol>
        <GridCol>
          <FormItem label="至" name="createTimeTo">
            <DatePicker allow-clear />
          </FormItem>
        </GridCol>
      </Grid>
    </Form>
  </Card>
  <!---------- 查询参数 end ----------->

  <Card :hoverable="true">
    <!---------- 表格操作行 begin ----------->
    <FlexRow>
      <FlexRow>
        <AxTableOperateButtons add-config="stock-transaction-item:add" batch-delete-config="stock-transaction-item:delete" import-config="stock-transaction-item:import" export-config="stock-transaction-item:export" />
      </FlexRow>
      <ButtonGroup>
        <IconButton label="查询" icon-type="SearchOutlined" type="primary" @click="stockTransactionItemService.onSearch" />
        <IconButton label="重置" icon-type="ReloadOutlined" @click="stockTransactionItemService.resetQuery" />
      </ButtonGroup>
    </FlexRow>
    <!---------- 表格操作行 end ----------->
    <br>
    <!-- 使用组件化的表格 -->
    <AxTable min-width="180rem">
      <template #bodyCell="{ column, record }">
        <!-- 只为操作列添加模板插槽 -->
        <template v-if="column.dataIndex === 'action'">
          <FlexRow justify="start">
            <IconAnchor
              v-privilege="'stock-transaction-item:view'" label="查看" icon-type="EyeOutlined"
              @click="stockTransactionItemService.openDetailView(record.id)"
            />
            <IconAnchor
              v-privilege="'stock-transaction-item:edit'" label="编辑" icon-type="EditOutlined"
              @click="stockTransactionItemService.openEditForm(record.id)"
            />
            <IconAnchor
              v-privilege="'stock-transaction-item:delete'" label="删除" icon-type="DeleteOutlined" color="red"
              @click="stockTransactionItemService.deleteEntity(record.id)"
            />
          </FlexRow>
        </template>
      </template>
    </AxTable>
  </Card>

  <!-- 导入弹窗 -->
  <AxImportExcelModal />
  <!-- 弹窗组件 -->
  <StockTransactionItemDetail />
  <StockTransactionItemForm />
</template>
