<script lang="ts" setup>
// 只导入同级目录组件
import CustomerSearch from '@/components/business/AxSearchSelect/CustomerSearch/CustomerSearch.vue'
import SalesOrderCreateModal from './components/SalesOrderCreateModal.vue'
import SalesOrderPaymentModal from './components/SalesOrderShippingModal.vue'
import SalesOrderWarehouseModal from './components/SalesOrderWarehouseModal.vue'
import { customerSearchServiceEx } from './service/customerSearchServiceEx'
import { salesOrderCreateService } from './service/salesOrderCreateService'
import { salesOrderService } from './service/salesOrderService'
import { salesOrderWarehouseModalService } from './service/salesOrderWarehouseModal'

// 提供 service
salesOrderService.provide()
salesOrderCreateService.provide()
salesOrderWarehouseModalService.provide()
customerSearchServiceEx.provide()
</script>

<template>
  <!---------- 查询参数 begin ----------->
  <Card>
    <Form v-privilege="'salesOrder:query'" layout="vertical">
      <Grid>
        <GridCol>
          <FormItem label="订单编号">
            <InputText v-model="salesOrderService.queryParam.orderNo" placeholder="请输入订单编号" />
          </FormItem>
        </GridCol>
        <GridCol>
          <FormItem label="客户名称">
            <CustomerSearch />
          </FormItem>
        </GridCol>
        <GridCol>
          <FormItem label="付款方式">
            <DictSelect v-model="salesOrderService.queryParam.paymentMethod" key-code="payment_method" placeholder="请选择付款方式" />
          </FormItem>
        </GridCol>
        <GridCol>
          <FormItem label="订单日期">
            <DateRangePicker v-model:from="salesOrderService.queryParam.orderDateFrom" v-model:to="salesOrderService.queryParam.orderDateTo" />
          </FormItem>
        </GridCol>
        <GridCol>
          <FormItem label="交货日期">
            <DateRangePicker v-model:from="salesOrderService.queryParam.deliveryDateFrom" v-model:to="salesOrderService.queryParam.deliveryDateTo" />
          </FormItem>
        </GridCol>
        <GridCol :span="5">
          <FormItem label="订单状态" type="none">
            <DictRadio v-model="salesOrderService.queryParam.orderStatus" key-code="order_status" @change="salesOrderCreateService.onSearch" />
          </FormItem>
        </GridCol>
      </Grid>
    </Form>
  </Card>
  <!---------- 查询参数 end ----------->
  <Card>
    <!-- 表格操作行 -->
    <FlexRow>
      <FlexRow>
        <IconButton
          v-privilege="'order:add'"
          label="开单"
          icon-type="PlusOutlined"
          type="primary"
          @click="salesOrderCreateService.openCreateModal"
        />
        <AxTableOperateButtons
          batch-delete-config="salesOrder:delete"
          import-config="salesOrder:import"
          export-config="salesOrder:export"
        />
      </FlexRow>
      <ButtonGroup>
        <IconButton label="查询" icon-type="SearchOutlined" type="primary" @click="salesOrderCreateService.onSearch" />
        <IconButton label="重置" icon-type="ReloadOutlined" @click="salesOrderCreateService.resetQuery" />
      </ButtonGroup>
    </FlexRow>
    <!-- 操作按钮 -->
    <!-- 数据表格 -->
    <AxTable class="mt-4">
      <template #bodyCell="{ column, record }">
        <!-- 只为操作列添加模板插槽 -->
        <template v-if="column.dataIndex === 'action'">
          <FlexRow justify="start">
            <IconAnchor
              v-privilege="'salesOrder:view'" label="收款" icon-type="CreditCardOutlined"
              @click="salesOrderCreateService.openEditForm(record.id!)"
            />
            <IconAnchor
              v-privilege="'salesOrder:view'" label="发货" icon-type="EyeOutlined"
              @click="salesOrderWarehouseModalService.openEditForm(record.id!)"
            />
            <IconAnchor
              v-privilege="'salesOrder:delete'" label="删除" icon-type="DeleteOutlined" color="red"
              @click="salesOrderCreateService.deleteEntity(record.id!)"
            />
          </FlexRow>
        </template>
      </template>
    </AxTable>
  </Card>
  <!-- 导入弹窗 -->
  <AxImportExcelModal />
  <!-- 开单弹窗 -->
  <SalesOrderCreateModal />
  <!-- 收款弹窗 -->
  <SalesOrderPaymentModal />
  <!-- 仓库选择弹窗 -->
  <SalesOrderWarehouseModal />
</template>
