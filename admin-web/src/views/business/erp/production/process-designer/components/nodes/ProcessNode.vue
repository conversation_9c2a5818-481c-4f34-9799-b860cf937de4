<!-- production/process-designer/components/nodes/ProcessNode.vue -->
<script setup lang="ts">
import type { NodeProps } from '@vue-flow/core'
import type { ProcessNodeData } from '../../types/process-designer-types'
import Icon from '@/components/base/Icon/Icon.vue'
import { Handle, Position } from '@vue-flow/core'
import { NodeType } from '../../types/process-designer-types'

// 使用 Vue Flow 的 NodeProps 接口
const props = defineProps<NodeProps<ProcessNodeData>>()

// 根据节点类型获取样式
function getNodeClass() {
  const baseClass = 'px-4 py-3 rounded-lg shadow-lg border-2 transition-all duration-200 cursor-pointer min-w-[140px]'

  if (props.selected) {
    return `${baseClass} border-blue-500 shadow-blue-200`
  }

  switch (props.data.type) {
    case NodeType.START:
      return `${baseClass} bg-gradient-to-r from-green-400 to-green-600 text-white border-green-300`
    case NodeType.PROCESS:
      return `${baseClass} bg-gradient-to-r from-blue-400 to-blue-600 text-white border-blue-300`
    case NodeType.DECISION:
      return `${baseClass} bg-gradient-to-r from-orange-400 to-orange-600 text-white border-orange-300`
    case NodeType.END:
      return `${baseClass} bg-gradient-to-r from-red-400 to-red-600 text-white border-red-300`
    default:
      return `${baseClass} bg-gray-500 text-white border-gray-300`
  }
}

// 根据节点类型获取图标名称
function getIconType() {
  switch (props.data.type) {
    case NodeType.START:
      return 'PlayCircleOutlined'
    case NodeType.PROCESS:
      return 'SettingOutlined'
    case NodeType.DECISION:
      return 'CheckCircleOutlined'
    case NodeType.END:
      return 'StopOutlined'
    default:
      return 'QuestionOutlined'
  }
}
</script>

<template>
  <div :class="getNodeClass()">
    <!-- 输入连接点 -->
    <Handle
      v-if="data.type !== NodeType.START"
      type="target"
      :position="Position.Left"
      class="w-3 h-3 bg-white border-2 border-gray-400"
    />

    <!-- 节点内容 -->
    <div class="flex items-center space-x-2">
      <div class="flex-shrink-0">
        <Icon
          :icon-type="getIconType()"
          class="text-lg"
        />
      </div>
      <div class="flex-1 min-w-0">
        <div class="font-medium text-sm truncate">
          {{ data.stepName || data.label }}
        </div>
        <div v-if="data.stepCode" class="text-xs opacity-80 truncate">
          {{ data.stepCode }}
        </div>
      </div>
    </div>

    <!-- 工时信息 -->
    <div v-if="data.type === NodeType.PROCESS && data.standardTime" class="mt-2 text-xs opacity-80">
      <div class="flex justify-between">
        <span>标准工时:</span>
        <span>{{ data.standardTime }}分钟</span>
      </div>
    </div>

    <!-- 输出连接点 -->
    <Handle
      v-if="data.type !== NodeType.END"
      type="source"
      :position="Position.Right"
      class="w-3 h-3 bg-white border-2 border-gray-400"
    />
  </div>
</template>
