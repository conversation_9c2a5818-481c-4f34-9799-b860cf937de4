<!-- production/process-designer/components/NodePanel.vue -->
<script setup lang="ts">
import type { NodeType } from '../types/process-designer-types'
import Icon from '@/components/base/Icon/Icon.vue'
import { nodeTemplates } from '../config/node-config'
import { ProcessDesignerService } from '../service/ProcessDesignerService'

// 注入服务
const service = ProcessDesignerService.inject()

// 拖拽开始
function onDragStart(event: DragEvent, nodeType: NodeType) {
  if (event.dataTransfer) {
    event.dataTransfer.setData('application/vueflow', nodeType)
    event.dataTransfer.effectAllowed = 'move'
  }
}

// 添加节点到画布中心
function addNodeToCenter(nodeType: NodeType) {
  // 计算画布中心位置
  const centerX = 400
  const centerY = 300

  service.addNode(nodeType, { x: centerX, y: centerY })
}
</script>

<template>
  <div class="w-64 bg-white border-r border-gray-200 flex flex-col h-full">
    <!-- 面板标题 -->
    <div class="p-4 border-b border-gray-100">
      <h3 class="text-lg font-semibold text-gray-900 flex items-center">
        <Icon icon-type="MenuOutlined" class="mr-2 text-blue-500" />
        节点面板
      </h3>
      <p class="text-sm text-gray-500 mt-1">
        拖拽节点到画布中
      </p>
    </div>

    <!-- 节点列表 -->
    <div class="flex-1 p-4 space-y-3 overflow-y-auto">
      <div
        v-for="template in nodeTemplates"
        :key="template.type"
        class="group cursor-move select-none"
        draggable="true"
        @dragstart="onDragStart($event, template.type)"
        @click="addNodeToCenter(template.type)"
      >
        <div class="bg-gray-50 hover:bg-gray-100 border-2 border-dashed border-gray-300 hover:border-blue-400 rounded-lg p-4 transition-all duration-200">
          <div class="flex items-center space-x-3">
            <!-- 节点图标 -->
            <div
              class="w-10 h-10 rounded-lg flex items-center justify-center text-white text-lg" :class="[
                template.color,
              ]"
            >
              <component :is="template.icon" />
            </div>

            <!-- 节点信息 -->
            <div class="flex-1 min-w-0">
              <div class="font-medium text-gray-900 text-sm">
                {{ template.label }}
              </div>
              <div class="text-xs text-gray-500 mt-1 truncate">
                {{ template.description }}
              </div>
            </div>
          </div>

          <!-- 拖拽提示 -->
          <div class="mt-2 text-xs text-gray-400 group-hover:text-blue-500 transition-colors">
            <Icon icon-type="DragOutlined" class="mr-1" />
            拖拽或点击添加
          </div>
        </div>
      </div>
    </div>

    <!-- 操作按钮 -->
    <div class="p-4 border-t border-gray-100 space-y-2">
      <Button
        type="primary"
        block
        @click="service.autoLayout()"
      >
        <template #icon>
          <Icon icon-type="MenuOutlined" />
        </template>
        自动布局
      </Button>

      <Button
        block
        @click="service.clearCanvas()"
      >
        <template #icon>
          <Icon icon-type="CloseOutlined" />
        </template>
        清空画布
      </Button>
    </div>
  </div>
</template>
