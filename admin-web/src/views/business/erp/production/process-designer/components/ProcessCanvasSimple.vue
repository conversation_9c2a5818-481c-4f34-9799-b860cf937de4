<!-- 简化版流程设计器画布组件 -->
<script setup lang="ts">
import type { ConnectionParams } from '../types/process-designer-types'
import Icon from '@/components/base/Icon/Icon.vue'
import { Background } from '@vue-flow/background'
import { Controls } from '@vue-flow/controls'
import { useVueFlow, VueFlow } from '@vue-flow/core'
import { MiniMap } from '@vue-flow/minimap'
import { onMounted, ref } from 'vue'
import { ProcessDesignerService } from '../service/ProcessDesignerService'
import { NodeType } from '../types/process-designer-types'

// 注入服务
const service = ProcessDesignerService.inject()

// Vue Flow 实例
const { onConnect, onNodeClick, onEdgeClick } = useVueFlow()

// 画布容器引用
const canvasRef = ref<HTMLElement>()

// 连接节点事件
onConnect((connection: ConnectionParams) => {
  service.onConnect(connection)
})

// 点击节点事件
onNodeClick(({ node }) => {
  service.selectNode(node as any)
})

// 点击边事件
onEdgeClick(({ edge }) => {
  service.selectEdge(edge as any)
})

// 拖拽放置事件
function handleDrop(event: DragEvent) {
  event.preventDefault()

  const nodeType = event.dataTransfer?.getData('application/vueflow') as NodeType
  if (!nodeType || !canvasRef.value)
    return

  const { left, top } = canvasRef.value.getBoundingClientRect()
  const position = {
    x: event.clientX - left - 80,
    y: event.clientY - top - 40,
  }

  service.addNode(nodeType, position)
}

// 拖拽悬停事件
function handleDragOver(event: DragEvent) {
  event.preventDefault()
  if (event.dataTransfer) {
    event.dataTransfer.dropEffect = 'move'
  }
}

// 组件挂载后设置Vue Flow实例
onMounted(() => {
  service.vueFlowInstance.value = { onConnect, onNodeClick, onEdgeClick }
})
</script>

<template>
  <div ref="canvasRef" class="flex-1 relative bg-gray-50" @drop="handleDrop" @dragover="handleDragOver">
    <VueFlow
      :nodes="service.flowData.nodes"
      :edges="service.flowData.edges"
      class="h-full"
      :default-viewport="{ zoom: 1 }"
      :min-zoom="0.2"
      :max-zoom="4"
      fit-view-on-init
      :connection-line-style="{ stroke: '#3B82F6', strokeWidth: 2 }"
      :default-edge-options="{ type: 'smoothstep', animated: true }"
    >
      <!-- 背景网格 -->
      <Background
        pattern-color="#e5e7eb"
        :gap="20"
        :size="1"
        variant="dots"
      />

      <!-- 控制面板 -->
      <Controls
        class="bg-white shadow-lg rounded-lg border border-gray-200"
        :show-zoom="true"
        :show-fit-view="true"
        :show-interactive="true"
      />

      <!-- 小地图 -->
      <MiniMap
        class="bg-white shadow-lg rounded-lg border border-gray-200"
        :node-color="(node) => {
          switch (node.type) {
          case NodeType.START: return '#10b981'
          case NodeType.PROCESS: return '#3b82f6'
          case NodeType.DECISION: return '#f59e0b'
          case NodeType.END: return '#ef4444'
          default: return '#6b7280'
          }
        }"
        mask-color="rgba(0, 0, 0, 0.1)"
      />
    </VueFlow>

    <!-- 空状态提示 -->
    <div
      v-if="service.flowData.nodes.length <= 1"
      class="absolute inset-0 flex items-center justify-center pointer-events-none"
    >
      <div class="text-center text-gray-400">
        <div class="text-6xl mb-4">
          <Icon icon-type="DragOutlined" />
        </div>
        <h3 class="text-xl font-medium mb-2">
          开始设计流程
        </h3>
        <p class="text-sm">
          点击左侧按钮添加节点到画布中
        </p>
        <p class="text-sm">
          使用默认节点样式
        </p>
      </div>
    </div>
  </div>
</template>

<style>
/* Vue Flow 样式 */
@import '@vue-flow/core/dist/style.css';
@import '@vue-flow/core/dist/theme-default.css';
@import '@vue-flow/controls/dist/style.css';
@import '@vue-flow/minimap/dist/style.css';
/* @vue-flow/background 不再需要单独的 CSS 文件 */

/* 自定义样式 */
.vue-flow__node {
  cursor: pointer;
}

.vue-flow__node.selected {
  box-shadow: 0 0 0 2px #3b82f6;
}

.vue-flow__edge.selected .vue-flow__edge-path {
  stroke: #3b82f6;
  stroke-width: 3;
}

.vue-flow__handle {
  width: 12px;
  height: 12px;
  border: 2px solid #374151;
  background: #ffffff;
}

.vue-flow__handle.connectable {
  cursor: crosshair;
}

.vue-flow__handle.connecting {
  background: #3b82f6;
}
</style>
