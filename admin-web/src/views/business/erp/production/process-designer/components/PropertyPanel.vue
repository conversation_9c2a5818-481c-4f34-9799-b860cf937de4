<!-- production/process-designer/components/PropertyPanel.vue -->
<script setup lang="ts">
import { useConfirm } from 'primevue/useconfirm'
import { computed } from 'vue'
import { nodePropertyConfigs } from '../config/node-config'
import { ProcessDesignerService } from '../service/ProcessDesignerService'

// 注入服务
const service = ProcessDesignerService.inject()
const { require: requireConfirmation } = useConfirm()

// 当前选中的配置
const currentConfig = computed(() => {
  if (service.selectedNode.value) {
    return nodePropertyConfigs[service.selectedNode.value.data.type] || []
  }
  return []
})

// 当前表单数据
const formData = computed(() => {
  return service.selectedNode.value?.data || {}
})

// 更新属性
function updateProperty(key: string, value: unknown) {
  if (service.selectedNode.value) {
    service.updateNodeProperty(service.selectedNode.value.id, key, value)
  }
}

// 删除当前节点
function deleteCurrentNode() {
  requireConfirmation({
    message: '确定要删除此节点吗？',
    header: '确认删除',
    accept: () => {
      if (service.selectedNode.value) {
        service.deleteNode(service.selectedNode.value.id)
      }
    },
  })
}

// 删除连线
function deleteCurrentEdge() {
  requireConfirmation({
    message: '确定要删除此连线吗？',
    header: '确认删除',
    accept: () => {
      if (service.selectedEdge.value) {
        service.deleteEdge(service.selectedEdge.value.id)
      }
    },
  })
}

// 关闭面板
function closePanel() {
  service.selectedNode.value = null
  service.selectedEdge.value = null
  service.showPropertyPanel.value = false
}
</script>

<template>
  <div
    v-if="service.showPropertyPanel.value"
    class="w-80 bg-white border-l border-gray-200 flex flex-col h-full"
  >
    <!-- 面板标题 -->
    <div class="p-4 border-b border-gray-100 flex items-center justify-between">
      <div>
        <h3 class="text-lg font-semibold text-gray-900 flex items-center">
          <Icon icon-type="SettingOutlined" class="mr-2 text-blue-500" />
          属性设置
        </h3>
        <p v-if="service.selectedNode.value" class="text-sm text-gray-500 mt-1">
          {{ service.selectedNode.value.data.label }}
        </p>
      </div>
      <Button
        text
        severity="secondary"
        class="text-gray-400 hover:text-gray-600 p-1"
        @click="closePanel"
      >
        <template #icon>
          <Icon icon-type="CloseOutlined" />
        </template>
      </Button>
    </div>

    <!-- 属性表单 -->
    <div class="flex-1 p-4 overflow-y-auto">
      <Form v-if="service.selectedNode.value" class="space-y-4">
        <FormItem
          v-for="config in currentConfig"
          :key="config.key"
          :label="config.label"
          :required="config.required"
        >
          <!-- 文本输入 -->
          <InputText
            v-if="config.type === 'input'"
            :model-value="formData[config.key]"
            :placeholder="config.placeholder"
            @update:model-value="updateProperty(config.key, $event)"
          />

          <!-- 数字输入 -->
          <InputNumber
            v-else-if="config.type === 'number'"
            :model-value="formData[config.key]"
            :placeholder="config.placeholder"
            :min="0"
            class="w-full"
            @update:model-value="updateProperty(config.key, $event)"
          />

          <!-- 文本域 -->
          <CustomTextArea
            v-else-if="config.type === 'textarea'"
            :model-value="formData[config.key]"
            :placeholder="config.placeholder"
            :rows="3"
            @update:model-value="updateProperty(config.key, $event)"
          />

          <!-- 选择器 -->
          <Dropdown
            v-else-if="config.type === 'select'"
            :model-value="formData[config.key]"
            :options="config.options"
            option-label="label"
            option-value="value"
            :placeholder="config.placeholder"
            class="w-full"
            @change="updateProperty(config.key, $event.value)"
          />
        </FormItem>
      </Form>

      <!-- 边属性编辑 -->
      <Form v-else-if="service.selectedEdge.value" class="space-y-4">
        <FormItem label="连线标签">
          <InputText
            :model-value="service.selectedEdge.value.data?.label"
            placeholder="请输入连线标签"
            @update:model-value="service.selectedEdge.value!.data!.label = $event"
          />
        </FormItem>

        <FormItem label="条件">
          <InputText
            :model-value="service.selectedEdge.value.data?.condition"
            placeholder="请输入条件"
            @update:model-value="service.selectedEdge.value!.data!.condition = $event"
          />
        </FormItem>

        <FormItem label="备注">
          <CustomTextArea
            :model-value="service.selectedEdge.value.data?.remark"
            placeholder="请输入备注"
            :rows="3"
            @update:model-value="service.selectedEdge.value!.data!.remark = $event"
          />
        </FormItem>
      </Form>
    </div>

    <!-- 操作按钮 -->
    <div class="p-4 border-t border-gray-100 space-y-2">
      <Button
        v-if="service.selectedNode.value"
        severity="danger"
        class="w-full"
        @click="deleteCurrentNode"
      >
        <template #icon>
          <Icon icon-type="DeleteOutlined" />
        </template>
        删除节点
      </Button>

      <Button
        v-if="service.selectedEdge.value"
        severity="danger"
        class="w-full"
        @click="deleteCurrentEdge"
      >
        <template #icon>
          <Icon icon-type="DeleteOutlined" />
        </template>
        删除连线
      </Button>
    </div>
  </div>
</template>
