<script setup lang="ts">
import { useConfirm } from 'primevue/useconfirm'
import { useToast } from 'primevue/usetoast'
import { onMounted } from 'vue'
import { productionOrderService } from '../service/ProductionOrderService'
import ProductionOrderDetail from './ProductionOrderDetail.vue'
import ProductionOrderForm from './ProductionOrderForm.vue'

// 使用服务
const service = productionOrderService

// PrimeVue hooks
const toast = useToast()
const { require: requireConfirmation } = useConfirm()

// 事件处理
function handleSearch() {
  service.current.value = 1
  service.queryPage()
}

function handleReset() {
  service.resetQuery()
}

function handleRefresh() {
  service.queryPage()
}

function handleAdd() {
  service.openAddForm()
}

function handleEdit(id: number) {
  service.openEditForm(id)
}

function handleDetail(id: number) {
  service.openDetailView(id)
}

function handleDelete(id: number) {
  requireConfirmation({
    message: '确定要删除这条记录吗？',
    header: '确认删除',
    icon: 'pi pi-exclamation-triangle',
    acceptClass: 'p-button-danger',
    accept: async () => {
      try {
        await service.deleteEntity(id)
        toast.add({ severity: 'success', summary: '删除成功', life: 3000 })
      }
      catch {
        toast.add({ severity: 'error', summary: '删除失败', life: 3000 })
      }
    },
  })
}

async function handleSave() {
  try {
    await service.saveForm()
    toast.add({ severity: 'success', summary: '保存成功', life: 3000 })
  }
  catch {
    toast.add({ severity: 'error', summary: '保存失败', life: 3000 })
  }
}

function onPage(event: { page: number, rows: number }) {
  service.current.value = event.page + 1
  service.pageSize.value = event.rows
  service.queryPage()
}

// 状态颜色映射
function getStatusSeverity(status: string) {
  const severityMap: Record<string, string> = {
    pending: 'secondary',
    processing: 'info',
    completed: 'success',
    cancelled: 'danger',
  }
  return severityMap[status] || 'secondary'
}

// 优先级颜色映射
function getPrioritySeverity(priority: string) {
  const severityMap: Record<string, string> = {
    low: 'secondary',
    normal: 'primary',
    high: 'warning',
    urgent: 'danger',
  }
  return severityMap[priority] || 'secondary'
}

// 组件挂载时初始化
onMounted(() => {
  service.provide()
})
</script>

<template>
  <div class="production-order-list">
    <!-- 查询区域 -->
    <Card>
      <Form>
        <Grid>
          <GridCol>
            <FormItem label="工单编号">
              <InputText
                v-model="service.queryParam.orderNo"
                placeholder="请输入工单编号"
                @keyup.enter="handleSearch"
              />
            </FormItem>
          </GridCol>
          <GridCol>
            <FormItem label="产品名称">
              <InputText
                v-model="service.queryParam.productName"
                placeholder="请输入产品名称"
                @keyup.enter="handleSearch"
              />
            </FormItem>
          </GridCol>
          <GridCol>
            <FormItem label="状态">
              <Select
                v-model="service.queryParam.status"
                placeholder="请选择状态"
                :options="[
                  { label: '待开始', value: 'pending' },
                  { label: '进行中', value: 'processing' },
                  { label: '已完成', value: 'completed' },
                  { label: '已取消', value: 'cancelled' },
                ]"
                option-label="label"
                option-value="value"
                show-clear
                class="w-30"
              />
            </FormItem>
          </GridCol>
          <GridCol>
            <FormItem label="优先级">
              <Select
                v-model="service.queryParam.priority"
                placeholder="请选择优先级"
                :options="[
                  { label: '低', value: 'low' },
                  { label: '普通', value: 'normal' },
                  { label: '高', value: 'high' },
                  { label: '紧急', value: 'urgent' },
                ]"
                option-label="label"
                option-value="value"
                show-clear
                class="w-30"
              />
            </FormItem>
          </GridCol>
          <GridCol>
            <FormItem>
              <div class="flex gap-2">
                <Button severity="primary" @click="handleSearch">
                  <template #icon>
                    <Icon icon-type="SearchOutlined" />
                  </template>
                  查询
                </Button>
                <Button @click="handleReset">
                  <template #icon>
                    <Icon icon-type="ReloadOutlined" />
                  </template>
                  重置
                </Button>
              </div>
            </FormItem>
          </GridCol>
        </Grid>
      </Form>
    </Card>

    <!-- 操作按钮区域 -->
    <Card class="mt-4">
      <template #title>
        <div class="flex gap-2">
          <Button severity="primary" @click="handleAdd">
            <template #icon>
              <Icon icon-type="PlusOutlined" />
            </template>
            新增工单
          </Button>
          <Button @click="handleRefresh">
            <template #icon>
              <Icon icon-type="ReloadOutlined" />
            </template>
            刷新
          </Button>
        </div>
      </template>

      <!-- 数据表格 -->
      <DataTable
        :value="service.dataSource.value"
        :loading="service.loading.value"
        data-key="id"
        striped-rows
        show-gridlines
        size="small"
      >
        <Column field="orderNo" header="工单编号" style="width: 150px">
          <template #body="{ data }">
            <Button link @click="handleDetail(data.id)">
              {{ data.orderNo }}
            </Button>
          </template>
        </Column>
        <Column field="planName" header="生产计划" style="width: 120px" />

        <Column header="产品信息" style="width: 180px">
          <template #body="{ data }">
            <div>
              <div class="font-medium text-blue-600">
                {{ data.productName }}
              </div>
              <div class="text-xs text-gray-400">
                {{ data.productCode }}
              </div>
            </div>
          </template>
        </Column>

        <Column header="生产进度" style="width: 150px">
          <template #body="{ data }">
            <div>
              <div class="w-full bg-gray-200 rounded-full h-2 mb-1">
                <div
                  class="h-2 rounded-full transition-all duration-300"
                  :class="data.status === 'completed' ? 'bg-green-500' : 'bg-blue-500'"
                  :style="{ width: `${data.progressPercent}%` }"
                />
              </div>
              <div class="text-xs text-gray-600 text-center">
                {{ data.completedQuantity }}/{{ data.quantity }}
              </div>
            </div>
          </template>
        </Column>

        <Column field="startDate" header="开始日期" style="width: 100px" />

        <Column field="endDate" header="结束日期" style="width: 100px" />

        <Column header="状态" style="width: 80px">
          <template #body="{ data }">
            <Tag :severity="getStatusSeverity(data.status)" :value="data.statusText" />
          </template>
        </Column>

        <Column header="优先级" style="width: 80px">
          <template #body="{ data }">
            <Tag :severity="getPrioritySeverity(data.priority)" :value="data.priorityText" />
          </template>
        </Column>

        <Column field="remark" header="备注" />

        <Column header="操作" style="width: 150px" frozen align-frozen="right">
          <template #body="{ data }">
            <div class="flex gap-1">
              <Button link size="small" @click="handleDetail(data.id)">
                详情
              </Button>
              <Button link size="small" @click="handleEdit(data.id)">
                编辑
              </Button>
              <Button link size="small" severity="danger" @click="handleDelete(data.id)">
                删除
              </Button>
            </div>
          </template>
        </Column>
      </DataTable>

      <!-- 分页组件 -->
      <Paginator
        :first="(service.current.value - 1) * service.pageSize.value"
        :rows="service.pageSize.value"
        :total-records="service.total.value"
        :rows-per-page-options="[10, 20, 50]"
        template="FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink RowsPerPageDropdown"
        @page="onPage"
      />
    </Card>

    <!-- 表单弹窗 -->
    <ProductionOrderForm
      v-model:visible="service.formVisible.value"
      :form-data="service.formData"
      :form-mode="service.formMode.value"
      @save="handleSave"
    />

    <!-- 详情弹窗 -->
    <ProductionOrderDetail
      v-model:visible="service.detailVisible.value"
      :detail-data="service.detailData"
    />
  </div>
</template>
