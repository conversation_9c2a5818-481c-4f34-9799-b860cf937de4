<script setup lang="ts">
import WarehouseDetail from './components/WarehouseDetail.vue'
import WarehouseForm from './components/WarehouseForm.vue'
import { warehouseService } from './service/WarehouseService'

warehouseService.provide()
</script>

<template>
  <!---------- 查询参数 begin ----------->
  <Card>
    <Form :ref="warehouseService.tableFormRef" v-privilege="'warehouse:query'">
      <Grid>
        <GridCol>
          <FormItem label="仓库编码" name="warehouseCode">
            <InputText allow-clear />
          </FormItem>
        </GridCol>
        <GridCol>
          <FormItem label="仓库名称" name="warehouseName">
            <InputText allow-clear />
          </FormItem>
        </GridCol>
        <GridCol>
          <FormItem label="仓库类型" name="warehouseType">
            <DictSelect key-code="warehouse_type" allow-clear />
          </FormItem>
        </GridCol>
        <GridCol>
          <FormItem label="仓库状态" name="status">
            <DictSelect key-code="warehouse_status" allow-clear />
          </FormItem>
        </GridCol>
        <GridCol>
          <FormItem label="省份" name="province">
            <InputText allow-clear />
          </FormItem>
        </GridCol>
        <GridCol>
          <FormItem label="城市" name="city">
            <InputText allow-clear />
          </FormItem>
        </GridCol>
        <GridCol>
          <FormItem label="联系人" name="contactPerson">
            <InputText allow-clear />
          </FormItem>
        </GridCol>
        <GridCol>
          <FormItem label="是否默认" name="isDefault">
            <DictSelect key-code="yes_no" allow-clear />
          </FormItem>
        </GridCol>
      </Grid>
    </Form>
  </Card>
  <!---------- 查询参数 end ----------->

  <Card :hoverable="true">
    <!---------- 表格操作行 begin ----------->
    <FlexRow>
      <FlexRow>
        <AxTableOperateButtons add-config="warehouse:add" batch-delete-config="warehouse:delete" import-config="warehouse:import" export-config="warehouse:export" />
      </FlexRow>
      <ButtonGroup>
        <IconButton label="查询" icon-type="SearchOutlined" type="primary" @click="warehouseService.onSearch" />
        <IconButton label="重置" icon-type="ReloadOutlined" @click="warehouseService.resetQuery" />
      </ButtonGroup>
    </FlexRow>
    <!---------- 表格操作行 end ----------->
    <br>
    <!-- 使用组件化的表格 -->
    <AxTable min-width="180rem">
      <template #bodyCell="{ column, record }">
        <!-- 只为操作列添加模板插槽 -->
        <template v-if="column.dataIndex === 'action'">
          <FlexRow justify="start">
            <IconAnchor
              v-privilege="'warehouse:view'" label="查看" icon-type="EyeOutlined"
              @click="warehouseService.openDetailView(record.id)"
            />
            <IconAnchor
              v-privilege="'warehouse:edit'" label="编辑" icon-type="EditOutlined"
              @click="warehouseService.openEditForm(record.id)"
            />
            <IconAnchor
              v-privilege="'warehouse:delete'" label="删除" icon-type="DeleteOutlined" color="red"
              @click="warehouseService.deleteEntity(record.id)"
            />
          </FlexRow>
        </template>
      </template>
    </AxTable>
  </Card>

  <!-- 导入弹窗 -->
  <AxImportExcelModal />
  <!-- 弹窗组件 -->
  <WarehouseDetail />
  <WarehouseForm />
</template>
