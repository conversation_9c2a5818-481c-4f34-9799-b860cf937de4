<script lang="ts" setup>
import { formatBooleanColor } from '@/utils/boolean-util'
import { onBeforeUnmount } from 'vue'
import ProductDetail from './components/ProductDetail.vue'
import ProductForm from './components/ProductForm.vue'
import SkuForm from './components/SkuForm.vue'
import { formatStatusText, skuColumns } from './config/productColumns'
import { ProductCategoryTreeServiceEx } from './service/ProductCategoryTreeServiceEx'

import { productService } from './service/ProductService'

// 提供服务实例
productService.provide()
const productCategoryTreeServiceEx = new ProductCategoryTreeServiceEx()
productCategoryTreeServiceEx.provide()

// 组件销毁前清空SKU缓存和状态
onBeforeUnmount(() => {
  productService.clearState()
})
</script>

<template>
  <!---------- 查询参数 begin ----------->
  <Card>
    <Form :ref="productService.tableFormRef" v-privilege="'product:query'">
      <Grid>
        <GridCol>
          <FormItem label="商品编码" name="productCode">
            <InputText placeholder="请输入商品编码" />
          </FormItem>
        </GridCol>
        <GridCol>
          <FormItem label="商品名称" name="name">
            <InputText placeholder="请输入商品名称" />
          </FormItem>
        </GridCol>
        <GridCol>
          <FormItem label="分类" name="categoryId">
            <AxProductCategoryTree />
          </FormItem>
        </GridCol>
        <GridCol>
          <FormItem label="品牌" name="brandId">
            <BrandSearch />
          </FormItem>
        </GridCol>
        <GridCol>
          <FormItem label="状态" name="status">
            <DictSelect key-code="product_status" placeholder="请选择状态" />
          </FormItem>
        </GridCol>
        <GridCol>
          <FormItem type="none" name="isRawMaterial">
            <BooleanRadio true-text="是原材料" false-text="不是原材料" />
          </FormItem>
        </GridCol>
      </Grid>
    </Form>
  </Card>
  <!---------- 查询参数 end ----------->

  <Card :hoverable="true">
    <!---------- 表格操作行 begin ----------->
    <FlexRow>
      <FlexRow>
        <TableOperateButtons
          add-config="product:add"
          batch-delete-config="product:delete"
          import-config="product:import"
          export-config="product:export"
        />
      </FlexRow>
      <ButtonGroup>
        <IconButton label="查询" icon-type="SearchOutlined" type="primary" @click="productService.onSearch" />
        <IconButton label="重置" icon-type="ReloadOutlined" @click="productService.resetQuery" />
      </ButtonGroup>
    </FlexRow>
    <!---------- 表格操作行 end ----------->

    <TableExpand>
      <template #expandedRowRender="{ record }">
        <CustomTable :data-source="productService.getSkuData(record.id)" :columns="skuColumns" row-key="id">
          <template #bodyCell="{ column, record: skuRecord }">
            <template v-if="column.dataIndex === 'status'">
              <Tag :color="formatBooleanColor(skuRecord.status)" :label="formatStatusText(skuRecord.status)" />
            </template>
          </template>
        </CustomTable>
      </template>

      <template #bodyCell="{ column, record }">
        <!-- 是否原材料列 -->
        <template v-if="column.dataIndex === 'isRawMaterial'">
          <Tag :color="record.isRawMaterial ? 'success' : 'default'" :label="record.isRawMaterial ? '是' : '否'" />
        </template>

        <!-- 来源列 -->
        <template v-if="column.dataIndex === 'sourceType'">
          <DictValue key-code="product_source_type" :value-code="record.sourceType" />
        </template>

        <!-- 状态列 -->
        <template v-if="column.dataIndex === 'status'">
          <Tag :color="productService.getStatusColor(record.status)" :label="productService.formatStatus(record.status)" />
        </template>

        <!-- 操作列 -->
        <template v-if="column.dataIndex === 'action'">
          <FlexRow justify="start" gap="1px">
            <IconAnchor
              v-privilege="'product:add'" label="新增" icon-type="PlusOutlined"
              @click="productService.openAddSkuForm(record.id)"
            />
            <IconAnchor
              v-privilege="'product:view'" label="查看" icon-type="EyeOutlined"
              @click="productService.openDetailView(record.id)"
            />
            <IconAnchor
              v-privilege="'product:edit'" label="编辑" icon-type="EditOutlined"
              @click="productService.openEditForm(record.id)"
            />
            <IconAnchor
              v-privilege="'product:delete'" label="删除" icon-type="DeleteOutlined" color="red"
              @click="productService.deleteEntity(record.id)"
            />
          </FlexRow>
        </template>
      </template>
    </TableExpand>
  </Card>
  <!-- 导入弹窗 -->
  <AxImportExcelModal />
  <!-- 商品表单 -->
  <ProductForm />
  <!-- 商品详情 -->
  <ProductDetail />
  <!-- SKU表单 -->
  <SkuForm />
</template>
