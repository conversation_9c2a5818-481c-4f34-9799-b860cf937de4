<script lang="ts" setup>
import type { PurchaseOrderSkuService } from '../service/purchaseOrderSkuService'
import { CustomText, CustomTextArea as TextArea, CustomTextTitle as Title } from '@/components/base/CustomText'
import SupplierSearch from '@/components/business/AxSearchSelect/SupplierSearch/SupplierSearch.vue'
import { CRUD_KEY } from '@/service/composite/TableCrudService'
import { supplierSearchServiceFormEx } from '../service/supplierSearchServiceFormEx'
import { createSelectedSkuColumns, createSkuColumns, paymentMethodOptions } from './columns'

// 注入订单服务
const orderSkuService = inject<PurchaseOrderSkuService>(CRUD_KEY)!
// 提供供应商搜索服务
supplierSearchServiceFormEx.provide()

// 创建SKU列配置
const skuColumns = createSkuColumns(orderSkuService)
// 创建已选商品表格列配置
const selectedSkuColumns = createSelectedSkuColumns(orderSkuService)
</script>

<template>
  <Dialog
    v-model:visible="orderSkuService.createModalOpen"
    header="开单"
    class="relative overflow-hidden" :class="[
      orderSkuService.isSettlementTab ? 'w-[60vw]' : 'w-[80vw]',
    ]"
  >
    <div class="relative w-full h-75vh flex flex-col">
      <!-- 顶部供应商选择区域 -->
      <div class="w-full h-12 border-b border-gray-200 flex-shrink-0">
        <FlexRow justify="start">
          <FormItem label="供应商名称" style="margin-right: 24px;">
            <SupplierSearch />
          </FormItem>
        </FlexRow>
      </div>

      <!-- 底部区域：左侧Tabs内容 + 右侧订单明细 -->
      <div class="relative w-full flex-1 flex gap-4 min-h-0">
        <!-- 左侧：Tabs切换区域 -->
        <div
          class="relative h-full overflow-y-hidden"
          :style="{ width: orderSkuService.isSettlementTab ? '30vw' : '50vw' }"
        >
          <BaseTabs v-model:value="orderSkuService.activeTab" class="h-full">
            <BaseTabList>
              <BaseTab value="products">
                商品选择
              </BaseTab>
              <BaseTab value="settlement">
                结算信息
              </BaseTab>
            </BaseTabList>
            <BaseTabPanels>
              <!-- Tab 1: 商品选择 -->
              <BaseTabsPanel value="products">
                <!-- 商品搜索 -->
                <FlexRow class="mb-4 flex-shrink-0">
                  <InputText
                    v-model="orderSkuService.productSearchParam.skuNameExt"
                    placeholder="搜索商品名称或SKU编码"
                    class="w-72 mr-3"
                  />
                  <IconButton
                    label="重置"
                    icon-type="ReloadOutlined"
                    @click="() => orderSkuService.resetSearch()"
                  />
                </FlexRow>

                <!-- Sku列表表格 -->
                <div class="flex-1 flex flex-col min-h-0">
                  <CustomTable
                    :columns="skuColumns"
                    :data-source="orderSkuService.productList"
                    :scroll="{ y: 400, x: 400 }"
                    size="small"
                    row-key="id"
                  />

                  <!-- 商品分页 -->
                  <div class="mt-4 text-center flex-shrink-0">
                    <Paginator
                      v-model:first="orderSkuService.productSearchParam.pageParam.pageNum"
                      :rows="orderSkuService.productSearchParam.pageParam.pageSize"
                      :total-records="orderSkuService.productTotal"
                      :rows-per-page-options="[10, 20, 50]"
                      template="FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink RowsPerPageDropdown"
                      @page="(event) => orderSkuService.onProductPageChange(event.page + 1, event.rows)"
                    />
                  </div>
                </div>
              </BaseTabsPanel>
              <!-- Tab 2: 结算信息 -->
              <BaseTabsPanel value="settlement">
                <div class="h-full p-4">
                  <Form layout="vertical" class="space-y-4">
                    <FormItem label="联系人" required>
                      <InputText
                        v-model="orderSkuService.orderForm.contactName"
                        disabled
                        placeholder="请输入供应商"
                        class="w-full"
                      />
                    </FormItem>

                    <FormItem label="联系电话" required>
                      <InputText
                        v-model="orderSkuService.orderForm.contactPhone"
                        disabled
                        placeholder="请输入供应商"
                        class="w-full"
                      />
                    </FormItem>

                    <FormItem label="收货地址" required>
                      <TextArea
                        v-model="orderSkuService.orderForm.deliveryAddress"
                        disabled
                        placeholder="请输入供应商"
                        :rows="1"
                        :auto-size="false"
                        style="resize: none;"
                        class="w-full"
                      />
                    </FormItem>

                    <FormItem label="付款账户" required>
                      <Dropdown
                        v-model="orderSkuService.orderForm.paymentMethod"
                        :options="paymentMethodOptions"
                        placeholder="请选择付款账户"
                        class="w-full"
                      />
                    </FormItem>

                    <FormItem label="已付金额">
                      <InputNumber
                        v-model="orderSkuService.orderForm.paidAmount"
                        :min="0"
                        :max="orderSkuService.totalAmount"
                        :min-fraction-digits="2"
                        :max-fraction-digits="2"
                        placeholder="请输入已付金额"
                        class="w-full"
                        @input="orderSkuService.handlePaidAmountChange"
                        @focus="orderSkuService.handlePaidAmountFocus"
                      />
                      <div v-if="(orderSkuService.orderForm.paidAmount || 0) > orderSkuService.totalAmount" class="text-red-500 text-xs mt-1">
                        已付金额不能超过总金额
                      </div>
                    </FormItem>
                  </Form>
                </div>
              </BaseTabsPanel>
            </BaseTabPanels>
          </BaseTabs>
        </div>

        <!-- 右侧：订单明细区域 -->
        <div class="relative h-full" style="width: 30vw;">
          <FlexRow justify="between" class="flex-shrink-0">
            <Title :level="1" value="订单明细" />
            <IconButton
              v-if="!orderSkuService.isSettlementTab"
              label="一键清空"
              icon-type="ClearOutlined"
              size="small"
              danger
              :disabled="orderSkuService.selectedProducts.length === 0"
              @click="() => orderSkuService.selectedSkus.splice(0)"
            />
          </FlexRow>

          <!-- 已选商品列表 -->
          <div class="flex-1 mb-4 min-h-0">
            <CustomTable
              :columns="selectedSkuColumns"
              :data-source="orderSkuService.selectedProducts"
              :scroll="{ y: 300 }"
              size="small"
              row-key="id"
            />
          </div>

          <!-- 总计 -->
          <div class="border-t border-gray-200 pt-4 flex-shrink-0">
            <FlexRow justify="between" class="mb-2">
              <CustomText strong value="总计：" />
              <CustomText strong :value="`¥${orderSkuService.totalAmount.toFixed(2)}`" class="text-xl text-red-500" />
            </FlexRow>

            <!-- 已付金额 -->
            <FlexRow justify="between" class="mb-2">
              <CustomText value="已付金额：" />
              <CustomText :value="`¥${(orderSkuService.orderForm.paidAmount || 0).toFixed(2)}`" class="text-green-600" />
            </FlexRow>

            <!-- 剩余未付 -->
            <FlexRow justify="between" class="mb-4">
              <CustomText strong value="剩余未付：" />
              <CustomText strong :value="`¥${orderSkuService.remainingAmount.toFixed(2)}`" class="text-lg text-orange-500" />
            </FlexRow>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <!-- 商品选择页按钮 -->
      <template v-if="!orderSkuService.isSettlementTab">
        <Button
          label="取消"
          severity="danger"
          @click="() => {
            supplierSearchServiceFormEx.resetListQuery()
            orderSkuService.handleCloseModal()
          }"
        />
        <Button
          label="下一步"
          @click="() => orderSkuService.switchTab('settlement')"
        />
      </template>

      <!-- 结算信息页按钮 -->
      <template v-else-if="orderSkuService.isSettlementTab">
        <Button
          label="取消"
          severity="danger"
          @click="() => {
            supplierSearchServiceFormEx.resetListQuery()
            orderSkuService.handleCloseModal()
          }"
        />
        <Button
          label="上一步"
          @click="() => orderSkuService.switchTab('products')"
        />
        <Button
          label="提交订单"
          severity="success"
          :disabled="!orderSkuService.canSubmit"
          @click="orderSkuService.handleSubmitOrder"
        />
      </template>
    </template>
  </Dialog>
</template>
