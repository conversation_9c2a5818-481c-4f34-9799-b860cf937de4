<script setup lang="ts">
import CustomerDetail from './components/CustomerDetail.vue'
import CustomerForm from './components/CustomerForm.vue'
import { customerService } from './service/CustomerService'

// 服务初始化
customerService.provide()
</script>

<template>
  <!---------- 查询参数 begin ----------->
  <Card>
    <Form :ref="customerService.tableFormRef" v-privilege="'customer:query'">
      <Grid>
        <GridCol>
          <FormItem label="客户编码" name="customerCode">
            <InputText allow-clear />
          </FormItem>
        </GridCol>
        <GridCol>
          <FormItem label="客户名称" name="customerName">
            <InputText allow-clear />
          </FormItem>
        </GridCol>
        <GridCol>
          <FormItem label="客户类型" name="customerType">
            <Select
              placeholder="请选择客户类型"
              allow-clear
              :options="[
                { label: '普通客户', value: 'REGULAR' },
                { label: 'VIP客户', value: 'VIP' },
                { label: '战略客户', value: 'STRATEGIC' },
                { label: '经销商', value: 'DISTRIBUTOR' },
                { label: '代理商', value: 'AGENT' },
              ]"
            />
          </FormItem>
        </GridCol>
        <GridCol>
          <FormItem label="客户状态" name="status">
            <Select
              placeholder="请选择客户状态"
              allow-clear
              :options="[
                { label: '活跃', value: 'ACTIVE' },
                { label: '非活跃', value: 'INACTIVE' },
                { label: '黑名单', value: 'BLACKLIST' },
                { label: '潜在客户', value: 'POTENTIAL' },
              ]"
            />
          </FormItem>
        </GridCol>
        <GridCol>
          <FormItem label="联系人姓名" name="contactName">
            <InputText allow-clear />
          </FormItem>
        </GridCol>
        <GridCol>
          <FormItem label="联系人电话" name="contactPhone">
            <InputText allow-clear />
          </FormItem>
        </GridCol>
        <GridCol>
          <FormItem label="创建时间" name="createTime">
            <DatePicker
              type="daterange"
              allow-clear
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
            />
          </FormItem>
        </GridCol>
      </Grid>
    </Form>
  </Card>
  <!---------- 查询参数 end ----------->

  <Card :hoverable="true">
    <!---------- 表格操作行 begin ----------->
    <FlexRow>
      <FlexRow>
        <AxTableOperateButtons
          add-config="customer:add"
          batch-delete-config="customer:delete"
          import-config="customer:import"
          export-config="customer:export"
        />
      </FlexRow>
      <ButtonGroup>
        <IconButton
          label="查询"
          icon-type="SearchOutlined"
          type="primary"
          @click="customerService.onSearch"
        />
        <IconButton
          label="重置"
          icon-type="ReloadOutlined"
          @click="customerService.resetQuery"
        />
      </ButtonGroup>
    </FlexRow>
    <!---------- 表格操作行 end ----------->
    <br>
    <!-- 使用组件化的表格 -->
    <AxTable min-width="180rem">
      <template #bodyCell="{ column, record }">
        <!-- 只为操作列添加模板插槽 -->
        <template v-if="column.dataIndex === 'action'">
          <FlexRow justify="start">
            <IconAnchor
              v-privilege="'customer:view'"
              label="查看"
              icon-type="EyeOutlined"
              @click="customerService.openDetailView(record.id)"
            />
            <IconAnchor
              v-privilege="'customer:edit'"
              label="编辑"
              icon-type="EditOutlined"
              @click="customerService.openEditForm(record.id)"
            />
            <IconAnchor
              v-privilege="'customer:delete'"
              label="删除"
              icon-type="DeleteOutlined"
              color="red"
              @click="customerService.deleteEntity(record.id)"
            />
          </FlexRow>
        </template>
      </template>
    </AxTable>
  </Card>

  <!-- 导入弹窗 -->
  <AxImportExcelModal />
  <!-- 弹窗组件 -->
  <CustomerDetail />
  <CustomerForm />
</template>
