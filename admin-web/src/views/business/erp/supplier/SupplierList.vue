<script lang="ts" setup>
import SupplierDetail from './components/SupplierDetail.vue'
import SupplierForm from './components/SupplierForm.vue'
import { supplierService } from './service/SupplierService'

supplierService.provide()
</script>

<template>
  <FlexCol class="h-full">
    <!---------- 查询参数 begin ----------->
    <Card>
      <Form :ref="supplierService.tableFormRef" v-privilege="'supplier:query'">
        <Grid>
          <GridCol>
            <FormItem label="供应商编码" name="supplierCode">
              <InputText />
            </FormItem>
          </GridCol>
          <GridCol>
            <FormItem label="供应商名称" name="supplierName">
              <InputText />
            </FormItem>
          </GridCol>
          <GridCol>
            <FormItem label="供应商类型" name="supplierType">
              <DictSelect key-code="supplier_type" />
            </FormItem>
          </GridCol>
          <GridCol>
            <FormItem label="联系人" name="contactPerson">
              <InputText />
            </FormItem>
          </GridCol>
          <GridCol>
            <FormItem label="联系电话" name="contactPhone">
              <InputText />
            </FormItem>
          </GridCol>
          <GridCol>
            <FormItem label="审批状态" name="approvalStatus">
              <DictSelect key-code="approval_status" />
            </FormItem>
          </GridCol>
          <GridCol>
            <FormItem label="创建时间" name="createTime">
              <DateRangePicker />
            </FormItem>
          </GridCol>
        </Grid>
      </Form>
    </Card>
    <!---------- 查询参数 end ----------->
    <Card :hoverable="true">
      <!---------- 表格操作行 begin ----------->
      <FlexRow>
        <FlexRow>
          <AxTableOperateButtons add-config="supplier:add" batch-delete-config="supplier:delete" import-config="supplier:import" export-config="supplier:export" />
          <CustomIconButton label="新增" type="primary" @click="supplierService.openAddForm" />
        </FlexRow>
        <ButtonGroup>
          <IconButton label="查询" icon-type="SearchOutlined" type="primary" @click="supplierService.onSearch" />
          <IconButton label="重置" icon-type="ReloadOutlined" @click="supplierService.resetQuery" />
        </ButtonGroup>
      </FlexRow>
      <!---------- 表格操作行 end ----------->
      <br>
      <!-- 使用组件化的表格 -->
      <AxTable min-width="150rem">
        <template #bodyCell="{ column, record }">
          <!-- 只为操作列添加模板插槽 -->
          <template v-if="column.dataIndex === 'action'">
            <FlexRow justify="start">
              <IconAnchor
                v-privilege="'supplier:view'" label="查看" icon-type="EyeOutlined" @click="supplierService.openDetailView(record.id)"
              />
              <IconAnchor
                v-privilege="'supplier:edit'" label="编辑" icon-type="EditOutlined" @click="supplierService.openEditForm(record.id)"
              />
              <IconAnchor
                v-privilege="'supplier:delete'" label="删除" icon-type="DeleteOutlined" color="red" @click="supplierService.deleteEntity(record.id)"
              />
            </FlexRow>
          </template>
        </template>
      </AxTable>
    </Card>
  </FlexCol>
  <!-- 导入弹窗 -->
  <AxImportExcelModal />
  <!-- 供应商表单 -->
  <SupplierForm />
  <!-- 供应商详情 -->
  <SupplierDetail />
</template>
