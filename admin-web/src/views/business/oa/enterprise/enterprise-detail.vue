<!--
  * 公司 详情
-->
<script setup lang="ts">
import { enterpriseApi } from '@/api/business/oa/enterprise-api'
import { AxLoading } from '@/components/base/ax-loading'
import Icon from '@/components/base/Icon/Icon.vue'
import DataTracer from '@/components/utils/data-tracer/index.vue'
import FilePreview from '@/components/utils/file-preview/index.vue'
import { DATA_TRACER_TYPE_ENUM } from '@/constants/support/data-tracer-const'
import { sentry } from '@/lib/sentry'
import { computed, onMounted, ref } from 'vue'
import { useRoute } from 'vue-router'
import BankList from './components/enterprise-bank-list.vue'
import EmployeeList from './components/enterprise-employee-list.vue'
import InvoiceList from './components/enterprise-invoice-list.vue'
import EnterpriseOperate from './components/enterprise-operate-modal.vue'

const route = useRoute()
const enterpriseId = ref()
onMounted(() => {
  if (route.query.enterpriseId) {
    enterpriseId.value = Number(route.query.enterpriseId)
    getDetail()
  }
})

// 编辑
const operateRef = ref()
function showUpdate() {
  operateRef.value.showModal(enterpriseId.value)
}

// 详情
const detail = ref({})

async function getDetail() {
  try {
    const result = await enterpriseApi.detail(enterpriseId.value)
    detail.value = result.data
  }
  catch (error) {
    sentry.captureError(error)
  }
  finally {
    AxLoading.hide()
  }
}

// 地区
const area = computed(() => {
  let area = ''
  if (!detail.value) {
    return area
  }
  if (detail.value.provinceName) {
    area = area + detail.value.provinceName
  }
  if (detail.value.cityName) {
    area = area + detail.value.cityName
  }
  if (detail.value.districtName) {
    area = area + detail.value.districtName
  }
  return area
})

const logo = computed(() => {
  if (!detail.value) {
    return ''
  }
  if (!_isEmpty(detail.value.enterpriseLogo)) {
    return detail.value.enterpriseLogo[0].fileUrl
  }
  return ''
})
</script>

<template>
  <div class="bg-white p-4 mb-4 rounded">
    <div class="flex items-center justify-between mb-4">
      <div class="flex items-center gap-4">
        <img v-if="logo" :src="logo" class="w-16 h-16 rounded-full object-cover">
        <div>
          <h1 class="text-2xl font-bold">
            {{ detail.enterpriseName }}
          </h1>
        </div>
      </div>
      <Button severity="info" @click="showUpdate">
        <template #icon>
          <Icon icon-type="EditOutlined" />
        </template>
        编辑
      </Button>
    </div>

    <Descriptions>
      <DescriptionsItem label="统一社会信用代码">
        {{ detail.unifiedSocialCreditCode }}
      </DescriptionsItem>
      <DescriptionsItem label="联系人">
        {{ detail.contact }}
      </DescriptionsItem>
      <DescriptionsItem label="联系人电话">
        {{ detail.contactPhone }}
      </DescriptionsItem>
      <DescriptionsItem label="邮箱">
        {{ detail.email }}
      </DescriptionsItem>
      <DescriptionsItem label="所在城市">
        {{ area }}
      </DescriptionsItem>
      <DescriptionsItem label="详细地址">
        {{ detail.address }}
      </DescriptionsItem>
      <DescriptionsItem label="创建时间">
        {{ detail.createTime }}
      </DescriptionsItem>
      <DescriptionsItem label="创建人">
        {{ detail.createUserName }}
      </DescriptionsItem>
      <DescriptionsItem label="营业执照">
        <FilePreview :file-list="detail.businessLicense" />
      </DescriptionsItem>
    </Descriptions>
  </div>

  <Card>
    <TabView>
      <TabPanel header="员工信息">
        <EmployeeList :enterprise-id="enterpriseId" />
      </TabPanel>
      <TabPanel header="银行信息">
        <BankList :enterprise-id="enterpriseId" />
      </TabPanel>
      <TabPanel header="发票信息">
        <InvoiceList :enterprise-id="enterpriseId" />
      </TabPanel>
      <TabPanel header="变更记录">
        <DataTracer :data-id="enterpriseId" :type="DATA_TRACER_TYPE_ENUM.OA_ENTERPRISE.value" />
      </TabPanel>
    </TabView>
    <EnterpriseOperate ref="operateRef" @refresh="getDetail" />
  </Card>
</template>
