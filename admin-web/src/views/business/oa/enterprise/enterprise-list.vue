<!--
  * 企业列表
-->
<script setup lang="ts">
import { enterpriseApi } from '@/api/business/oa/enterprise-api'
import { AxLoading } from '@/components/base/ax-loading'
import Icon from '@/components/base/Icon/Icon.vue'
import { PAGE_SIZE, PAGE_SIZE_OPTIONS } from '@/constants/common-const'
import { TABLE_ID_CONST } from '@/constants/support/table-id-const'
import { defaultTimeRanges } from '@/lib/default-time-ranges'
import { sentry } from '@/lib/sentry'
import { useConfirm } from 'primevue/useconfirm'
import { useToast } from 'primevue/usetoast'
import { onMounted, reactive, ref } from 'vue'
import { useRouter } from 'vue-router'
import EnterpriseOperate from './components/enterprise-operate-modal.vue'

const toast = useToast()
const { require: requireConfirmation } = useConfirm()

// --------------------------- 企业表格 列 ---------------------------

const columns = ref([
  {
    title: '企业名称',
    dataIndex: 'enterpriseName',
    minWidth: 180,
    ellipsis: true,
  },
  {
    title: '统一社会信用代码',
    dataIndex: 'unifiedSocialCreditCode',
    minWidth: 170,
    ellipsis: true,
  },
  {
    title: '企业类型',
    dataIndex: 'type',
    width: 100,
  },
  {
    title: '联系人',
    width: 100,
    dataIndex: 'contact',
    ellipsis: true,
  },
  {
    title: '联系人电话',
    width: 120,
    dataIndex: 'contactPhone',
    ellipsis: true,
  },
  {
    title: '邮箱',
    minWidth: 100,
    dataIndex: 'email',
    ellipsis: true,
  },
  {
    title: '状态',
    width: 50,
    dataIndex: 'disabledFlag',
  },
  {
    title: '创建人',
    width: 60,
    dataIndex: 'createUserName',
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    width: 150,
  },
  {
    title: '操作',
    dataIndex: 'action',
    width: 100,
  },
])

// --------------------------- 查询 ---------------------------

const queryFormState = {
  keywords: '',
  endTime: null,
  startTime: null,
  pageNum: 1,
  pageSize: PAGE_SIZE,
  searchCount: true,
}
const queryForm = reactive({ ...queryFormState })
const tableLoading = ref(false)
const tableData = ref([])
const total = ref(0)

// 日期选择
const searchDate = ref()

function dateChange(value: Date | Date[] | null) {
  if (Array.isArray(value) && value.length === 2 && value[0] && value[1]) {
    queryForm.startTime = value[0].toISOString().split('T')[0]
    queryForm.endTime = value[1].toISOString().split('T')[0]
  }
  else {
    queryForm.startTime = null
    queryForm.endTime = null
  }
}

function onSearch() {
  queryForm.pageNum = 1
  ajaxQuery()
}

function resetQuery() {
  searchDate.value = []
  Object.assign(queryForm, queryFormState)
  ajaxQuery()
}

async function ajaxQuery() {
  try {
    tableLoading.value = true
    const responseModel = await enterpriseApi.enterprisePage(queryForm)
    const list = responseModel.data.list
    total.value = responseModel.data.total
    tableData.value = list
  }
  catch (e) {
    sentry.captureError(e)
  }
  finally {
    tableLoading.value = false
  }
}

// --------------------------- 导出 ---------------------------
async function exportExcel() {
  await enterpriseApi.exportExcel(queryForm)
}

// --------------------------- 删除 ---------------------------

function confirmDelete(enterpriseId: string) {
  requireConfirmation({
    message: '删除后，该信息将不可恢复',
    header: '确定要删除吗？',
    acceptLabel: '删除',
    rejectLabel: '取消',
    accept: () => {
      del(enterpriseId)
    },
  })
}

async function del(enterpriseId: string) {
  try {
    AxLoading.show()
    await enterpriseApi.delete(enterpriseId)
    toast.add({ severity: 'success', summary: '删除成功', life: 3000 })
    ajaxQuery()
  }
  catch (e) {
    sentry.captureError(e)
  }
  finally {
    AxLoading.hide()
  }
}

// --------------------------- 增加、修改、详情 ---------------------------

const router = useRouter()
const operateRef = ref()
function add() {
  operateRef.value.showModal()
}

function update(enterpriseId: string) {
  operateRef.value.showModal(enterpriseId)
}

function detail(enterpriseId: string) {
  router.push({ path: '/oa/enterprise/enterprise-detail', query: { enterpriseId } })
}

onMounted(ajaxQuery)
</script>

<template>
  <Form v-privilege="'oa:enterprise:query'" class="mb-4">
    <div class="grid grid-cols-12 gap-4 items-end">
      <FormItem label="关键字" class="col-span-4">
        <InputText v-model="queryForm.keywords" placeholder="企业名称/联系人/联系电话" class="w-full" />
      </FormItem>

      <FormItem label="创建时间" class="col-span-4">
        <DatePicker
          v-model="searchDate"
          selection-mode="range"
          :presets="defaultTimeRanges"
          class="w-full"
          @update:model-value="dateChange"
        />
      </FormItem>

      <div class="col-span-4">
        <ButtonGroup>
          <Button severity="info" @click="onSearch">
            <template #icon>
              <Icon icon-type="SearchOutlined" />
            </template>
            查询
          </Button>
          <Button severity="secondary" @click="resetQuery">
            <template #icon>
              <Icon icon-type="ReloadOutlined" />
            </template>
            重置
          </Button>
        </ButtonGroup>
      </div>
    </div>
  </Form>

  <Card>
    <div class="flex justify-between mb-4">
      <div class="flex gap-2">
        <Button v-privilege="'oa:enterprise:add'" severity="info" @click="add()">
          <template #icon>
            <Icon icon-type="PlusOutlined" />
          </template>
          新建企业
        </Button>
        <Button v-privilege="'oa:enterprise:export'" severity="info" @click="exportExcel()">
          <template #icon>
            <Icon icon-type="FileExcelOutlined" />
          </template>
          导出数据（带水印）
        </Button>
      </div>
      <div>
        <TableOperator v-model="columns" :table-id="TABLE_ID_CONST.BUSINESS.OA.ENTERPRISE" :refresh="ajaxQuery" />
      </div>
    </div>

    <DataTable
      :value="tableData"
      :loading="tableLoading"
      scrollable
      scroll-height="flex"
      striped-rows
      :paginator="false"
      data-key="enterpriseId"
    >
      <Column field="enterpriseName" header="企业名称" style="min-width: 180px">
        <template #body="{ data }">
          <Button link class="p-0 text-blue-500 hover:text-blue-700" @click="detail(data.enterpriseId)">
            {{ data.enterpriseName }}
          </Button>
        </template>
      </Column>
      <Column field="unifiedSocialCreditCode" header="统一社会信用代码" style="min-width: 170px" />
      <Column field="type" header="企业类型" style="width: 100px">
        <template #body="{ data }">
          <span>{{ $smartEnumPlugin.getDescByValue('ENTERPRISE_TYPE_ENUM', data.type) }}</span>
        </template>
      </Column>
      <Column field="contact" header="联系人" style="width: 100px" />
      <Column field="contactPhone" header="联系人电话" style="width: 120px" />
      <Column field="email" header="邮箱" style="min-width: 100px" />
      <Column field="disabledFlag" header="状态" style="width: 80px">
        <template #body="{ data }">
          <Tag :severity="data.disabledFlag ? 'danger' : 'success'" :value="data.disabledFlag ? '禁用' : '启用'" />
        </template>
      </Column>
      <Column field="createUserName" header="创建人" style="width: 100px" />
      <Column field="createTime" header="创建时间" style="width: 150px" />
      <Column header="操作" style="width: 120px">
        <template #body="{ data }">
          <div class="flex gap-2">
            <Button
              v-privilege="'oa:enterprise:update'"
              link
              size="small"
              class="text-blue-500 hover:text-blue-700"
              @click="update(data.enterpriseId)"
            >
              编辑
            </Button>
            <Button
              v-privilege="'oa:enterprise:delete'"
              link
              size="small"
              class="text-red-500 hover:text-red-700"
              @click="confirmDelete(data.enterpriseId)"
            >
              删除
            </Button>
          </div>
        </template>
      </Column>
    </DataTable>

    <div class="mt-4">
      <Paginator
        :first="(queryForm.pageNum - 1) * queryForm.pageSize"
        :rows="queryForm.pageSize"
        :total-records="total"
        :rows-per-page-options="PAGE_SIZE_OPTIONS.map(Number)"
        template="FirstPageLink PrevPageLink CurrentPageReport NextPageLink LastPageLink RowsPerPageDropdown"
        current-page-report-template="共 {totalRecords} 条记录，第 {currentPage} 页，共 {totalPages} 页"
        @page="(event) => { queryForm.pageNum = event.page + 1; queryForm.pageSize = event.rows; ajaxQuery(); }"
      />
    </div>
    <EnterpriseOperate ref="operateRef" @refresh="ajaxQuery" />
  </Card>
</template>
