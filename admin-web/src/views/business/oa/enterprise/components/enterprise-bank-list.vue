<!--
  * 企业 银行列表
-->
<script setup lang="ts">
import { bankApi } from '@/api/business/oa/bank-api'
import { AxLoading } from '@/components/base/ax-loading'
import Icon from '@/components/base/Icon/Icon.vue'
import Form from '@/components/base/form/Form.vue'
import FormItem from '@/components/base/form/FormItem.vue'
import { PAGE_SIZE, PAGE_SIZE_OPTIONS } from '@/constants/common-const'
import { TABLE_ID_CONST } from '@/constants/support/table-id-const'
import { sentry } from '@/lib/sentry'
import Button from 'primevue/button'
import Card from 'primevue/card'
import Column from 'primevue/column'
import DataTable from 'primevue/datatable'
import DatePicker from 'primevue/datepicker'
import InputText from 'primevue/inputtext'
import Paginator from 'primevue/paginator'
import { useConfirm } from 'primevue/useconfirm'
import { useToast } from 'primevue/usetoast'
import { reactive, ref, watch } from 'vue'
import BankOperateModal from './enterprise-bank-operate-modal.vue'

interface Props {
  enterpriseId?: number | null
}

const props = withDefaults(defineProps<Props>(), {
  enterpriseId: null,
})

// PrimeVue hooks 初始化
const toast = useToast()
const { require: requireConfirmation } = useConfirm()

const queryFormState = {
  enterpriseId: props.enterpriseId,
  keywords: '',
  endTime: null,
  startTime: null,
  pageNum: 1,
  pageSize: PAGE_SIZE,
  searchCount: true,
}
const queryForm = reactive({ ...queryFormState })
const tableLoading = ref(false)
const tableData = ref<any[]>([])
const total = ref(0)
const operateModal = ref()

// 日期选择
const searchDate = ref<Date[] | null>(null)

function dateChange(value: Date[] | null) {
  if (value && value.length === 2) {
    queryForm.startTime = value[0].toISOString().split('T')[0]
    queryForm.endTime = value[1].toISOString().split('T')[0]
  }
  else {
    queryForm.startTime = null
    queryForm.endTime = null
  }
}

function resetQuery() {
  searchDate.value = null
  Object.assign(queryForm, queryFormState, { enterpriseId: props.enterpriseId })
  ajaxQuery()
}

function onSearch() {
  queryForm.pageNum = 1
  ajaxQuery()
}

async function ajaxQuery() {
  try {
    tableLoading.value = true
    const responseModel = await bankApi.pageQuery(queryForm)
    const list = responseModel.data.list
    total.value = responseModel.data.total
    tableData.value = list
  }
  catch (e) {
    sentry.captureError(e)
  }
  finally {
    tableLoading.value = false
  }
}

function confirmDelete(bankId: number) {
  requireConfirmation({
    message: '确定要删除吗？删除后，该信息将不可恢复',
    header: '确认删除',
    acceptLabel: '删除',
    rejectLabel: '取消',
    accept: () => {
      del(bankId)
    },
  })
}

async function del(bankId: number) {
  try {
    AxLoading.show()
    await bankApi.delete(bankId)
    toast.add({ severity: 'success', summary: '删除成功', life: 3000 })
    ajaxQuery()
  }
  catch (e) {
    sentry.captureError(e)
  }
  finally {
    AxLoading.hide()
  }
}

function addOrUpdate(rowData?: any) {
  operateModal.value.showModal(rowData)
}

watch(
  () => props.enterpriseId,
  (value) => {
    if (value) {
      queryForm.enterpriseId = value
      ajaxQuery()
    }
  },
  {
    immediate: true,
  },
)
</script>

<template>
  <Form>
    <Grid :cols="3" :gap="4">
      <GridCol>
        <FormItem label="关键字">
          <InputText v-model="queryForm.keywords" placeholder="开户银行/账户名称/账户/创建人" />
        </FormItem>
      </GridCol>

      <GridCol>
        <FormItem label="创建时间">
          <DatePicker v-model="searchDate" selection-mode="range" @update:model-value="dateChange" />
        </FormItem>
      </GridCol>

      <GridCol>
        <FormItem>
          <div class="flex gap-2">
            <Button @click="onSearch">
              <template #icon>
                <Icon icon-type="SearchOutlined" />
              </template>
              查询
            </Button>
            <Button @click="resetQuery">
              <template #icon>
                <Icon icon-type="ReloadOutlined" />
              </template>
              重置
            </Button>
          </div>
        </FormItem>
      </GridCol>
    </Grid>
  </Form>

  <Card>
    <div class="flex justify-between mb-4">
      <div class="flex gap-2">
        <Button @click="addOrUpdate()">
          <template #icon>
            <Icon icon-type="PlusOutlined" />
          </template>
          新建账户
        </Button>
      </div>
      <div>
        <TableOperator :table-id="TABLE_ID_CONST.BUSINESS.OA.ENTERPRISE_BANK" :refresh="ajaxQuery" />
      </div>
    </div>

    <DataTable
      :value="tableData"
      :loading="tableLoading"
      data-key="bankId"
      striped-rows
    >
      <Column field="bankName" header="开户银行" />
      <Column field="accountName" header="账户名称" />
      <Column field="accountNumber" header="账号" />
      <Column field="businessFlag" header="是否对公">
        <template #body="{ data }">
          {{ data.businessFlag ? '是' : '否' }}
        </template>
      </Column>
      <Column field="disabledFlag" header="状态">
        <template #body="{ data }">
          {{ data.disabledFlag ? '禁用' : '启用' }}
        </template>
      </Column>
      <Column field="remark" header="备注" />
      <Column field="createUserName" header="创建人" />
      <Column field="createTime" header="创建时间" />
      <Column header="操作">
        <template #body="{ data }">
          <div class="flex gap-2">
            <Button text @click="addOrUpdate(data)">
              编辑
            </Button>
            <Button text severity="danger" @click="confirmDelete(data.bankId)">
              删除
            </Button>
          </div>
        </template>
      </Column>
    </DataTable>

    <div class="mt-4">
      <Paginator
        :first="(queryForm.pageNum - 1) * queryForm.pageSize"
        :rows="queryForm.pageSize"
        :total-records="total"
        :rows-per-page-options="PAGE_SIZE_OPTIONS.map(Number)"
        template="FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink RowsPerPageDropdown CurrentPageReport"
        current-page-report-template="共 {totalRecords} 条记录"
        @page="(event) => { queryForm.pageNum = event.page + 1; queryForm.pageSize = event.rows; ajaxQuery(); }"
      />
    </div>

    <!-- 新建编辑modal -->
    <BankOperateModal ref="operateModal" :enterprise-id="props.enterpriseId" @reload-list="ajaxQuery" />
  </Card>
</template>
