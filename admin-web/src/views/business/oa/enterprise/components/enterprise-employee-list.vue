<!--
  * 企业 员工
-->
<script setup lang="ts">
import { enterpriseApi } from '@/api/business/oa/enterprise-api'
import { AxLoading } from '@/components/base/ax-loading'
import Icon from '@/components/base/Icon/Icon.vue'
import EmployeeTableSelectModal from '@/components/system/employee-table-select-modal/index.vue'
import { PAGE_SIZE, PAGE_SIZE_OPTIONS } from '@/constants/common-const'
import { sentry } from '@/lib/sentry'
import { useConfirm } from 'primevue/useconfirm'
import { useToast } from 'primevue/usetoast'
import { computed, reactive, ref, watch } from 'vue'

const props = defineProps({
  enterpriseId: {
    type: Number,
    default: null,
  },
})
const toast = useToast()
const { require: requireConfirmation } = useConfirm()

const columns = reactive([
  {
    title: '姓名',
    dataIndex: 'actualName',
  },
  {
    title: '手机号',
    dataIndex: 'phone',
    width: 120,
  },
  {
    title: '登录账号',
    dataIndex: 'loginName',
  },
  {
    title: '企业',
    dataIndex: 'enterpriseName',
    ellipsis: true,
  },
  {
    title: '部门',
    dataIndex: 'departmentName',
    ellipsis: true,
  },
  {
    title: '状态',
    dataIndex: 'disabledFlag',
    width: 80,
  },
  {
    title: '操作',
    dataIndex: 'operate',
    width: 60,
  },
])

// --------------------------- 查询 ---------------------------

const defaultQueryForm = {
  pageNum: 1,
  pageSize: PAGE_SIZE,
  enterpriseId: undefined,
  keyword: undefined,
}
// 查询表单
const queryForm = reactive({ ...defaultQueryForm })
const total = ref(0)
const tableData = ref([])
const tableLoading = ref(false)

function resetQueryEmployee() {
  queryForm.keyword = ''
  queryEmployee()
}

function onSearch() {
  queryForm.pageNum = 1
  queryEmployee()
}

async function queryEmployee() {
  try {
    tableLoading.value = true
    queryForm.enterpriseId = props.enterpriseId
    const res = await enterpriseApi.queryPageEmployeeList(queryForm)
    tableData.value = res.data.list
    total.value = res.data.total
  }
  catch (e) {
    sentry.captureError(e)
  }
  finally {
    tableLoading.value = false
  }
}

async function selectData(list) {
  if (_isEmpty(list)) {
    toast.add({ severity: 'warn', summary: '请选择员工', life: 3000 })
    return
  }
  AxLoading.show()
  try {
    const params = {
      employeeIdList: list,
      enterpriseId: props.enterpriseId,
    }
    await enterpriseApi.addEmployee(params)
    toast.add({ severity: 'success', summary: '添加成功', life: 3000 })
    await queryEmployee()
  }
  catch (e) {
    sentry.captureError(e)
  }
  finally {
    AxLoading.hide()
  }
}

// --------------------------- 添加员工 ---------------------------

// 添加员工
const selectEmployeeModal = ref()
async function addEmployee() {
  const res = await enterpriseApi.employeeList([props.enterpriseId])
  const selectedIdList = res.data.map(e => e.employeeId) || []
  selectEmployeeModal.value.showModal(selectedIdList)
}

// --------------------------- 删除 ---------------------------

// 删除员工方法
async function deleteEmployee(employeeId: number) {
  requireConfirmation({
    message: '确定要删除该企业下的员工么？',
    header: '提示',
    acceptLabel: '确定',
    rejectLabel: '取消',
    accept: async () => {
      AxLoading.show()
      try {
        const param = {
          employeeIdList: [employeeId],
          enterpriseId: props.enterpriseId,
        }
        await enterpriseApi.deleteEmployee(param)
        toast.add({ severity: 'success', summary: '移除成功', life: 3000 })
        await queryEmployee()
      }
      catch (e) {
        sentry.captureError(e)
      }
      finally {
        AxLoading.hide()
      }
    },
  })
}

// 批量删除
const selectedRowKeyList = ref([])
const hasSelected = computed(() => selectedRowKeyList.value.length > 0)
function onSelectChange(selectedRowKeys: any[]) {
  selectedRowKeyList.value = selectedRowKeys
}

// 批量移除
function batchDelete() {
  if (!hasSelected.value) {
    toast.add({ severity: 'warn', summary: '请选择要删除的员工', life: 3000 })
    return
  }
  requireConfirmation({
    message: '确定要删除该企业下的员工么？',
    header: '提示',
    acceptLabel: '确定',
    rejectLabel: '取消',
    accept: async () => {
      AxLoading.show()
      try {
        const params = {
          employeeIdList: selectedRowKeyList.value,
          enterpriseId: props.enterpriseId,
        }
        await enterpriseApi.deleteEmployee(params)
        toast.add({ severity: 'success', summary: '移除成功', life: 3000 })
        selectedRowKeyList.value = []
        await queryEmployee()
      }
      catch (e) {
        sentry.captureError(e)
      }
      finally {
        AxLoading.hide()
      }
    },
  })
}

watch(
  () => props.enterpriseId,
  (e) => {
    if (e) {
      queryEmployee()
    }
  },
  { immediate: true },
)
</script>

<template>
  <div>
    <div class="flex justify-between items-center mb-4">
      <div class="flex items-center gap-2">
        <span>关键字：</span>
        <InputText v-model="queryForm.keyword" placeholder="姓名/手机号/登录账号" class="w-64" />
        <Button severity="info" @click="onSearch">
          <template #icon>
            <Icon icon-type="SearchOutlined" />
          </template>
          搜索
        </Button>
        <Button severity="secondary" @click="resetQueryEmployee">
          <template #icon>
            <Icon icon-type="ReloadOutlined" />
          </template>
          重置
        </Button>
      </div>

      <div class="flex gap-2">
        <Button v-privilege="'oa:enterprise:addEmployee'" severity="info" @click="addEmployee">
          <template #icon>
            <Icon icon-type="PlusOutlined" />
          </template>
          添加员工
        </Button>
        <Button v-privilege="'oa:enterprise:deleteEmployee'" severity="danger" @click="batchDelete">
          <template #icon>
            <Icon icon-type="DeleteOutlined" />
          </template>
          批量移除
        </Button>
      </div>
    </div>

    <DataTable
      v-model:selection="selectedRowKeyList"
      :value="tableData"
      :loading="tableLoading"
      selection-mode="multiple"
      data-key="employeeId"
      striped-rows
      :paginator="false"
    >
      <Column selection-mode="multiple" />
      <Column field="actualName" header="姓名" />
      <Column field="phone" header="手机号" style="width: 120px" />
      <Column field="loginName" header="登录账号" />
      <Column field="enterpriseName" header="企业" />
      <Column field="departmentName" header="部门" />
      <Column field="disabledFlag" header="状态" style="width: 80px">
        <template #body="{ data }">
          <Tag :severity="data.disabledFlag ? 'danger' : 'success'" :value="data.disabledFlag ? '禁用' : '启用'" />
        </template>
      </Column>
      <Column header="操作" style="width: 80px">
        <template #body="{ data }">
          <Button
            v-privilege="'oa:enterprise:deleteEmployee'"
            link
            size="small"
            class="text-red-500 hover:text-red-700"
            @click="deleteEmployee(data.employeeId)"
          >
            移除
          </Button>
        </template>
      </Column>
    </DataTable>

    <div class="mt-4">
      <Paginator
        :first="(queryForm.pageNum - 1) * queryForm.pageSize"
        :rows="queryForm.pageSize"
        :total-records="total"
        :rows-per-page-options="PAGE_SIZE_OPTIONS.map(Number)"
        template="FirstPageLink PrevPageLink CurrentPageReport NextPageLink LastPageLink RowsPerPageDropdown"
        current-page-report-template="共 {totalRecords} 条记录，第 {currentPage} 页，共 {totalPages} 页"
        @page="(event) => { queryForm.pageNum = event.page + 1; queryForm.pageSize = event.rows; queryEmployee(); }"
      />
    </div>
    <EmployeeTableSelectModal ref="selectEmployeeModal" @select-data="selectData" />
  </div>
</template>
