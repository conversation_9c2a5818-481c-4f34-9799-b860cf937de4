<!--
  * 通知  查看记录
-->
<script setup lang="ts">
import { noticeApi } from '@/api/business/oa/notice-api'
import DepartmentTreeSelect from '@/components/system/department-tree-select/index.vue'
import { PAGE_SIZE, PAGE_SIZE_OPTIONS } from '@/constants/common-const'
import uaparser from 'ua-parser-js'
import { reactive, ref } from 'vue'

const props = defineProps({
  noticeId: {
    type: [Number, String],
  },
})

defineExpose({
  onSearch,
})

const tableData = ref([])
const total = ref(0)
const tableLoading = ref(false)

const defaultQueryForm = {
  noticeId: props.noticeId,
  departmentId: null,
  keywords: '',
  pageNum: 1,
  pageSize: PAGE_SIZE,
}

const queryForm = reactive({ ...defaultQueryForm })

function buildDeviceInfo(userAgent) {
  if (!userAgent) {
    return ''
  }

  const ua = uaparser(userAgent)
  const browser = ua.browser.name
  const os = ua.os.name
  return `${browser}/${os}/${ua.device.vendor ? ua.device.vendor + ua.device.model : ''}`
}

async function queryViewRecord() {
  try {
    tableLoading.value = true
    const result = await noticeApi.queryViewRecord(queryForm)

    for (const e of result.data.list) {
      e.firstDevice = buildDeviceInfo(e.firstUserAgent)
      e.lastDevice = buildDeviceInfo(e.lastUserAgent)
    }

    tableData.value = result.data.list
    total.value = result.data.total
  }
  catch (err) {
    console.log(err)
  }
  finally {
    tableLoading.value = false
  }
}
// 点击查询
function onSearch() {
  queryForm.pageNum = 1
  queryViewRecord()
}

// 点击重置
function resetQuery() {
  Object.assign(queryForm, defaultQueryForm)
  queryViewRecord()
}
</script>

<template>
  <div>
    <Form class="mb-4">
      <div class="grid grid-cols-12 gap-4 items-end">
        <FormItem label="部门" class="col-span-3">
          <DepartmentTreeSelect v-model="queryForm.departmentId" />
        </FormItem>
        <FormItem label="关键字" class="col-span-3">
          <InputText v-model="queryForm.keywords" placeholder="姓名/IP/设备" />
        </FormItem>
        <div class="col-span-6">
          <ButtonGroup>
            <Button severity="primary" @click="onSearch">
              <template #icon>
                <Icon icon-type="SearchOutlined" />
              </template>
              查询
            </Button>
            <Button severity="secondary" @click="resetQuery">
              <template #icon>
                <Icon icon-type="ReloadOutlined" />
              </template>
              重置
            </Button>
          </ButtonGroup>
        </div>
      </div>
    </Form>
    <DataTable
      :value="tableData"
      :loading="tableLoading"
      data-key="employeeId"
      size="small"
      class="border border-gray-300"
    >
      <Column field="employeeName" header="姓名">
        <template #body="{ data }">
          {{ data.employeeName }}({{ data.departmentName }})
        </template>
      </Column>
      <Column field="pageViewCount" header="查看次数" />
      <Column field="firstIp" header="首次查看设备">
        <template #body="{ data }">
          {{ data.firstIp }} ({{ data.firstDevice }})
        </template>
      </Column>
      <Column field="createTime" header="首次查看时间" />
      <Column field="lastIp" header="最后一次查看设备">
        <template #body="{ data }">
          {{ data.lastIp }} ({{ data.lastDevice }})
        </template>
      </Column>
      <Column field="updateTime" header="最后一次查看时间" />
    </DataTable>
    <div class="mt-4">
      <Paginator
        :first="(queryForm.pageNum - 1) * queryForm.pageSize"
        :rows="queryForm.pageSize"
        :total-records="total"
        :rows-per-page-options="PAGE_SIZE_OPTIONS"
        @page="(event) => { queryForm.pageNum = event.page + 1; queryForm.pageSize = event.rows; queryViewRecord(); }"
      />
    </div>
  </div>
</template>
