<!--
  * 通知  详情 （员工列表）
-->
<script setup lang="ts">
import type { TabViewChangeEvent } from 'primevue/tabview'
import { noticeApi } from '@/api/business/oa/notice-api'
import { PAGE_SIZE, PAGE_SIZE_OPTIONS } from '@/constants/common-const'
import { sentry } from '@/lib/sentry'
import { onMounted, reactive, ref } from 'vue'
import { useRouter } from 'vue-router'

interface NoticeItem {
  noticeId: string
  title: string
  noticeTypeName: string
  viewFlag: boolean
  userViewCount: number
  pageViewCount: number
  source: string
  author: string
  publishTime: string
}

interface QueryForm {
  noticeTypeId?: string
  keywords: string
  publishTimeBegin: string | null
  publishTimeEnd: string | null
  notViewFlag: boolean | null
  pageNum: number
  pageSize: number
}

// ------------------ 通知分类 ------------------

// 查询分类列表
const noticeTypeList = ref<Array<{ id: string, name: string }>>([])
async function queryNoticeTypeList() {
  try {
    const result = await noticeApi.getAllNoticeTypeList()
    noticeTypeList.value = result.data
  }
  catch (err) {
    sentry.captureError(err)
  }
}

// ------------------ 查询相关 ------------------

const queryFormState: QueryForm = {
  noticeTypeId: undefined,
  keywords: '',
  publishTimeBegin: null,
  publishTimeEnd: null,
  notViewFlag: null,
  pageNum: 1,
  pageSize: PAGE_SIZE,
}
const queryForm = reactive<QueryForm>({ ...queryFormState })

const tableData = ref<NoticeItem[]>([])
const total = ref<number>(0)
const tableLoading = ref<boolean>(false)

const publishDate = ref<Date[] | null>(null)

// 查询列表
async function queryNoticeList() {
  try {
    tableLoading.value = true
    const result = await noticeApi.queryEmployeeNotice(queryForm)
    tableData.value = result.data.list
    total.value = result.data.total
  }
  catch (err) {
    sentry.captureError(err)
  }
  finally {
    tableLoading.value = false
  }
}

function changeNotViewFlag(event: TabViewChangeEvent) {
  queryForm.notViewFlag = event.index === 0 ? null : true
  queryForm.pageNum = 1
  queryNoticeList()
}

function onSearch() {
  queryForm.pageNum = 1
  queryNoticeList()
}

function onReload() {
  Object.assign(queryForm, queryFormState)
  publishDate.value = null
  queryNoticeList()
}

onMounted(() => {
  queryNoticeTypeList()
  queryNoticeList()
})

function publishDateChange(value: Date | Date[] | null) {
  if (Array.isArray(value) && value.length === 2 && value[0] && value[1]) {
    queryForm.publishTimeBegin = value[0].toISOString().split('T')[0]
    queryForm.publishTimeEnd = value[1].toISOString().split('T')[0]
  }
  else {
    queryForm.publishTimeBegin = null
    queryForm.publishTimeEnd = null
  }
}

// ------------------ 详情 ------------------

// 进入详情
const router = useRouter()
function toDetail(noticeId: string) {
  router.push({
    path: '/oa/notice/notice-employee-detail',
    query: { noticeId },
  })
}
</script>

<template>
  <Form class="mb-4">
    <Grid :cols="3" :gap="4">
      <GridCol>
        <FormItem label="关键字">
          <InputText v-model="queryForm.keywords" placeholder="标题、作者、来源、文号" />
        </FormItem>
      </GridCol>

      <GridCol>
        <FormItem label="发布时间">
          <DateRangePicker v-model="publishDate" @date-select="publishDateChange" />
        </FormItem>
      </GridCol>

      <GridCol>
        <FormItem>
          <div class="flex gap-2">
            <Button
              severity="info"
              :loading="tableLoading"
              @click="onSearch"
            >
              <template #icon>
                <Icon icon-type="SearchOutlined" />
              </template>
              查询
            </Button>
            <Button
              severity="secondary"
              outlined
              @click="onReload"
            >
              <template #icon>
                <Icon icon-type="ReloadOutlined" />
              </template>
              重置
            </Button>
          </div>
        </FormItem>
      </GridCol>
    </Grid>
  </Form>

  <Card class="overflow-y-auto">
    <TabView @tab-change="changeNotViewFlag">
      <TabPanel header="全部" />
      <TabPanel header="未读" />
    </TabView>

    <DataTable
      :value="tableData"
      :loading="tableLoading"
      responsive-layout="scroll"
      class="mt-4"
      striped-rows
      hover
    >
      <Column field="title" header="标题">
        <template #body="{ data }: { data: NoticeItem }">
          <a
            :class="data.viewFlag ? 'text-gray-500' : 'text-blue-600'"
            class="cursor-pointer hover:underline"
            @click="toDetail(data.noticeId)"
          >
            【{{ data.noticeTypeName }}】{{ data.title }}
            <Tag
              v-if="!data.viewFlag"
              severity="danger"
              value="未读"
              class="ml-2"
            />
            <Tag
              v-else
              severity="secondary"
              value="已读"
              class="ml-2"
            />
          </a>
        </template>
      </Column>
      <Column field="pageViewCount" header="访问量" style="width: 120px">
        <template #body="{ data }: { data: NoticeItem }">
          <div class="text-sm">
            <div>{{ data.userViewCount }}人</div>
            <div class="text-gray-500">
              {{ data.pageViewCount }}次
            </div>
          </div>
        </template>
      </Column>
      <Column field="source" header="来源" style="width: 150px" />
      <Column field="author" header="作者" style="width: 100px" />
      <Column field="publishTime" header="发布时间" style="width: 160px" />
    </DataTable>
  </Card>
  <div class="flex justify-between items-center mt-4">
    <div class="text-sm text-gray-600">
      共 {{ total }} 条记录
    </div>
    <Paginator
      :first="(queryForm.pageNum - 1) * queryForm.pageSize"
      :rows="queryForm.pageSize"
      :total-records="total"
      :rows-per-page-options="PAGE_SIZE_OPTIONS.map(Number)"
      template="FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink RowsPerPageDropdown"
      @page="(event) => { queryForm.pageNum = event.page + 1; queryForm.pageSize = event.rows; queryNoticeList(); }"
    />
  </div>
</template>
