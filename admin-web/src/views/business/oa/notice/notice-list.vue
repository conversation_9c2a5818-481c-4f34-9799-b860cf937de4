<!--
  * 通知  管理列表
-->

<script setup lang="ts">
import { noticeApi } from '@/api/business/oa/notice-api'
import { PAGE_SIZE, PAGE_SIZE_OPTIONS } from '@/constants/common-const'
import { TABLE_ID_CONST } from '@/constants/support/table-id-const'
import { sentry } from '@/lib/sentry'
import { useConfirm } from 'primevue/useconfirm'
import { useToast } from 'primevue/usetoast'
import { onMounted, reactive, ref } from 'vue'
import { useRouter } from 'vue-router'
import NoticeFormDrawer from './components/notice-form-drawer.vue'

const queryFormState = {
  noticeTypeId: undefined, // 分类
  keywords: '', // 标题、作者、来源
  documentNumber: '', // 文号
  createUserId: undefined, // 创建人
  deletedFlag: undefined, // 删除标识
  createTimeBegin: null, // 创建-开始时间
  createTimeEnd: null, // 创建-截止时间
  publishTimeBegin: null, // 发布-开始时间
  publishTimeEnd: null, // 发布-截止时间
  pageNum: 1,
  pageSize: PAGE_SIZE,
}
const queryForm = reactive({ ...queryFormState })

const tableColumns = ref([
  {
    title: `标题`,
    dataIndex: 'title',
    width: 300,
    ellipsis: true,
  },
  {
    title: `文号`,
    dataIndex: 'documentNumber',
    width: 100,
    ellipsis: true,
  },
  {
    title: '分类',
    dataIndex: 'noticeTypeName',
    width: 60,
    ellipsis: true,
  },
  {
    title: `作者`,
    dataIndex: 'author',
    width: 80,
    ellipsis: true,
  },
  {
    title: `来源`,
    dataIndex: 'source',
    width: 90,
    ellipsis: true,
  },

  {
    title: '可见范围',
    dataIndex: 'allVisibleFlag',
    width: 90,
    ellipsis: true,
  },
  {
    title: '发布',
    dataIndex: 'publishFlag',
    width: 80,
  },
  {
    title: '删除',
    dataIndex: 'deletedFlag',
    width: 80,
  },
  {
    title: '发布时间',
    dataIndex: 'publishTime',
    width: 150,
  },
  {
    title: '页面浏览量',
    dataIndex: 'pageViewCount',
    width: 90,
  },
  {
    title: '用户浏览量',
    dataIndex: 'userViewCount',
    width: 90,
  },
  {
    title: '创建人',
    dataIndex: 'createUserName',
    width: 80,
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    width: 150,
  },
  {
    title: '操作',
    dataIndex: 'action',
    fixed: 'right',
    width: 90,
  },
])

// ------------------ 通知分类 ------------------

// 查询分类列表
const noticeTypeList = ref([])
async function queryNoticeTypeList() {
  try {
    const result = await noticeApi.getAllNoticeTypeList()
    noticeTypeList.value = result.data
  }
  catch (err) {
    sentry.captureError(err)
  }
}

// ------------------ 查询相关 ------------------

const tableData = ref([])
const total = ref(0)
const tableLoading = ref(false)

onMounted(() => {
  queryNoticeTypeList()
  queryNoticeList()
})

// 查询列表
async function queryNoticeList() {
  try {
    tableLoading.value = true
    const result = await noticeApi.queryNotice(queryForm)
    tableData.value = result.data.list
    total.value = result.data.total
  }
  catch (err) {
    sentry.captureError(err)
  }
  finally {
    tableLoading.value = false
  }
}

// 点击查询
function onSearch() {
  queryForm.pageNum = 1
  queryNoticeList()
}

// 发布日期选择
const publishDate = ref(null)
// 创建日期选择
const createDate = ref(null)

// 点击重置
function onReload() {
  Object.assign(queryForm, queryFormState)
  publishDate.value = null
  createDate.value = null
  queryNoticeList()
}
function publishDateChange(value: Date | Date[] | null) {
  if (Array.isArray(value) && value.length === 2 && value[0] && value[1]) {
    queryForm.publishTimeBegin = value[0].toISOString().split('T')[0]
    queryForm.publishTimeEnd = value[1].toISOString().split('T')[0]
  }
  else {
    queryForm.publishTimeBegin = null
    queryForm.publishTimeEnd = null
  }
}
function createDateChange(value: Date | Date[] | null) {
  if (Array.isArray(value) && value.length === 2 && value[0] && value[1]) {
    queryForm.createTimeBegin = value[0].toISOString().split('T')[0]
    queryForm.createTimeEnd = value[1].toISOString().split('T')[0]
  }
  else {
    queryForm.createTimeBegin = null
    queryForm.createTimeEnd = null
  }
}

// ------------------ 新建、编辑 ------------------

// 新建、编辑
const noticeFormDrawer = ref()
function addOrUpdate(noticeId) {
  noticeFormDrawer.value.showModal(noticeId)
}

// ------------------ 删除 ------------------

// 初始化 PrimeVue hooks
const toast = useToast()
const { require: requireConfirmation } = useConfirm()

// 删除
function onDelete(noticeId) {
  requireConfirmation({
    message: '确认删除此数据吗?',
    header: '提示',
    accept: () => {
      deleteNotice(noticeId)
    },
  })
}

// 删除API
async function deleteNotice(noticeId) {
  try {
    tableLoading.value = true
    await noticeApi.deleteNotice(noticeId)
    toast.add({ severity: 'success', summary: '删除成功', life: 3000 })
    queryNoticeList()
  }
  catch (err) {
    sentry.captureError(err)
  }
  finally {
    tableLoading.value = false
  }
}

// ------------------ 详情 ------------------

// 进入详情
const router = useRouter()
function toDetail(noticeId) {
  router.push({
    path: '/oa/notice/notice-detail',
    query: { noticeId },
  })
}
</script>

<template>
  <Form v-privilege="'oa:notice:query'" class="p-4 bg-white rounded-lg mb-4">
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
      <FormItem label="分类">
        <Dropdown v-model="queryForm.noticeTypeId" :options="noticeTypeList" option-label="noticeTypeName" option-value="noticeTypeId" placeholder="分类" show-clear class="w-full" />
      </FormItem>

      <FormItem label="关键字">
        <InputText v-model="queryForm.keywords" placeholder="标题、作者、来源" class="w-full" />
      </FormItem>

      <FormItem label="文号">
        <InputText v-model="queryForm.documentNumber" placeholder="文号" class="w-full" />
      </FormItem>

      <FormItem label="创建人">
        <InputText v-model="queryForm.createUserId" placeholder="创建人" class="w-full" />
      </FormItem>

      <FormItem label="是否删除">
        <BooleanSelect v-model="queryForm.deletedFlag" class="w-full" />
      </FormItem>

      <FormItem label="发布时间">
        <DateRangePicker v-model="publishDate" class="w-full" @update:model-value="publishDateChange" />
      </FormItem>

      <FormItem label="创建时间">
        <DateRangePicker v-model="createDate" class="w-full" @update:model-value="createDateChange" />
      </FormItem>

      <FormItem>
        <div class="flex gap-2">
          <Button severity="primary" @click="onSearch">
            <template #icon>
              <Icon icon-type="SearchOutlined" />
            </template>
            查询
          </Button>
          <Button @click="onReload">
            <template #icon>
              <Icon icon-type="ReloadOutlined" />
            </template>
            重置
          </Button>
        </div>
      </FormItem>
    </div>
  </Form>

  <Card class="bg-white">
    <div class="flex justify-between mb-4">
      <div>
        <Button v-privilege="'oa:notice:add'" severity="primary" @click="addOrUpdate()">
          <template #icon>
            <Icon icon-type="PlusOutlined" />
          </template>
          新建
        </Button>
      </div>
      <div>
        <TableOperator v-model="tableColumns" :table-id="TABLE_ID_CONST.BUSINESS.OA.NOTICE" :refresh="queryNoticeList" />
      </div>
    </div>

    <DataTable
      :value="tableData"
      :loading="tableLoading"
      striped-rows
      responsive-layout="scroll"
      scroll-height="400px"
      data-key="noticeId"
    >
      <Column field="title" header="标题" style="width: 300px">
        <template #body="{ data }">
          <a class="text-blue-600 hover:text-blue-800 cursor-pointer" @click="toDetail(data.noticeId)">{{ data.title }}</a>
        </template>
      </Column>
      <Column field="documentNumber" header="文号" style="width: 100px" />
      <Column field="noticeTypeName" header="分类" style="width: 60px" />
      <Column field="author" header="作者" style="width: 80px" />
      <Column field="source" header="来源" style="width: 90px" />
      <Column field="allVisibleFlag" header="可见范围" style="width: 90px">
        <template #body="{ data }">
          {{ data.allVisibleFlag ? '全部可见' : '部分可见' }}
        </template>
      </Column>
      <Column field="publishFlag" header="发布" style="width: 80px">
        <template #body="{ data }">
          {{ data.publishFlag ? '已发布' : '待发布' }}
        </template>
      </Column>
      <Column field="deletedFlag" header="删除" style="width: 80px">
        <template #body="{ data }">
          <Tag v-if="data.deletedFlag" severity="danger" value="已删除" />
          <Tag v-else severity="success" value="未删除" />
        </template>
      </Column>
      <Column field="publishTime" header="发布时间" style="width: 150px" />
      <Column field="pageViewCount" header="页面浏览量" style="width: 90px" />
      <Column field="userViewCount" header="用户浏览量" style="width: 90px" />
      <Column field="createUserName" header="创建人" style="width: 80px" />
      <Column field="createTime" header="创建时间" style="width: 150px" />
      <Column header="操作" style="width: 90px" frozen-pos="right">
        <template #body="{ data }">
          <div class="flex gap-2">
            <Button v-privilege="'oa:notice:update'" text @click="addOrUpdate(data.noticeId)">
              编辑
            </Button>
            <Button v-privilege="'oa:notice:delete'" text severity="danger" @click="onDelete(data.noticeId)">
              删除
            </Button>
          </div>
        </template>
      </Column>
    </DataTable>

    <div class="mt-4">
      <Paginator
        :first="(queryForm.pageNum - 1) * queryForm.pageSize"
        :rows="queryForm.pageSize"
        :total-records="total"
        :rows-per-page-options="PAGE_SIZE_OPTIONS.map(Number)"
        template="FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink RowsPerPageDropdown CurrentPageReport"
        current-page-report-template="共 {totalRecords} 条记录"
        @page="(event) => { queryForm.pageNum = event.page + 1; queryForm.pageSize = event.rows; queryNoticeList(); }"
      />
    </div>
  </Card>

  <NoticeFormDrawer ref="noticeFormDrawer" @reload-list="queryNoticeList" />
</template>
