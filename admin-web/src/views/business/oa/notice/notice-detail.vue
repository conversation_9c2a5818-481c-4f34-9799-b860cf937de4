<!--
  * 通知  详情
-->
<script setup lang="ts">
import { noticeApi } from '@/api/business/oa/notice-api'
import { AxLoading } from '@/components/base/ax-loading'
import Icon from '@/components/base/Icon/Icon.vue'
import FilePreviewModal from '@/components/utils/file-preview-modal/index.vue'
import { sentry } from '@/lib/sentry'
import { onMounted, ref } from 'vue'
import { useRoute } from 'vue-router'
import NoticeFormDrawer from './components/notice-form-drawer.vue'
import NoticeViewRecordList from './components/notice-view-record-list.vue'

defineProps({
  newsType: {
    type: Number,
  },
})

const route = useRoute()

const activeIndex = ref(0)

const noticeDetail = ref({})
const noticeViewRecordList = ref()

onMounted(() => {
  if (route.query.noticeId) {
    queryNoticeDetail()
    noticeViewRecordList.value.onSearch()
  }
})

// 查询详情
async function queryNoticeDetail() {
  try {
    AxLoading.show()
    const result = await noticeApi.getUpdateNoticeInfo(route.query.noticeId)
    noticeDetail.value = result.data
  }
  catch (err) {
    sentry.captureError(err)
  }
  finally {
    AxLoading.hide()
  }
}

// 点击编辑
const noticeFormDrawerRef = ref()
function onEdit() {
  noticeFormDrawerRef.value.showModal(noticeDetail.value.noticeId)
}

// 预览附件
const filePreviewRef = ref()
function onPrevFile(fileItem) {
  filePreviewRef.value.showPreview(fileItem)
}
</script>

<template>
  <Card class="mb-4">
    <div class="flex justify-between items-center mb-4">
      <h1 class="text-xl font-bold">
        {{ noticeDetail.title }}
      </h1>
      <Button v-if="!noticeDetail.publishFlag" severity="info" size="small" @click="onEdit">
        <template #icon>
          <Icon icon-type="EditOutlined" />
        </template>
        编辑
      </Button>
    </div>
    <Descriptions :column="4">
      <DescriptionsItem label="分类">
        {{ noticeDetail.noticeTypeName }}
      </DescriptionsItem>
      <DescriptionsItem label="文号">
        {{ noticeDetail.documentNumber }}
      </DescriptionsItem>
      <DescriptionsItem label="来源">
        {{ noticeDetail.source }}
      </DescriptionsItem>
      <DescriptionsItem label="作者">
        {{ noticeDetail.author }}
      </DescriptionsItem>
      <DescriptionsItem label="页面浏览量">
        {{ noticeDetail.pageViewCount }}
      </DescriptionsItem>
      <DescriptionsItem label="用户浏览量">
        {{ noticeDetail.userViewCount }}
      </DescriptionsItem>
      <DescriptionsItem label="创建时间">
        {{ noticeDetail.createTime }}
      </DescriptionsItem>
      <DescriptionsItem label="发布时间">
        {{ noticeDetail.publishTime }}
      </DescriptionsItem>
      <DescriptionsItem label="定时发布">
        {{ noticeDetail.publishFlag ? '已发布' : '待发布' }}
      </DescriptionsItem>
      <DescriptionsItem label="删除状态">
        {{ noticeDetail.deletedFlag ? '已删除' : '未删除' }}
      </DescriptionsItem>
      <DescriptionsItem v-if="!$lodash.isEmpty(noticeDetail.attachment)" label="附件">
        <div class="file-list">
          <a v-for="item in noticeDetail.attachment" :key="item.fileId" class="file-item" @click="onPrevFile(item)">{{ item.fileName }}</a>
        </div>
      </DescriptionsItem>
      <DescriptionsItem label="可见范围">
        <template v-if="noticeDetail.allVisibleFlag">
          全部可见
        </template>
        <div class="visible-list">
          <div v-for="item in noticeDetail.visibleRangeList" :key="item.dataId" class="visible-item">
            {{ item.dataName }}
          </div>
        </div>
      </DescriptionsItem>
    </Descriptions>
  </Card>

  <Card>
    <TabView v-model:active-index="activeIndex">
      <TabPanel header="内容">
        <div class="content-html" v-html="noticeDetail.contentHtml" />
      </TabPanel>
      <TabPanel header="查看记录">
        <NoticeViewRecordList ref="noticeViewRecordList" :notice-id="route.query.noticeId" />
      </TabPanel>
      <TabPanel header="操作记录">
        <!-- 操作记录内容 -->
      </TabPanel>
    </TabView>
  </Card>

  <!-- 编辑 -->
  <NoticeFormDrawer ref="noticeFormDrawerRef" @reload-list="queryNoticeDetail" />

  <!-- 预览附件 -->
  <FilePreviewModal ref="filePreviewRef" />
</template>

<style scoped>
.file-list {
  @apply w-full flex flex-wrap;
}
.file-item {
  @apply block mr-2.5 text-blue-600 hover:text-blue-800 cursor-pointer;
}
.visible-list {
  @apply flex flex-wrap;
}
.visible-item {
  @apply mr-2.5 text-gray-600;
}
.content-html {
  @apply prose max-w-none;
}
.content-html img {
  @apply max-w-full;
}
</style>
