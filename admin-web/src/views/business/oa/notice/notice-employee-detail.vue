<!--
  * 通知  详情 （员工）
-->
<script setup lang="ts">
import { noticeApi } from '@/api/business/oa/notice-api'
import { AxLoading } from '@/components/base/ax-loading'
import FilePreview from '@/components/utils/file-preview/index.vue'
import { sentry } from '@/lib/sentry'
import { onMounted, ref } from 'vue'
import { useRoute } from 'vue-router'
import NoticeViewRecordList from './components/notice-view-record-list.vue'

const route = useRoute()

const noticeDetail = ref({})

onMounted(() => {
  if (route.query.noticeId) {
    queryNoticeDetail()
  }
})

const noticeViewRecordList = ref()

// 查询详情
async function queryNoticeDetail() {
  try {
    AxLoading.show()
    const result = await noticeApi.view(route.query.noticeId)
    noticeDetail.value = result.data

    noticeViewRecordList.value.onSearch()
  }
  catch (err) {
    sentry.captureError(err)
  }
  finally {
    AxLoading.hide()
  }
}

// 点击编辑
const noticeFormDrawerRef = ref()
function onEdit() {
  noticeFormDrawerRef.value.showModal(noticeDetail.value.noticeId)
}

// 打印
function print() {
  const bdhtml = window.document.body.innerHTML
  const sprnstr = '<!--startprint-->' // 必须在页面添加<!--startprint-->和<!--endprint-->而且需要打印的内容必须在它们之间
  const eprnstr = '<!--endprint-->'
  let prnhtml = bdhtml.substr(bdhtml.indexOf(sprnstr))
  prnhtml = prnhtml.substring(0, prnhtml.indexOf(eprnstr))
  const newWin = window.open('') // 新打开一个空窗口
  newWin.document.body.innerHTML = prnhtml
  newWin.document.close() // 在IE浏览器中使用必须添加这一句
  newWin.focus() // 在IE浏览器中使用必须添加这一句
  newWin.print() // 打印
  newWin.close() // 关闭窗口
}
</script>

<template>
  <Card>
    <div>
      <div class="content-header">
        <!-- startprint -->
        <div class="content-header-title">
          {{ noticeDetail.title }}
        </div>
        <div class="content-header-info">
          <span v-show="noticeDetail.author">作者：{{ noticeDetail.author }}</span>
          <span v-show="noticeDetail.source">来源：{{ noticeDetail.source }}</span>
          <span>发布旷间：{{ noticeDetail.publishTime }}</span>
          <span>阅读量：{{ noticeDetail.pageViewCount }}</span>
          <span @click="print">【打印本页】</span>
        </div>
      </div>
      <div class="content-html" v-html="noticeDetail.contentHtml" />
      <!-- endprint -->
    </div>
    <Divider />
    <div>
      附件：
      <FilePreview v-if="!$lodash.isEmpty(noticeDetail.attachment)" :file-list="noticeDetail.attachment" />
      <span v-else>无</span>
    </div>
  </Card>

  <Card class="mt-2">
    <template #title>
      记录
    </template>
    <NoticeViewRecordList ref="noticeViewRecordList" :notice-id="route.query.noticeId" />
  </Card>
</template>

<style lang="less" scoped>
  :deep(.ant-descriptions-item-content) {
    flex: 1;
    overflow: hidden;
  }
  .file-list {
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    .file-item {
      display: block;
      margin-right: 10px;
    }
  }
  .visible-list {
    display: flex;
    flex-wrap: wrap;
    .visible-item {
      margin-right: 10px;
      color: #666;
    }
  }
  .content-header {
    .content-header-title {
      margin: 10px 0px;
      font-size: 18px;
      font-weight: bold;
      text-align: center;
    }
    .content-header-info {
      margin: 10px 0px;
      font-size: 14px;
      color: #888;
      text-align: center;
      span {
        margin: 0 10px;
        cursor: pointer;
      }
    }
  }
  .content-html {
    img {
      max-width: 100%;
    }
  }
</style>
