<script lang="ts" setup>
import { DictSelect } from '@/components/utils/Dict'
import StockDetail from './components/StockDetail.vue'
import StockForm from './components/StockForm.vue'
import { stockService } from './service/StockService'

// 提供 service
stockService.provide()
</script>

<template>
  <!---------- 查询参数 begin ----------->
  <Card>
    <Form :ref="stockService.tableFormRef" v-privilege="'stock:query'" layout="vertical">
      <Grid>
        <GridCol>
          <FormItem label="仓库名称" name="warehouseName">
            <!-- <InputText v-model="stockService.queryParam.warehouseName" placeholder="请输入仓库名称" /> -->
            <WarehouseSearch />
          </FormItem>
        </GridCol>
        <GridCol>
          <FormItem label="商品名称" name="productName">
            <InputText placeholder="请输入商品名称" />
          </FormItem>
        </GridCol>
        <GridCol>
          <FormItem label="SKU编码" name="skuCode">
            <InputText placeholder="请输入SKU编码" />
          </FormItem>
        </GridCol>
        <GridCol>
          <FormItem label="库存状态" name="stockStatus">
            <DictSelect key-code="stock_status" placeholder="请选择库存状态" />
          </FormItem>
        </GridCol>
        <GridCol>
          <FormItem label="当前库存" name="currentStock">
            <InputNumber placeholder="请输入当前库存" :min="0" />
          </FormItem>
        </GridCol>
        <GridCol>
          <FormItem label="出入库日期" name="lastInDateRange">
            <DateRangePicker />
          </FormItem>
        </GridCol>
      </Grid>
    </Form>
  </Card>
  <!---------- 查询参数 end ----------->
  <Card :hoverable="true">
    <!---------- 表格操作行 begin ----------->
    <FlexRow>
      <FlexRow>
        <TableOperateButtons
          add-config="stock:add"
          batch-delete-config="stock:delete"
          import-config="stock:import"
          export-config="stock:export"
        />
      </FlexRow>
      <ButtonGroup>
        <IconButton label="查询" icon-type="SearchOutlined" type="primary" @click="stockService.onSearch" />
        <IconButton label="重置" icon-type="ReloadOutlined" @click="stockService.resetQuery" />
      </ButtonGroup>
    </FlexRow>
    <!---------- 表格操作行 end ----------->
    <br>
    <!-- 使用组件化的表格 -->
    <AxTable>
      <template #bodyCell="{ column, record }">
        <!-- 只为操作列添加模板插槽 -->
        <template v-if="column.dataIndex === 'action'">
          <FlexRow justify="start">
            <IconAnchor
              v-privilege="'stock:view'" label="查看" icon-type="EyeOutlined"
              @click="stockService.openDetailView(record.id!)"
            />
          </FlexRow>
        </template>
      </template>
    </AxTable>
  </Card>
  <!-- 导入弹窗 -->
  <AxImportExcelModal />
  <!-- 库存表单 -->
  <StockForm />
  <!-- 库存详情 -->
  <StockDetail />
</template>
