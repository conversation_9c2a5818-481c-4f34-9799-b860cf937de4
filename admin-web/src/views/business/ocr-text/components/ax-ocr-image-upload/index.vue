<!--
  * OCR图片上传组件
-->
<script setup lang="ts">
import { useToast } from 'primevue/usetoast'
import { ref } from 'vue'

interface CustomFileItem {
  uid: string
  name: string
  url?: string
  size?: number
  type?: string
  originFileObj?: File
}

interface UploadEvent {
  files: File[]
}

const props = defineProps({
  maxCount: {
    type: Number,
    default: 10, // 默认允许上传10张图片
  },
  accept: {
    type: String,
    default: '.jpg,.jpeg,.png,.gif',
  },
})

const emit = defineEmits(['change'])

const toast = useToast()

// 图片类型的后缀名
const imgFileType = ['jpg', 'jpeg', 'png', 'gif']

// 预览相关
const previewVisible = ref(false)
const previewImage = ref('')
const fileList = ref<CustomFileItem[]>([])

// 检查文件类型是否为图片
function isImage(file: File): boolean {
  const suffix = file.name.substring(file.name.lastIndexOf('.') + 1).toLowerCase()
  return imgFileType.includes(suffix)
}

// 文件选择处理
function handleSelect(event: UploadEvent) {
  const files = event.files

  for (const file of files) {
    // 检查文件类型
    if (!isImage(file)) {
      toast.add({ severity: 'error', summary: '文件类型错误', detail: '只能上传图片格式文件！' })
      continue
    }

    // 检查文件大小
    const isLt5M = file.size / 1024 / 1024 < 5
    if (!isLt5M) {
      toast.add({ severity: 'error', summary: '文件过大', detail: '图片大小不能超过5MB！' })
      continue
    }

    // 检查数量限制
    if (fileList.value.length >= props.maxCount) {
      toast.add({ severity: 'error', summary: '数量限制', detail: `最多只能上传${props.maxCount}张图片！` })
      break
    }

    // 创建本地预览URL
    const localUrl = URL.createObjectURL(file)

    // 添加到文件列表
    const uploadFile: CustomFileItem = {
      uid: `local_${Date.now()}_${fileList.value.length}`,
      name: file.name,
      url: localUrl,
      size: file.size,
      type: file.type,
      originFileObj: file,
    }

    fileList.value.push(uploadFile)
  }

  // 触发变化事件
  emit('change', fileList.value)
}

// 预览图片
function handlePreview(file: CustomFileItem) {
  if (file && file.url) {
    previewImage.value = file.url
    previewVisible.value = true
  }
  else {
    toast.add({ severity: 'warn', summary: '预览失败', detail: '无法预览此图片' })
  }
}

// 移除图片
function handleRemove(file: CustomFileItem) {
  const index = fileList.value.findIndex(item => item.uid === file.uid)
  if (index !== -1) {
    // 释放对象URL
    if (file.url && file.url.startsWith('blob:')) {
      URL.revokeObjectURL(file.url)
    }
    fileList.value.splice(index, 1)
    emit('change', fileList.value)
  }
}

// 获取文件列表
function getFileList(): File[] {
  return fileList.value
    .filter(file => file.originFileObj && file.originFileObj instanceof File)
    .map(file => file.originFileObj as File)
}

// 清空文件列表
function clearFiles() {
  fileList.value.forEach((file) => {
    if (file.url && file.url.startsWith('blob:')) {
      URL.revokeObjectURL(file.url)
    }
  })
  fileList.value = []
  emit('change', fileList.value)
}

// 对外暴露方法
defineExpose({
  fileList,
  getFileList,
  clearFiles,
})
</script>

<template>
  <div class="ocr-image-upload">
    <div class="flex flex-wrap gap-4">
      <!-- 已上传的图片 -->
      <div v-for="file in fileList" :key="file.uid" class="relative">
        <div class="w-26 h-26 border-2 border-dashed border-gray-300 rounded-lg overflow-hidden cursor-pointer hover:border-blue-400" @click="handlePreview(file)">
          <img :src="file.url" :alt="file.name" class="w-full h-full object-cover">
        </div>
        <Button
          icon="pi pi-times"
          class="absolute -top-2 -right-2 w-6 h-6 p-0"
          severity="danger"
          size="small"
          rounded
          @click="handleRemove(file)"
        />
      </div>

      <!-- 上传按钮 -->
      <div v-if="fileList.length < maxCount" class="w-26 h-26">
        <FileUpload
          mode="basic"
          name="file"
          :accept="accept"
          :multiple="true"
          :auto="false"
          choose-label="上传图片"
          class="upload-button"
          @select="handleSelect"
        >
          <template #empty>
            <div class="w-26 h-26 border-2 border-dashed border-gray-300 rounded-lg flex flex-col items-center justify-center cursor-pointer hover:border-blue-400 hover:bg-blue-50 transition-colors">
              <Icon icon-type="PlusOutlined" class="text-2xl text-gray-400 mb-1" />
              <span class="text-sm text-gray-500">上传图片</span>
            </div>
          </template>
        </FileUpload>
      </div>
    </div>

    <Dialog v-model:visible="previewVisible" modal :dismissable-mask="true" class="w-auto max-w-4xl">
      <template #header>
        <span>图片预览</span>
      </template>
      <div class="flex justify-center">
        <Image :src="previewImage" alt="预览图片" preview class="max-w-full max-h-96" />
      </div>
    </Dialog>
  </div>
</template>

<style scoped>
.ocr-image-upload :deep(.p-fileupload-choose) {
  padding: 0;
  border: none;
  background: transparent;
}

.upload-button :deep(.p-fileupload-content) {
  border: none;
  padding: 0;
}
</style>
