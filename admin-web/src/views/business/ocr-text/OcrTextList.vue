<!--
  * OCR图片转文字功能
-->
<script lang="ts" setup>
import { useToast } from 'primevue/usetoast'
import { ref } from 'vue'
import OcrImageUpload from './components/ax-ocr-image-upload/index.vue'
import { ocrTextService } from './service/OcrTextService'

// 上传界面
const uploadVisible = ref(false)
const uploadRef = ref()
const isProcessing = ref(false)
const resultText = ref<string>('')
const batchResults = ref<Array<{ text: string, fileName: string }>>([])
const showBatchResults = ref(false)

// Toast
const toast = useToast()

// 打开上传弹窗
function openUploadModal() {
  uploadVisible.value = true
}

// 处理文件变更
function handleFileChange() {
  // 文件变更事件处理
}

// 提交图片进行OCR识别
async function submitForOcr() {
  if (!uploadRef.value) {
    toast.add({ severity: 'error', summary: '请先选择图片', life: 3000 })
    return
  }

  const files = uploadRef.value.getFileList()

  if (!files || files.length === 0) {
    toast.add({ severity: 'error', summary: '请先选择图片', life: 3000 })
    return
  }

  isProcessing.value = true
  resultText.value = ''
  showBatchResults.value = false
  batchResults.value = []

  try {
    // 单图片处理
    if (files.length === 1) {
      toast.add({ severity: 'info', summary: '正在识别图片...', life: 0 })
      const text = await ocrTextService.recognizeImage(files[0])

      if (text) {
        resultText.value = text
        toast.add({ severity: 'success', summary: '识别成功', life: 3000 })
      }
      else {
        toast.add({ severity: 'error', summary: '识别失败', life: 3000 })
      }
    }
    // 批量处理
    else {
      toast.add({ severity: 'info', summary: `正在批量识别${files.length}张图片...`, life: 0 })
      const result = await ocrTextService.batchRecognizeImages(files)

      if (result.results.length > 0) {
        batchResults.value = result.results
        showBatchResults.value = true
        toast.add({
          severity: 'success',
          summary: `成功识别${result.success}张图片，失败${result.failed}张`,
          life: 3000,
        })
      }
      else {
        toast.add({ severity: 'error', summary: '批量识别失败', life: 3000 })
      }
    }
  }
  catch (error) {
    console.error('OCR处理错误:', error)
    toast.add({ severity: 'error', summary: '识别过程中发生错误', life: 3000 })
  }
  finally {
    isProcessing.value = false
  }
}

// 下载文本结果
function downloadText(text: string, fileName: string = 'ocr_result') {
  const blob = new Blob([text], { type: 'text/plain;charset=utf-8' })
  const url = URL.createObjectURL(blob)

  const link = document.createElement('a')
  link.href = url
  link.download = `${fileName}.txt`
  link.click()

  URL.revokeObjectURL(url)
}

// 关闭弹窗
function closeUploadModal() {
  uploadVisible.value = false
  // 清空结果
  resultText.value = ''
  showBatchResults.value = false
  batchResults.value = []
  // 如果存在上传组件，清空已选择的文件
  if (uploadRef.value) {
    uploadRef.value.clearFiles()
  }
}
</script>

<template>
  <div class="ocr-text-container">
    <Card>
      <template #title>
        图片转文字 OCR
      </template>
      <template #content>
        <div class="flex justify-between items-center mb-4">
          <div />
          <Button @click="openUploadModal">
            <template #icon>
              <Icon icon-type="UploadOutlined" />
            </template>
            上传图片
          </Button>
        </div>

        <div class="mb-5">
          <p>
            通过OCR识别技术将图片转换为文字，支持批量处理图片。<br>
            支持格式：JPG、JPEG、PNG、GIF 等常见图片格式。<br>
            图片大小限制：单张图片不超过5MB。
          </p>
        </div>

        <div class="grid grid-cols-3 gap-4">
          <div class="text-center">
            <div class="text-lg font-semibold mb-2">
              1. 上传图片
            </div>
            <div class="text-gray-600">
              选择本地图片上传
            </div>
          </div>
          <div class="text-center">
            <div class="text-lg font-semibold mb-2">
              2. 提交处理
            </div>
            <div class="text-gray-600">
              将图片发送到OCR服务
            </div>
          </div>
          <div class="text-center">
            <div class="text-lg font-semibold mb-2">
              3. 查看结果
            </div>
            <div class="text-gray-600">
              显示识别出的文字内容
            </div>
          </div>
        </div>
      </template>
    </Card>

    <!-- 上传弹窗 -->
    <Dialog
      v-model:visible="uploadVisible"
      modal
      :closable="false"
      :draggable="false"
      style="width: 800px"
      @hide="closeUploadModal"
    >
      <template #header>
        图片上传与OCR识别
      </template>
      <div class="upload-container">
        <div class="upload-panel">
          <h3 class="text-lg font-semibold mb-3">
            1. 选择图片
          </h3>
          <OcrImageUpload
            ref="uploadRef"
            :max-count="10"
            @change="handleFileChange"
          />
          <div class="upload-tips mt-3">
            <p class="text-sm text-gray-600">
              <Icon icon-type="PaperClipOutlined" /> 单次最多上传10张图片，单张图片大小不超过5MB<br>
              <Icon icon-type="PaperClipOutlined" /> 支持JPG、JPEG、PNG、GIF等常见图片格式
            </p>
          </div>
        </div>

        <Divider />

        <div class="action-panel flex justify-center my-5">
          <Button
            :loading="isProcessing"
            @click="submitForOcr"
          >
            <template #icon>
              <Icon icon-type="SyncOutlined" />
            </template>
            {{ isProcessing ? '处理中...' : '提交识别' }}
          </Button>
        </div>

        <Divider />

        <!-- 识别结果展示 -->
        <div v-if="resultText || showBatchResults" class="result-panel">
          <h3 class="text-lg font-semibold mb-3">
            识别结果
          </h3>

          <!-- 单个图片结果 -->
          <div v-if="resultText" class="single-result">
            <CustomTextArea
              v-model="resultText"
              :rows="10"
              readonly
            />
            <div class="result-actions flex justify-end mt-3">
              <Button @click="downloadText(resultText)">
                下载文本
              </Button>
            </div>
          </div>

          <!-- 批量图片结果 -->
          <div v-if="showBatchResults" class="batch-results">
            <TabView>
              <TabPanel
                v-for="(result, index) in batchResults"
                :key="index"
                :header="`文件 ${index + 1}: ${result.fileName}`"
              >
                <CustomTextArea
                  v-model="result.text"
                  :rows="5"
                  readonly
                />
                <div class="result-actions flex justify-end mt-3">
                  <Button
                    size="small"
                    @click="downloadText(result.text, result.fileName.split('.')[0])"
                  >
                    下载文本
                  </Button>
                </div>
              </TabPanel>
            </TabView>

            <div class="batch-actions flex justify-center mt-5">
              <Button
                @click="downloadText(batchResults.map(r => `=== ${r.fileName} ===\n${r.text}`).join('\n\n'), 'ocr_batch_results')"
              >
                下载全部结果
              </Button>
            </div>
          </div>
        </div>

        <!-- 没有结果时 -->
        <div v-else-if="!isProcessing" class="empty-result my-8">
          <div class="text-center text-gray-500">
            <p>请选择图片并提交进行OCR识别</p>
          </div>
        </div>
      </div>

      <template #footer>
        <Button @click="closeUploadModal">
          关闭
        </Button>
      </template>
    </Dialog>
  </div>
</template>
