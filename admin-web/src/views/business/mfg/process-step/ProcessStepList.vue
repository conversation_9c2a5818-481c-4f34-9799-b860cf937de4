<script setup lang="ts">
import type { ProcessStepQueryParam } from '@/api/business/process-step/model/process-step-ex'
import type { FormExpose } from '@/components/base/Form/type'
import ProcessStepDetail from './components/ProcessStepDetail.vue'
import ProcessStepForm from './components/ProcessStepForm.vue'
import { processStepService } from './service/ProcessStepService'

// 服务初始化
processStepService.provide()

// 查询表单处理
const formRef = ref<FormExpose<ProcessStepQueryParam>>()

async function handleSubmit() {
  if (formRef.value) {
    const res = await formRef.value.submit()
    processStepService.updateQueryParam(res.values)
  }
  processStepService.onSearch()
}

async function handleReset() {
  formRef.value && formRef.value.reset()
  processStepService.resetQuery()
}
</script>

<template>
  <!---------- 查询参数 begin ----------->
  <Card>
    <Form ref="formRef" v-privilege="'processStep:query'">
      <Grid :cols="4" :gap="4">
        <GridCol>
          <FormItem label="工序编码" name="stepCode">
            <InputText allow-clear />
          </FormItem>
        </GridCol>
        <GridCol>
          <FormItem label="工序名称" name="stepName">
            <InputText allow-clear />
          </FormItem>
        </GridCol>
        <GridCol>
          <FormItem label="工作站名称" name="workstationName">
            <InputText allow-clear />
          </FormItem>
        </GridCol>
        <GridCol>
          <FormItem label="工艺路线ID" name="routeId">
            <InputNumber allow-clear />
          </FormItem>
        </GridCol>
      </Grid>
    </Form>
  </Card>
  <!---------- 查询参数 end ----------->

  <Card :hoverable="true">
    <!---------- 表格操作行 begin ----------->
    <FlexRow>
      <FlexRow>
        <AxTableOperateButtons add-config="processStep:add" batch-delete-config="processStep:delete" import-config="processStep:import" export-config="processStep:export" />
      </FlexRow>
      <ButtonGroup>
        <IconButton label="查询" icon-type="SearchOutlined" type="primary" @click="handleSubmit" />
        <IconButton label="重置" icon-type="ReloadOutlined" @click="handleReset" />
      </ButtonGroup>
    </FlexRow>
    <!---------- 表格操作行 end ----------->
    <br>
    <!-- 使用组件化的表格 -->
    <AxTable min-width="150rem">
      <template #bodyCell="{ column, record }">
        <!-- 只为操作列添加模板插槽 -->
        <template v-if="column.dataIndex === 'action'">
          <FlexRow justify="start">
            <IconAnchor
              v-privilege="'processStep:view'" label="查看" icon-type="EyeOutlined"
              @click="processStepService.openDetailView(record.id)"
            />
            <IconAnchor
              v-privilege="'processStep:edit'" label="编辑" icon-type="EditOutlined"
              @click="processStepService.openEditForm(record.id)"
            />
            <IconAnchor
              v-privilege="'processStep:delete'" label="删除" icon-type="DeleteOutlined" color="red"
              @click="processStepService.deleteEntity(record.id)"
            />
          </FlexRow>
        </template>
      </template>
    </AxTable>
  </Card>

  <!-- 导入弹窗 -->
  <AxImportExcelModal />
  <!-- 弹窗组件 -->
  <ProcessStepDetail />
  <ProcessStepForm />
</template>
