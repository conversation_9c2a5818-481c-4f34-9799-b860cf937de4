<script setup lang="ts">
import type { QualityInspectionQueryParam } from '@/api/business/quality-inspection/model/quality-inspection-ex'
import type { FormExpose } from '@/components/base/Form/type'
import QualityInspectionDetail from './components/QualityInspectionDetail.vue'
import QualityInspectionForm from './components/QualityInspectionForm.vue'
import { qualityInspectionService } from './service/QualityInspectionService'

// 服务初始化
qualityInspectionService.provide()

// 查询表单处理
const formRef = ref<FormExpose<QualityInspectionQueryParam>>()

async function handleSubmit() {
  if (formRef.value) {
    const res = await formRef.value.submit()
    qualityInspectionService.updateQueryParam(res.values)
  }
  qualityInspectionService.onSearch()
}

async function handleReset() {
  formRef.value && formRef.value.reset()
  qualityInspectionService.resetQuery()
}
</script>

<template>
  <!---------- 查询参数 begin ----------->
  <Card>
    <Form ref="formRef" v-privilege="'qualityInspection:query'">
      <Grid :cols="4" :gap="4">
        <GridCol>
          <FormItem label="检验单号" name="inspectionNo">
            <InputText allow-clear />
          </FormItem>
        </GridCol>
        <GridCol>
          <FormItem label="任务编号" name="taskNo">
            <InputText allow-clear />
          </FormItem>
        </GridCol>
        <GridCol>
          <FormItem label="检验类型" name="inspectionType">
            <DictSelect key-code="inspection_type" allow-clear />
          </FormItem>
        </GridCol>
        <GridCol>
          <FormItem label="检验结果" name="result">
            <DictSelect key-code="inspection_result" allow-clear />
          </FormItem>
        </GridCol>
        <GridCol>
          <FormItem label="检验员姓名" name="inspectorName">
            <InputText allow-clear />
          </FormItem>
        </GridCol>
        <GridCol>
          <FormItem label="生产任务ID" name="taskId">
            <InputNumber allow-clear :min="0" />
          </FormItem>
        </GridCol>
      </Grid>
    </Form>
  </Card>
  <!---------- 查询参数 end ----------->

  <Card :hoverable="true">
    <!---------- 表格操作行 begin ----------->
    <FlexRow>
      <FlexRow>
        <AxTableOperateButtons add-config="qualityInspection:add" batch-delete-config="qualityInspection:delete" import-config="qualityInspection:import" export-config="qualityInspection:export" />
      </FlexRow>
      <ButtonGroup>
        <IconButton label="查询" icon-type="SearchOutlined" type="primary" @click="handleSubmit" />
        <IconButton label="重置" icon-type="ReloadOutlined" @click="handleReset" />
      </ButtonGroup>
    </FlexRow>
    <!---------- 表格操作行 end ----------->
    <br>
    <!-- 使用组件化的表格 -->
    <AxTable min-width="150rem">
      <template #bodyCell="{ column, record }">
        <!-- 只为操作列添加模板插槽 -->
        <template v-if="column.dataIndex === 'action'">
          <FlexRow justify="start">
            <IconAnchor
              v-privilege="'qualityInspection:view'" label="查看" icon-type="EyeOutlined"
              @click="qualityInspectionService.openDetailView(record.id)"
            />
            <IconAnchor
              v-privilege="'qualityInspection:edit'" label="编辑" icon-type="EditOutlined"
              @click="qualityInspectionService.openEditForm(record.id)"
            />
            <IconAnchor
              v-privilege="'qualityInspection:delete'" label="删除" icon-type="DeleteOutlined" color="red"
              @click="qualityInspectionService.deleteEntity(record.id)"
            />
          </FlexRow>
        </template>
      </template>
    </AxTable>
  </Card>

  <!-- 导入弹窗 -->
  <AxImportExcelModal />
  <!-- 弹窗组件 -->
  <QualityInspectionDetail />
  <QualityInspectionForm />
</template>
