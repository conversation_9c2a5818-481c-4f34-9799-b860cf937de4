<script setup lang="ts">
import type { ProcessStepForm } from '@/api/business/process-step/model/process-step-ex'
import type {
  WorkstationProcessRelationForm,
  WorkstationProcessRelationResult,
} from '@/api/business/workstation-process-relation/model/workstation-process-relation-ex'
import type { WorkstationService } from '../service/WorkstationService'
import { processStepApi } from '@/api/business/process-step/process-step-api'
import {
  workstationProcessRelationApi,
} from '@/api/business/workstation-process-relation/workstation-process-relation-api'
import { CRUD_KEY } from '@/service/composite/TableCrudService'
import { useConfirm } from 'primevue/useconfirm'
import { useToast } from 'primevue/usetoast'

// 获取工作站服务实例
const workstationService = inject<WorkstationService>(CRUD_KEY)!

// PrimeVue hooks
const { require: requireConfirmation } = useConfirm()
const toast = useToast()

// 工序关系列表
const processRelations = ref<WorkstationProcessRelationResult[]>([])
// 可选工序列表
const availableProcessSteps = ref<ProcessStepForm[]>([])
// 显示添加工序对话框
const showAddProcessDialog = ref(false)
// 选中的工序ID
const selectedProcessStep = ref<number | null>(null)
// 新增工序关系表单
const newProcessRelation = ref<WorkstationProcessRelationForm>({
  workstationId: undefined,
  processStepId: undefined,
  remark: '',
})
// 编辑工序关系表单
const editProcessRelation = ref<WorkstationProcessRelationForm>({})
// 显示编辑对话框
const showEditDialog = ref(false)

// 表格列定义
const columns = [
  {
    title: '工序名称',
    dataIndex: 'processStepName',
    key: 'processStepName',
    width: 100,
  },
  {
    title: '备注',
    dataIndex: 'remark',
    key: 'remark',
    width: 100,
  },
  {
    title: '操作',
    dataIndex: 'actions',
    key: 'actions',
    width: 100,
  },
]

// 监听工作站数据变化
watch(() => workstationService.formData.id, (newId) => {
  if (newId) {
    loadProcessRelations()
  }
}, { immediate: true })

// 加载工序关系列表
async function loadProcessRelations() {
  if (!workstationService.formData.id)
    return

  try {
    const res = await workstationProcessRelationApi.workstationProcessRelationList({
      workstationId: workstationService.formData.id,
    })
    if (res.success) {
      const relations = res.data || []

      // 获取所有工序ID
      const processStepIds = relations.map(r => r.processStepId).filter(id => id)

      if (processStepIds.length > 0) {
        // 查询工序详情
        const processStepRes = await processStepApi.processStepList({})
        if (processStepRes.success) {
          const processStepMap = new Map()
          processStepRes.data?.forEach((step) => {
            processStepMap.set(step.id, step)
          })

          // 为关系数据添加工序名称
          processRelations.value = relations.map(relation => ({
            ...relation,
            processStepName: processStepMap.get(relation.processStepId)?.stepName || '未知工序',
          }))
        }
        else {
          processRelations.value = relations
        }
      }
      else {
        processRelations.value = relations
      }
    }
  }
  catch (error) {
    console.error('加载工序关系失败:', error)
  }
}

// 加载可选工序列表
async function loadAvailableProcessSteps() {
  try {
    const res = await processStepApi.processStepList({})
    if (res.success) {
      availableProcessSteps.value = res.data || []
    }
  }
  catch (error) {
    console.error('加载工序列表失败:', error)
  }
}

// 显示添加工序对话框
function showAddProcess() {
  loadAvailableProcessSteps()
  newProcessRelation.value = {
    workstationId: workstationService.formData.id,
    processStepId: undefined,
    remark: '',
  }
  showAddProcessDialog.value = true
}

// 添加工序关系
async function addProcessRelation() {
  if (!selectedProcessStep.value) {
    console.warn('请选择工序')
    return
  }

  try {
    newProcessRelation.value.processStepId = selectedProcessStep.value
    const res = await workstationProcessRelationApi.addWorkstationProcessRelation(newProcessRelation.value)
    if (res.success) {
      toast.add({ severity: 'success', summary: '添加成功', detail: '工序关系已添加', life: 3000 })
      showAddProcessDialog.value = false
      selectedProcessStep.value = null
      await loadProcessRelations()
    }
    else {
      toast.add({ severity: 'error', summary: '添加失败', detail: '添加工序关系失败', life: 3000 })
    }
  }
  catch (error) {
    console.error('添加工序关系失败:', error)
  }
}

// 编辑工序关系
function editProcess(relation: WorkstationProcessRelationResult) {
  editProcessRelation.value = { ...relation }
  showEditDialog.value = true
}

// 更新工序关系
async function updateProcessRelation() {
  try {
    const res = await workstationProcessRelationApi.updateWorkstationProcessRelation(editProcessRelation.value)
    if (res.success) {
      toast.add({ severity: 'success', summary: '更新成功', detail: '工序关系已更新', life: 3000 })
      showEditDialog.value = false
      await loadProcessRelations()
    }
    else {
      toast.add({ severity: 'error', summary: '更新失败', detail: '更新工序关系失败', life: 3000 })
    }
  }
  catch (error) {
    console.error('更新工序关系失败:', error)
  }
}

// 删除工序关系
function deleteProcessRelation(relation: WorkstationProcessRelationResult) {
  requireConfirmation({
    message: '确定要删除该工序关系吗？',
    header: '确认删除',
    icon: 'pi pi-exclamation-triangle',
    acceptClass: 'p-button-danger',
    accept: async () => {
      try {
        const res = await workstationProcessRelationApi.deleteWorkstationProcessRelation(relation.id!)
        if (res.success) {
          toast.add({ severity: 'success', summary: '删除成功', detail: '工序关系已删除', life: 3000 })
          await loadProcessRelations()
        }
        else {
          toast.add({ severity: 'error', summary: '删除失败', detail: '删除工序关系失败', life: 3000 })
        }
      }
      catch (error) {
        console.error('删除工序关系失败:', error)
        toast.add({ severity: 'error', summary: '删除失败', detail: '删除工序关系时发生错误', life: 3000 })
      }
    },
  })
}
</script>

<template>
  <div class="workstation-process-manager">
    <!-- 工序关系表格 -->
    <div class="mb-4">
      <div class="flex justify-between items-center mb-4">
        <h4 class="text-lg font-medium">
          工序信息
        </h4>
        <Button
          severity="primary"
          size="small"
          :disabled="!workstationService.formData.id"
          @click="showAddProcess"
        >
          <template #icon>
            <Icon name="plus" />
          </template>
          添加工序
        </Button>
      </div>

      <CustomTable
        :data-source="processRelations"
        :columns="columns"
        size="small"
        :loading="false"
        row-key="id"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'processStepName'">
            <span>{{ record.processStepName || '未知工序' }}</span>
          </template>
          <template v-else-if="column.dataIndex === 'actions'">
            <div class="flex gap-2">
              <Button
                size="small"
                severity="secondary"
                text
                @click="editProcess(record)"
              >
                编辑
              </Button>
              <Button
                size="small"
                severity="danger"
                text
                @click="deleteProcessRelation(record)"
              >
                删除
              </Button>
            </div>
          </template>
        </template>
      </CustomTable>
    </div>

    <!-- 添加工序对话框 -->
    <Dialog
      v-model:visible="showAddProcessDialog"
      header="添加工序"
      style="width: 700px"
    >
      <Form>
        <Grid :cols="1" :gap="4">
          <GridCol>
            <FormItem label="选择工序">
              <Select
                v-model="selectedProcessStep"
                :options="availableProcessSteps"
                option-label="stepName"
                option-value="id"
                placeholder="请选择工序"
                filter
                show-clear
              />
            </FormItem>
          </GridCol>
          <GridCol>
            <FormItem label="备注">
              <Textarea
                v-model="newProcessRelation.remark"
                placeholder="请输入备注"
                :rows="3"
              />
            </FormItem>
          </GridCol>
        </Grid>
      </Form>

      <template #footer>
        <Button label="取消" severity="danger" @click="showAddProcessDialog = false" />
        <Button label="添加" @click="addProcessRelation" />
      </template>
    </Dialog>

    <!-- 编辑工序对话框 -->
    <Dialog
      v-model:visible="showEditDialog"
      header="编辑工序关系"
      style="width: 700px"
    >
      <Form>
        <Grid :cols="1" :gap="4">
          <GridCol>
            <FormItem label="备注">
              <Textarea
                v-model="editProcessRelation.remark"
                placeholder="请输入备注"
                :rows="3"
              />
            </FormItem>
          </GridCol>
        </Grid>
      </Form>

      <template #footer>
        <Button label="取消" severity="danger" @click="showEditDialog = false" />
        <Button label="更新" @click="updateProcessRelation" />
      </template>
    </Dialog>

    <!-- 确认对话框 -->
    <ConfirmDialog />
    <!-- Toast消息 -->
    <Toast />
  </div>
</template>

<style scoped>
.workstation-process-manager {
  min-height: 300px;
}
</style>
