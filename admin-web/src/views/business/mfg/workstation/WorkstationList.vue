<script setup lang="ts">
// 1. 导入区域
import type { WorkstationQueryParam } from '@/api/business/workstation/model/workstation-ex'
import type { FormExpose } from '@/components/base/Form/type'
import WorkstationDetail from './components/WorkstationDetail.vue'
import WorkstationForm from './components/WorkstationForm.vue'
import { workstationService } from './service/WorkstationService'

// 2. 服务初始化
workstationService.provide()

// 3. 查询表单处理（注意：List组件不需要任何生命周期代码）
const formRef = ref<FormExpose<WorkstationQueryParam>>()

async function handleSubmit() {
  if (formRef.value) {
    const res = await formRef.value.submit()
    workstationService.updateQueryParam(res.values) // 更新查询参数
  }
  workstationService.onSearch() // 执行查询操作
}

async function handleReset() {
  formRef.value && formRef.value.reset()
  workstationService.resetQuery()
}
</script>

<template>
  <!---------- 查询参数 begin ----------->
  <Card>
    <Form ref="formRef" v-privilege="'workstation:query'">
      <Grid :cols="4" :gap="4">
        <GridCol>
          <FormItem label="工作站编码" name="stationCode">
            <InputText allow-clear />
          </FormItem>
        </GridCol>
        <GridCol>
          <FormItem label="工作站名称" name="stationName">
            <InputText allow-clear />
          </FormItem>
        </GridCol>
        <GridCol>
          <FormItem label="工作站类型" name="stationType">
            <DictSelect key-code="workstation_type" allow-clear />
          </FormItem>
        </GridCol>
        <GridCol>
          <FormItem label="状态" name="status">
            <DictSelect key-code="workstation_status" allow-clear />
          </FormItem>
        </GridCol>
        <GridCol>
          <FormItem label="工作站位置" name="location">
            <InputText allow-clear />
          </FormItem>
        </GridCol>
        <GridCol>
          <FormItem label="生产车间" name="workshopId">
            <WorkshopSearch placeholder="请选择生产车间" allow-clear />
          </FormItem>
        </GridCol>
        <GridCol>
          <FormItem label="创建时间从" name="createTimeFrom">
            <DatePicker />
          </FormItem>
        </GridCol>
        <GridCol>
          <FormItem label="创建时间至" name="createTimeTo">
            <DatePicker />
          </FormItem>
        </GridCol>
      </Grid>
    </Form>
  </Card>
  <!---------- 查询参数 end ----------->

  <Card :hoverable="true">
    <!---------- 表格操作行 begin ----------->
    <FlexRow>
      <FlexRow>
        <AxTableOperateButtons add-config="workstation:add" batch-delete-config="workstation:delete" import-config="workstation:import" export-config="workstation:export" />
      </FlexRow>
      <ButtonGroup>
        <IconButton label="查询" icon-type="SearchOutlined" type="primary" @click="handleSubmit" />
        <IconButton label="重置" icon-type="ReloadOutlined" @click="handleReset" />
      </ButtonGroup>
    </FlexRow>
    <!---------- 表格操作行 end ----------->
    <br>
    <!-- 使用组件化的表格 -->
    <AxTable min-width="150rem">
      <template #bodyCell="{ column, record }">
        <!-- 只为操作列添加模板插槽 -->
        <template v-if="column.dataIndex === 'action'">
          <FlexRow justify="start">
            <IconAnchor
              v-privilege="'workstation:view'" label="查看" icon-type="EyeOutlined"
              @click="workstationService.openDetailView(record.id)"
            />
            <IconAnchor
              v-privilege="'workstation:edit'" label="编辑" icon-type="EditOutlined"
              @click="workstationService.openEditForm(record.id)"
            />
            <IconAnchor
              v-privilege="'workstation:delete'" label="删除" icon-type="DeleteOutlined" color="red"
              @click="workstationService.deleteEntity(record.id)"
            />
          </FlexRow>
        </template>
      </template>
    </AxTable>
  </Card>

  <!-- 导入弹窗 -->
  <AxImportExcelModal />
  <!-- 弹窗组件 -->
  <WorkstationDetail />
  <WorkstationForm />
</template>
