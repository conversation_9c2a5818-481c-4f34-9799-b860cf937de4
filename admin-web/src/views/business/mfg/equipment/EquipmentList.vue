<script lang="ts" setup>
import EquipmentDetail from './components/EquipmentDetail.vue'
import EquipmentForm from './components/EquipmentForm.vue'
import { equipmentService } from './service/EquipmentService'

// 提供 service
equipmentService.provide()
</script>

<template>
  <FlexCol class="h-full">
    <!---------- 查询参数 begin ----------->
    <Card>
      <Form :ref="equipmentService.tableFormRef" v-privilege="'equipment:query'">
        <Grid>
          <GridCol>
            <FormItem label="设备编码" name="equipmentCode">
              <InputText />
            </FormItem>
          </GridCol>
          <GridCol>
            <FormItem label="设备名称" name="equipmentName">
              <InputText />
            </FormItem>
          </GridCol>
          <GridCol>
            <FormItem label="设备类型" name="equipmentType">
              <DictSelect key-code="equipment_type" />
            </FormItem>
          </GridCol>
          <GridCol>
            <FormItem label="设备状态" name="status">
              <DictSelect key-code="equipment_status" />
            </FormItem>
          </GridCol>
          <GridCol>
            <FormItem label="制造商" name="manufacturer">
              <InputText />
            </FormItem>
          </GridCol>
          <GridCol>
            <FormItem label="设备位置" name="location">
              <InputText />
            </FormItem>
          </GridCol>
          <GridCol>
            <FormItem label="采购日期" name="purchaseDate">
              <DateRangePicker />
            </FormItem>
          </GridCol>
          <GridCol>
            <FormItem label="创建时间" name="createTime">
              <DateRangePicker />
            </FormItem>
          </GridCol>
        </Grid>
      </Form>
    </Card>
    <!---------- 查询参数 end ----------->
    <Card :hoverable="true">
      <!---------- 表格操作行 begin ----------->
      <FlexRow>
        <FlexRow>
          <AxTableOperateButtons add-config="equipment:add" batch-delete-config="equipment:delete" import-config="equipment:import" export-config="equipment:export" />
        </FlexRow>
        <ButtonGroup>
          <IconButton label="查询" icon-type="SearchOutlined" type="primary" @click="equipmentService.onSearch" />
          <IconButton label="重置" icon-type="ReloadOutlined" @click="equipmentService.resetQuery" />
        </ButtonGroup>
      </FlexRow>
      <!---------- 表格操作行 end ----------->
      <br>
      <!-- 使用组件化的表格 -->
      <AxTable min-width="180rem">
        <template #bodyCell="{ column, record }">
          <!-- 只为操作列添加模板插槽 -->
          <template v-if="column.dataIndex === 'action'">
            <FlexRow justify="start">
              <IconAnchor
                v-privilege="'equipment:view'" label="查看" icon-type="EyeOutlined"
                @click="equipmentService.openDetailView(record.id)"
              />
              <IconAnchor
                v-privilege="'equipment:edit'" label="编辑" icon-type="EditOutlined"
                @click="equipmentService.openEditForm(record.id)"
              />
              <IconAnchor
                v-privilege="'equipment:delete'" label="删除" icon-type="DeleteOutlined" color="red"
                @click="equipmentService.deleteEntity(record.id)"
              />
            </FlexRow>
          </template>
        </template>
      </AxTable>
    </Card>
  </FlexCol>
  <!-- 导入弹窗 -->
  <AxImportExcelModal />
  <!-- 设备表单 -->
  <EquipmentForm />
  <!-- 设备详情 -->
  <EquipmentDetail />
</template>
