<script setup lang="ts">
// 1. 导入区域
import type { ProcessRouteQueryParam } from '@/api/business/process-route/model/process-route-ex'
import type { FormExpose } from '@/components/base/Form/type'
import ProcessRouteDetail from './components/ProcessRouteDetail.vue'
import ProcessRouteForm from './components/ProcessRouteForm.vue'
import { processRouteService } from './service/ProcessRouteService'

// 2. 服务初始化
processRouteService.provide()

// 3. 查询表单处理（注意：List组件不需要任何生命周期代码）
const formRef = ref<FormExpose<ProcessRouteQueryParam>>()

async function handleSubmit() {
  if (formRef.value) {
    const res = await formRef.value.submit()
    processRouteService.updateQueryParam(res.values) // 更新查询参数
  }
  processRouteService.onSearch() // 执行查询操作
}

async function handleReset() {
  formRef.value && formRef.value.reset()
  processRouteService.resetQuery()
}
</script>

<template>
  <!---------- 查询参数 begin ----------->
  <Card>
    <Form ref="formRef" v-privilege="'process-route:query'">
      <Grid :cols="4" :gap="4">
        <GridCol>
          <FormItem label="工艺路线编码" name="routeCode">
            <InputText allow-clear />
          </FormItem>
        </GridCol>
        <GridCol>
          <FormItem label="工艺路线名称" name="routeName">
            <InputText allow-clear />
          </FormItem>
        </GridCol>
        <GridCol>
          <FormItem label="产品ID" name="productId">
            <InputNumber allow-clear :min="0" />
          </FormItem>
        </GridCol>
        <GridCol>
          <FormItem label="产品名称" name="productName">
            <InputText allow-clear />
          </FormItem>
        </GridCol>
        <GridCol>
          <FormItem label="版本号" name="version">
            <InputText allow-clear />
          </FormItem>
        </GridCol>
        <GridCol>
          <FormItem label="启用状态" name="isActive">
            <Select allow-clear placeholder="请选择启用状态">
              <SelectOption :value="true">
                启用
              </SelectOption>
              <SelectOption :value="false">
                禁用
              </SelectOption>
            </Select>
          </FormItem>
        </GridCol>
        <GridCol>
          <FormItem label="创建时间" name="createTimeFrom">
            <DatePicker allow-clear />
          </FormItem>
        </GridCol>
        <GridCol>
          <FormItem label="至" name="createTimeTo">
            <DatePicker allow-clear />
          </FormItem>
        </GridCol>
      </Grid>
    </Form>
  </Card>
  <!---------- 查询参数 end ----------->

  <Card :hoverable="true">
    <!---------- 表格操作行 begin ----------->
    <FlexRow>
      <FlexRow>
        <AxTableOperateButtons add-config="process-route:add" batch-delete-config="process-route:delete" import-config="process-route:import" export-config="process-route:export" />
      </FlexRow>
      <ButtonGroup>
        <IconButton label="查询" icon-type="SearchOutlined" type="primary" @click="handleSubmit" />
        <IconButton label="重置" icon-type="ReloadOutlined" @click="handleReset" />
      </ButtonGroup>
    </FlexRow>
    <!---------- 表格操作行 end ----------->
    <br>
    <!-- 使用组件化的表格 -->
    <AxTable min-width="150rem">
      <template #bodyCell="{ column, record }">
        <!-- 只为操作列添加模板插槽 -->
        <template v-if="column.dataIndex === 'action'">
          <FlexRow justify="start">
            <IconAnchor
              v-privilege="'process-route:view'" label="查看" icon-type="EyeOutlined"
              @click="processRouteService.openDetailView(record.id)"
            />
            <IconAnchor
              v-privilege="'process-route:edit'" label="编辑" icon-type="EditOutlined"
              @click="processRouteService.openEditForm(record.id)"
            />
            <IconAnchor
              v-privilege="'process-route:delete'" label="删除" icon-type="DeleteOutlined" color="red"
              @click="processRouteService.deleteEntity(record.id)"
            />
          </FlexRow>
        </template>
      </template>
    </AxTable>
  </Card>

  <!-- 导入弹窗 -->
  <AxImportExcelModal />
  <!-- 弹窗组件 -->
  <ProcessRouteDetail />
  <ProcessRouteForm />
</template>
