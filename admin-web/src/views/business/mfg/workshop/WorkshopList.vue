<script setup lang="ts">
// 1. 导入区域
import WorkshopDetail from './components/WorkshopDetail.vue'
import WorkshopForm from './components/WorkshopForm.vue'
import { workshopService } from './service/WorkshopService'

// 2. 服务初始化
workshopService.provide()
</script>

<template>
  <!---------- 查询参数 begin ----------->
  <Card>
    <Form :ref="workshopService.tableFormRef" v-privilege="'workshop:query'">
      <Grid>
        <GridCol>
          <FormItem label="车间编码" name="workshopCode">
            <InputText allow-clear />
          </FormItem>
        </GridCol>
        <GridCol>
          <FormItem label="车间名称" name="workshopName">
            <InputText allow-clear />
          </FormItem>
        </GridCol>
        <GridCol>
          <FormItem label="车间类型" name="workshopType">
            <DictSelect key-code="workshop_type" allow-clear />
          </FormItem>
        </GridCol>
        <GridCol>
          <FormItem label="车间状态" name="status">
            <DictSelect key-code="workshop_status" allow-clear />
          </FormItem>
        </GridCol>
      </Grid>
    </Form>
  </Card>
  <!---------- 查询参数 end ----------->

  <Card :hoverable="true">
    <!---------- 表格操作行 begin ----------->
    <FlexRow>
      <FlexRow>
        <AxTableOperateButtons add-config="workshop:add" batch-delete-config="workshop:delete" import-config="workshop:import" export-config="workshop:export" />
      </FlexRow>
      <ButtonGroup>
        <IconButton label="查询" icon-type="SearchOutlined" type="primary" @click="workshopService.onSearch" />
        <IconButton label="重置" icon-type="ReloadOutlined" @click="workshopService.resetQuery" />
      </ButtonGroup>
    </FlexRow>
    <!---------- 表格操作行 end ----------->
    <br>
    <!-- 使用组件化的表格 -->
    <AxTable min-width="180rem">
      <template #bodyCell="{ column, record }">
        <!-- 只为操作列添加模板插槽 -->
        <template v-if="column.dataIndex === 'action'">
          <FlexRow justify="start">
            <IconAnchor
              v-privilege="'workshop:view'" label="查看" icon-type="EyeOutlined"
              @click="workshopService.openDetailView(record.id)"
            />
            <IconAnchor
              v-privilege="'workshop:edit'" label="编辑" icon-type="EditOutlined"
              @click="workshopService.openEditForm(record.id)"
            />
            <IconAnchor
              v-privilege="'workshop:delete'" label="删除" icon-type="DeleteOutlined" color="red"
              @click="workshopService.deleteEntity(record.id)"
            />
          </FlexRow>
        </template>
      </template>
    </AxTable>
  </Card>

  <!-- 导入弹窗 -->
  <AxImportExcelModal />
  <!-- 弹窗组件 -->
  <WorkshopDetail />
  <WorkshopForm />
</template>
