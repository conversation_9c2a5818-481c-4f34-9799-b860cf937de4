<script setup lang="ts">
import type { ProductionOrderQueryParam } from '@/api/business/production-order/model/production-order-ex'
import type { FormExpose } from '@/components/base/Form/type'
import ProductionOrderDetail from './components/ProductionOrderDetail.vue'
import ProductionOrderForm from './components/ProductionOrderForm.vue'
import { productionOrderService } from './service/ProductionOrderService'

// 服务初始化
productionOrderService.provide()

// 查询表单处理（注意：List组件不需要任何生命周期代码）
const formRef = ref<FormExpose<ProductionOrderQueryParam>>()

async function handleSubmit() {
  if (formRef.value) {
    const res = await formRef.value.submit()
    productionOrderService.updateQueryParam(res.values) // 更新查询参数
  }
  productionOrderService.onSearch() // 执行查询操作
}

async function handleReset() {
  formRef.value && formRef.value.reset()
  productionOrderService.resetQuery()
}
</script>

<template>
  <!---------- 查询参数 begin ----------->
  <Card>
    <Form ref="formRef" v-privilege="'production-order:query'">
      <Grid :cols="4" :gap="4">
        <GridCol>
          <FormItem label="工单编号" name="orderNo">
            <InputText allow-clear />
          </FormItem>
        </GridCol>
        <GridCol>
          <FormItem label="工单类型" name="orderType">
            <DictSelect key-code="production_order_type" allow-clear />
          </FormItem>
        </GridCol>
        <GridCol>
          <FormItem label="优先级" name="priority">
            <DictSelect key-code="production_priority" allow-clear />
          </FormItem>
        </GridCol>
        <GridCol>
          <FormItem label="工单状态" name="status">
            <DictSelect key-code="production_order_status" allow-clear />
          </FormItem>
        </GridCol>
        <GridCol>
          <FormItem label="商品名称" name="productName">
            <InputText allow-clear />
          </FormItem>
        </GridCol>
        <GridCol>
          <FormItem label="SKU编码" name="skuCode">
            <InputText allow-clear />
          </FormItem>
        </GridCol>
        <GridCol>
          <FormItem label="操作人" name="operatorId">
            <EmployeeSearch
              v-model="productionOrderService.formData.operatorId"
              placeholder="请选择操作人"
              allow-clear
            />
          </FormItem>
        </GridCol>
        <GridCol>
          <FormItem label="开始日期从" name="startDateFrom">
            <DatePicker allow-clear />
          </FormItem>
        </GridCol>
        <GridCol>
          <FormItem label="开始日期至" name="startDateTo">
            <DatePicker allow-clear />
          </FormItem>
        </GridCol>
        <GridCol>
          <FormItem label="结束日期从" name="endDateFrom">
            <DatePicker allow-clear />
          </FormItem>
        </GridCol>
        <GridCol>
          <FormItem label="结束日期至" name="endDateTo">
            <DatePicker allow-clear />
          </FormItem>
        </GridCol>
        <GridCol>
          <FormItem label="创建时间从" name="createTimeFrom">
            <DatePicker allow-clear />
          </FormItem>
        </GridCol>
        <GridCol>
          <FormItem label="创建时间至" name="createTimeTo">
            <DatePicker allow-clear />
          </FormItem>
        </GridCol>
      </Grid>
    </Form>
  </Card>
  <!---------- 查询参数 end ----------->

  <Card :hoverable="true">
    <!---------- 表格操作行 begin ----------->
    <FlexRow>
      <FlexRow>
        <AxTableOperateButtons
          add-config="production-order:add"
          batch-delete-config="production-order:delete"
          import-config="production-order:import"
          export-config="production-order:export"
        />
      </FlexRow>
      <ButtonGroup>
        <IconButton label="查询" icon-type="SearchOutlined" type="primary" @click="handleSubmit" />
        <IconButton label="重置" icon-type="ReloadOutlined" @click="handleReset" />
      </ButtonGroup>
    </FlexRow>
    <!---------- 表格操作行 end ----------->
    <br>
    <!-- 使用组件化的表格 -->
    <AxTable min-width="150rem">
      <template #bodyCell="{ column, record }">
        <!-- 只为操作列添加模板插槽 -->
        <template v-if="column.dataIndex === 'action'">
          <FlexRow justify="start">
            <IconAnchor
              v-privilege="'production-order:view'" label="查看" icon-type="EyeOutlined"
              @click="productionOrderService.openDetailView(record.id)"
            />
            <IconAnchor
              v-privilege="'production-order:edit'" label="编辑" icon-type="EditOutlined"
              @click="productionOrderService.openEditForm(record.id)"
            />
            <IconAnchor
              v-privilege="'production-order:delete'" label="删除" icon-type="DeleteOutlined" color="red"
              @click="productionOrderService.deleteEntity(record.id)"
            />
          </FlexRow>
        </template>
      </template>
    </AxTable>
  </Card>

  <!-- 导入弹窗 -->
  <AxImportExcelModal />
  <!-- 弹窗组件 -->
  <ProductionOrderDetail />
  <ProductionOrderForm />
</template>
