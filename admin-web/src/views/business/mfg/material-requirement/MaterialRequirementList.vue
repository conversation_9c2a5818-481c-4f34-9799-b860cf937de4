<script setup lang="ts">
import type { MaterialRequirementQueryParam } from '@/api/business/material-requirement/model/material-requirement-ex'
import type { FormExpose } from '@/components/base/Form/type'
import MaterialRequirementDetail from './components/MaterialRequirementDetail.vue'
import MaterialRequirementForm from './components/MaterialRequirementForm.vue'
import { materialRequirementService } from './service/MaterialRequirementService'

// 服务初始化
materialRequirementService.provide()

// 查询表单处理（注意：List组件不需要任何生命周期代码）
const formRef = ref<FormExpose<MaterialRequirementQueryParam>>()

async function handleSubmit() {
  if (formRef.value) {
    const res = await formRef.value.submit()
    materialRequirementService.updateQueryParam(res.values) // 更新查询参数
  }
  materialRequirementService.onSearch() // 执行查询操作
}

async function handleReset() {
  formRef.value && formRef.value.reset()
  materialRequirementService.resetQuery()
}
</script>

<template>
  <!---------- 查询参数 begin ----------->
  <Card>
    <Form ref="formRef" v-privilege="'material-requirement:query'">
      <Grid :cols="4" :gap="4">
        <GridCol>
          <FormItem label="工单编号" name="orderNo">
            <InputText allow-clear />
          </FormItem>
        </GridCol>
        <GridCol>
          <FormItem label="生产工单ID" name="orderId">
            <InputNumber allow-clear :min="0" />
          </FormItem>
        </GridCol>
        <GridCol>
          <FormItem label="物料ID" name="materialId">
            <InputNumber allow-clear :min="0" />
          </FormItem>
        </GridCol>
        <GridCol>
          <FormItem label="物料名称" name="materialName">
            <InputText allow-clear />
          </FormItem>
        </GridCol>
        <GridCol>
          <FormItem label="物料编码" name="materialCode">
            <InputText allow-clear />
          </FormItem>
        </GridCol>
        <GridCol>
          <FormItem label="单位" name="unit">
            <InputText allow-clear />
          </FormItem>
        </GridCol>
        <GridCol>
          <FormItem label="需求状态" name="status">
            <DictSelect dict-code="material_requirement_status" allow-clear />
          </FormItem>
        </GridCol>
        <GridCol>
          <FormItem label="创建时间从" name="createTimeFrom">
            <DatePicker allow-clear />
          </FormItem>
        </GridCol>
        <GridCol>
          <FormItem label="创建时间至" name="createTimeTo">
            <DatePicker allow-clear />
          </FormItem>
        </GridCol>
      </Grid>
    </Form>
  </Card>
  <!---------- 查询参数 end ----------->

  <Card :hoverable="true">
    <!---------- 表格操作行 begin ----------->
    <FlexRow>
      <FlexRow>
        <AxTableOperateButtons
          add-config="material-requirement:add"
          batch-delete-config="material-requirement:delete"
          import-config="material-requirement:import"
          export-config="material-requirement:export"
        />
      </FlexRow>
      <ButtonGroup>
        <IconButton label="查询" icon-type="SearchOutlined" type="primary" @click="handleSubmit" />
        <IconButton label="重置" icon-type="ReloadOutlined" @click="handleReset" />
      </ButtonGroup>
    </FlexRow>
    <!---------- 表格操作行 end ----------->
    <br>
    <!-- 使用组件化的表格 -->
    <AxTable min-width="150rem">
      <template #bodyCell="{ column, record }">
        <!-- 只为操作列添加模板插槽 -->
        <template v-if="column.dataIndex === 'action'">
          <FlexRow justify="start">
            <IconAnchor
              v-privilege="'material-requirement:view'" label="查看" icon-type="EyeOutlined"
              @click="materialRequirementService.openDetailView(record.id)"
            />
            <IconAnchor
              v-privilege="'material-requirement:edit'" label="编辑" icon-type="EditOutlined"
              @click="materialRequirementService.openEditForm(record.id)"
            />
            <IconAnchor
              v-privilege="'material-requirement:delete'" label="删除" icon-type="DeleteOutlined" color="red"
              @click="materialRequirementService.deleteEntity(record.id)"
            />
          </FlexRow>
        </template>
      </template>
    </AxTable>
  </Card>

  <!-- 导入弹窗 -->
  <AxImportExcelModal />
  <!-- 弹窗组件 -->
  <MaterialRequirementDetail />
  <MaterialRequirementForm />
</template>
