<script setup lang="ts">
// 1. 导入区域
import type { ProductionPlanQueryParam } from '@/api/business/production-plan/model/production-plan-ex'
import type { FormExpose } from '@/components/base/Form/type'
import ProductionPlanDetail from './components/ProductionPlanDetail.vue'
import ProductionPlanForm from './components/ProductionPlanForm.vue'
import { productionPlanService } from './service/ProductionPlanService'

// 2. 服务初始化
productionPlanService.provide()

// 3. 查询表单处理（注意：List组件不需要任何生命周期代码）
const formRef = ref<FormExpose<ProductionPlanQueryParam>>()

async function handleSubmit() {
  if (formRef.value) {
    const res = await formRef.value.submit()
    productionPlanService.updateQueryParam(res.values) // 更新查询参数
  }
  productionPlanService.onSearch() // 执行查询操作
}

async function handleReset() {
  formRef.value && formRef.value.reset()
  productionPlanService.resetQuery()
}
</script>

<template>
  <!---------- 查询参数 begin ----------->
  <Card>
    <Form ref="formRef" v-privilege="'production-plan:query'">
      <Grid :cols="4" :gap="4">
        <GridCol>
          <FormItem label="计划编号" name="planNo">
            <InputText allow-clear />
          </FormItem>
        </GridCol>
        <GridCol>
          <FormItem label="计划名称" name="planName">
            <InputText allow-clear />
          </FormItem>
        </GridCol>
        <GridCol>
          <FormItem label="计划周期类型" name="periodType">
            <DictSelect key-code="production_plan_period_type" allow-clear />
          </FormItem>
        </GridCol>
        <GridCol>
          <FormItem label="计划状态" name="status">
            <DictSelect key-code="production_plan_status" allow-clear />
          </FormItem>
        </GridCol>
        <GridCol>
          <FormItem label="计划开始日期从" name="planStartDateFrom">
            <DatePicker />
          </FormItem>
        </GridCol>
        <GridCol>
          <FormItem label="计划开始日期至" name="planStartDateTo">
            <DatePicker />
          </FormItem>
        </GridCol>
        <GridCol>
          <FormItem label="计划结束日期从" name="planEndDateFrom">
            <DatePicker />
          </FormItem>
        </GridCol>
        <GridCol>
          <FormItem label="计划结束日期至" name="planEndDateTo">
            <DatePicker />
          </FormItem>
        </GridCol>
        <GridCol>
          <FormItem label="创建时间从" name="createTimeFrom">
            <DatePicker />
          </FormItem>
        </GridCol>
        <GridCol>
          <FormItem label="创建时间至" name="createTimeTo">
            <DatePicker />
          </FormItem>
        </GridCol>
      </Grid>
    </Form>
  </Card>
  <!---------- 查询参数 end ----------->

  <Card :hoverable="true">
    <!---------- 表格操作行 begin ----------->
    <FlexRow>
      <FlexRow>
        <AxTableOperateButtons add-config="production-plan:add" batch-delete-config="production-plan:delete" import-config="production-plan:import" export-config="production-plan:export" />
      </FlexRow>
      <ButtonGroup>
        <IconButton label="查询" icon-type="SearchOutlined" type="primary" @click="handleSubmit" />
        <IconButton label="重置" icon-type="ReloadOutlined" @click="handleReset" />
      </ButtonGroup>
    </FlexRow>
    <!---------- 表格操作行 end ----------->
    <br>
    <!-- 使用组件化的表格 -->
    <AxTable min-width="150rem">
      <template #bodyCell="{ column, record }">
        <!-- 只为操作列添加模板插槽 -->
        <template v-if="column.dataIndex === 'action'">
          <FlexRow justify="start">
            <IconAnchor
              v-privilege="'production-plan:view'" label="查看" icon-type="EyeOutlined"
              @click="productionPlanService.openDetailView(record.id)"
            />
            <IconAnchor
              v-privilege="'production-plan:edit'" label="编辑" icon-type="EditOutlined"
              @click="productionPlanService.openEditForm(record.id)"
            />
            <IconAnchor
              v-privilege="'production-plan:delete'" label="删除" icon-type="DeleteOutlined" color="red"
              @click="productionPlanService.deleteEntity(record.id)"
            />
          </FlexRow>
        </template>
      </template>
    </AxTable>
  </Card>

  <!-- 导入弹窗 -->
  <AxImportExcelModal />
  <!-- 弹窗组件 -->
  <ProductionPlanDetail />
  <ProductionPlanForm />
</template>
