<script setup lang="ts">
import type { ProductionScheduleQueryParam } from '@/api/business/production-schedule/model/production-schedule-ex'
import type { FormExpose } from '@/components/base/Form/type'
import ProductionScheduleDetail from './components/ProductionScheduleDetail.vue'
import ProductionScheduleForm from './components/ProductionScheduleForm.vue'
import { productionScheduleService } from './service/ProductionScheduleService'

// 服务初始化
productionScheduleService.provide()

// 查询表单处理
const formRef = ref<FormExpose<ProductionScheduleQueryParam>>()

async function handleSubmit() {
  if (formRef.value) {
    const res = await formRef.value.submit()
    productionScheduleService.updateQueryParam(res.values)
  }
  productionScheduleService.onSearch()
}

async function handleReset() {
  formRef.value && formRef.value.reset()
  productionScheduleService.resetQuery()
}
</script>

<template>
  <!---------- 查询参数 begin ----------->
  <Card>
    <Form ref="formRef" v-privilege="'production-schedule:query'">
      <Grid :cols="4" :gap="4">
        <GridCol>
          <FormItem label="排程编号" name="scheduleNo">
            <InputText allow-clear />
          </FormItem>
        </GridCol>
        <GridCol>
          <FormItem label="排程状态" name="status">
            <Select
              allow-clear
              dict-code="production_schedule_status"
              placeholder="请选择排程状态"
            />
          </FormItem>
        </GridCol>
        <GridCol>
          <FormItem label="优先级" name="priorityLevel">
            <Select allow-clear placeholder="请选择优先级">
              <SelectOption :value="1">
                低
              </SelectOption>
              <SelectOption :value="2">
                中
              </SelectOption>
              <SelectOption :value="3">
                高
              </SelectOption>
              <SelectOption :value="4">
                紧急
              </SelectOption>
            </Select>
          </FormItem>
        </GridCol>
        <GridCol>
          <FormItem label="计划开始时间" name="scheduledStartTimeFrom">
            <DatePicker allow-clear />
          </FormItem>
        </GridCol>
        <GridCol>
          <FormItem label="至" name="scheduledStartTimeTo">
            <DatePicker allow-clear />
          </FormItem>
        </GridCol>
        <GridCol>
          <FormItem label="计划结束时间" name="scheduledEndTimeFrom">
            <DatePicker allow-clear />
          </FormItem>
        </GridCol>
        <GridCol>
          <FormItem label="至" name="scheduledEndTimeTo">
            <DatePicker allow-clear />
          </FormItem>
        </GridCol>
        <GridCol>
          <FormItem label="计划生产数量" name="scheduledQuantity">
            <InputNumber allow-clear />
          </FormItem>
        </GridCol>
      </Grid>
    </Form>
  </Card>
  <!---------- 查询参数 end ----------->

  <Card :hoverable="true">
    <!---------- 表格操作行 begin ----------->
    <FlexRow>
      <FlexRow>
        <AxTableOperateButtons
          add-config="production-schedule:add"
          batch-delete-config="production-schedule:delete"
          import-config="production-schedule:import"
          export-config="production-schedule:export"
        />
      </FlexRow>
      <ButtonGroup>
        <IconButton
          label="查询"
          icon-type="SearchOutlined"
          type="primary"
          @click="handleSubmit"
        />
        <IconButton
          label="重置"
          icon-type="ReloadOutlined"
          @click="handleReset"
        />
      </ButtonGroup>
    </FlexRow>
    <!---------- 表格操作行 end ----------->
    <br>
    <!-- 使用组件化的表格 -->
    <AxTable min-width="150rem">
      <template #bodyCell="{ column, record }">
        <!-- 只为操作列添加模板插槽 -->
        <template v-if="column.dataIndex === 'action'">
          <FlexRow justify="start">
            <IconAnchor
              v-privilege="'production-schedule:view'"
              label="查看"
              icon-type="EyeOutlined"
              @click="productionScheduleService.openDetailView(record.id)"
            />
            <IconAnchor
              v-privilege="'production-schedule:edit'"
              label="编辑"
              icon-type="EditOutlined"
              @click="productionScheduleService.openEditForm(record.id)"
            />
            <IconAnchor
              v-privilege="'production-schedule:delete'"
              label="删除"
              icon-type="DeleteOutlined"
              color="red"
              @click="productionScheduleService.deleteEntity(record.id)"
            />
          </FlexRow>
        </template>
      </template>
    </AxTable>
  </Card>

  <!-- 导入弹窗 -->
  <AxImportExcelModal />
  <!-- 弹窗组件 -->
  <ProductionScheduleDetail />
  <ProductionScheduleForm />
</template>
