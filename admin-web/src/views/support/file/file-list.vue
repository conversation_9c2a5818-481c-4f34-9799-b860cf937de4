<script setup lang="ts">
import type { FileRecord } from '@/api/support/file/model/file'
import SmartEnumSelect from '@/components/base/enum-select/index.vue'
import FilePreviewModal from '@/components/utils/file-preview-modal/index.vue'
import FileUpload from '@/components/utils/file-upload/index.vue'
import { FILE_FOLDER_TYPE_ENUM } from '@/constants/support/file-const'
import { defaultTimeRanges } from '@/lib/default-time-ranges'
import { FileService } from './service/fileService'

// 服务注入
const fileService = new FileService()
fileService.provide()

// 表格列配置 - CustomTable使用Ant Design格式
const columns = [
  {
    title: '主键ID',
    dataIndex: 'fileId',
    width: 70,
  },
  {
    title: '文件夹',
    dataIndex: 'folderType',
    width: 100,
  },
  {
    title: '文件名称',
    dataIndex: 'fileName',
    width: 200,
  },
  {
    title: '文件大小',
    dataIndex: 'fileSize',
    width: 100,
  },
  {
    title: '文件类型',
    dataIndex: 'fileType',
    width: 80,
  },
  {
    title: '上传人',
    dataIndex: 'creatorName',
    width: 100,
  },
  {
    title: '人类型',
    dataIndex: 'creatorUserType',
    width: 100,
  },
  {
    title: '上传时间',
    dataIndex: 'createTime',
    width: 150,
  },
  {
    title: '操作',
    dataIndex: 'action',
    width: 120,
    fixed: 'right',
  },
]

// 生命周期
onMounted(() => {
  fileService.queryFiles()
})

// 查询方法
function onSearch() {
  fileService.searchFiles()
}

function resetQuery() {
  fileService.resetQuery()
}

function onChangeCreateTime(value: Date | Date[] | (Date | null)[] | null | undefined) {
  fileService.onCreateTimeChange(value)
}

// 文件操作方法
const filePreviewModalRef = ref()
function viewFile(file: FileRecord) {
  filePreviewModalRef.value.showPreview(file)
}

function downloadFile(file: FileRecord) {
  if (file.fileKey) {
    fileService.downloadFile(file.fileKey)
  }
}

// 上传文件方法
function showUploadModal() {
  fileService.showUploadModal()
}

function hideUploadModal() {
  fileService.hideUploadModal()
}

// 分页处理
function handlePageChange(page: number, pageSize?: number) {
  fileService.onPageChange(page, pageSize || 10)
}
</script>

<template>
  <!---------- 查询表单form begin ----------->
  <Form v-privilege="'support:file:query'" class="smart-query-form">
    <Grid :cols="6" :gap="4">
      <GridCol>
        <FormItem label="文件夹类型" class="smart-query-form-item">
          <SmartEnumSelect v-model:value="fileService.queryForm.folderType" enum-name="FILE_FOLDER_TYPE_ENUM" placeholder="文件夹类型" />
        </FormItem>
      </GridCol>
      <GridCol>
        <FormItem label="文件名" class="smart-query-form-item">
          <InputText v-model="fileService.queryForm.fileName" placeholder="文件名" />
        </FormItem>
      </GridCol>
      <GridCol>
        <FormItem label="文件Key" class="smart-query-form-item">
          <InputText v-model="fileService.queryForm.fileKey" placeholder="文件Key" />
        </FormItem>
      </GridCol>
      <GridCol>
        <FormItem label="文件类型" class="smart-query-form-item">
          <InputText v-model="fileService.queryForm.fileType" placeholder="文件类型" />
        </FormItem>
      </GridCol>
      <GridCol>
        <FormItem label="创建人" class="smart-query-form-item">
          <InputText v-model="fileService.queryForm.creatorName" placeholder="创建人" />
        </FormItem>
      </GridCol>
      <GridCol>
        <FormItem label="创建时间" class="smart-query-form-item">
          <DatePicker
            v-model="fileService.queryForm.createTime"
            selection-mode="range"
            :presets="defaultTimeRanges"
            @update:model-value="onChangeCreateTime"
          />
        </FormItem>
      </GridCol>
    </Grid>
  </Form>
  <!---------- 查询表单form end ----------->

  <Card>
    <!---------- 表格操作行 begin ----------->
    <FlexRow>
      <Button severity="primary" @click="showUploadModal">
        <i class="i-ant-design:cloud-upload-outlined" />
        上传文件
      </Button>
      <ButtonGroup>
        <Button severity="primary" @click="onSearch">
          <i class="i-ant-design:reload-outlined" />
          查询
        </Button>
        <Button @click="resetQuery">
          <i class="i-ant-design:search-outlined" />
          重置
        </Button>
      </ButtonGroup>
    </FlexRow>
    <!---------- 表格操作行 end ----------->

    <!---------- 表格 begin ----------->
    <CustomTable
      :data-source="fileService.dataList.value"
      :columns="columns"
      :loading="fileService.tableLoading.value"
      :total="fileService.total.value || 0"
      :page-param="{ pageNum: fileService.queryForm.pageNum || 1, pageSize: fileService.queryForm.pageSize || 10 }"
      row-key="fileId"
      show-pagination
      :on-page-change="handlePageChange"
      scroll-height="60vh"
    >
      <template #bodyCell="{ column, record }">
        <!-- 文件夹类型列 -->
        <template v-if="column.dataIndex === 'folderType'">
          <DictTag dict-code="FILE_FOLDER_TYPE_ENUM" :value="record.folderType" />
        </template>

        <!-- 人类型列 -->
        <template v-if="column.dataIndex === 'creatorUserType'">
          <DictTag dict-code="USER_TYPE_ENUM" :value="record.creatorUserType" />
        </template>

        <!-- 操作列 -->
        <template v-if="column.dataIndex === 'action'">
          <FlexRow justify="start">
            <IconAnchor
              v-privilege="'support:file:view'"
              label="查看"
              icon-type="EyeOutlined"
              @click="viewFile(record)"
            />
            <IconAnchor
              v-privilege="'support:file:download'"
              label="下载"
              icon-type="DownloadOutlined"
              @click="downloadFile(record)"
            />
          </FlexRow>
        </template>
      </template>
    </CustomTable>
    <!---------- 表格 end ----------->

    <FilePreviewModal ref="filePreviewModalRef" />

    <!-- 文件上传弹窗 -->
    <Dialog v-model:visible="fileService.uploadVisible.value" header="上传文件">
      <FileUpload
        list-type="text"
        :max-upload-size="5"
        button-text="点击上传文件"
        :default-file-list="[]"
        :multiple="true"
        :folder="FILE_FOLDER_TYPE_ENUM.COMMON.value"
      />
      <template #footer>
        <Button label="取消" severity="danger" @click="hideUploadModal" />
        <Button label="保存" @click="hideUploadModal" />
      </template>
    </Dialog>
  </Card>
</template>
