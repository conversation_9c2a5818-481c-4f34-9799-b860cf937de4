<!--
  * 登录失败锁定
-->
<script setup lang="ts">
import { loginFailApi } from '@/api/support/login-fail-api'
import { AxLoading } from '@/components/base/ax-loading'
import { PAGE_SIZE_OPTIONS } from '@/constants/common-const'
import { defaultTimeRanges } from '@/lib/default-time-ranges'
import { sentry } from '@/lib/sentry'
import { useConfirm } from 'primevue/useconfirm'
import { useToast } from 'primevue/usetoast'
// 使用项目的自动导入
import { onMounted, reactive, ref } from 'vue'

// ---------------------------- 表格列 ----------------------------

const columns = ref([
  {
    title: '登录名',
    dataIndex: 'loginName',
  },
  {
    title: '用户类型',
    dataIndex: 'userType',
  },
  {
    title: '登录失败次数',
    dataIndex: 'loginFailCount',
  },
  {
    title: '锁定状态',
    dataIndex: 'lockFlag',
  },
  {
    title: '锁定开始时间',
    dataIndex: 'loginLockBeginTime',
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
  },
  {
    title: '更新时间',
    dataIndex: 'updateTime',
  },
])

// ---------------------------- 查询数据表单和方法 ----------------------------

const queryFormState = {
  loginName: undefined, // 登录名
  lockFlag: true, // 锁定状态
  loginLockBeginTime: [], // 登录失败锁定时间
  loginLockBeginTimeBegin: undefined, // 登录失败锁定时间 开始
  loginLockBeginTimeEnd: undefined, // 登录失败锁定时间 结束
  pageNum: 1,
  pageSize: 10,
}
// 查询表单form
const queryForm = reactive({ ...queryFormState })
// 表格加载loading
const tableLoading = ref(false)
// 表格数据
const tableData = ref([])
// 总数
const total = ref(0)

// 重置查询条件
function resetQuery() {
  const pageSize = queryForm.pageSize
  Object.assign(queryForm, queryFormState)
  queryForm.pageSize = pageSize
  queryForm.lockFlag = undefined
  queryData()
}

// 查询数据

function onSearch() {
  queryForm.pageNum = 1
  queryData()
}

async function queryData() {
  tableLoading.value = true
  try {
    const queryResult = await loginFailApi.queryPage(queryForm)
    tableData.value = queryResult.data.list
    total.value = queryResult.data.total
  }
  catch (e) {
    sentry.captureError(e)
  }
  finally {
    tableLoading.value = false
  }
}

function onChangeLoginLockBeginTime(value: Date | Date[] | (Date | null)[] | null | undefined) {
  if (Array.isArray(value) && value.length === 2 && value[0] && value[1]) {
    queryForm.loginLockBeginTimeBegin = value[0].toISOString().split('T')[0]
    queryForm.loginLockBeginTimeEnd = value[1].toISOString().split('T')[0]
  }
  else {
    queryForm.loginLockBeginTimeBegin = undefined
    queryForm.loginLockBeginTimeEnd = undefined
  }
}

onMounted(queryData)

// ---------------------------- 批量解除锁定 ----------------------------

// 选择表格行
const selectedRowKeyList = ref([])

function onSelectChange(selectedRowKeys) {
  selectedRowKeyList.value = selectedRowKeys
}

// 初始化确认对话框和 toast
const { require: requireConfirmation } = useConfirm()
const toast = useToast()

// 批量解除锁定
function confirmBatchDelete() {
  requireConfirmation({
    message: '确定要批量解除锁定这些数据吗?',
    header: '提示',
    acceptLabel: '解锁',
    acceptClass: 'p-button-danger',
    rejectLabel: '取消',
    accept: () => {
      requestBatchDelete()
    },
  })
}

// 请求批量删除
async function requestBatchDelete() {
  try {
    AxLoading.show()
    await loginFailApi.batchDelete(selectedRowKeyList.value)
    toast.add({ severity: 'success', summary: '解锁成功', life: 3000 })
    queryData()
  }
  catch (e) {
    sentry.captureError(e)
  }
  finally {
    AxLoading.hide()
  }
}
</script>

<template>
  <!---------- 查询表单form begin ----------->
  <Form class="smart-query-form">
    <Grid :cols="4" :gap="4">
      <GridCol>
        <FormItem label="登录名" class="smart-query-form-item">
          <InputText v-model="queryForm.loginName" style="width: 300px" placeholder="登录名" />
        </FormItem>
      </GridCol>
      <GridCol>
        <FormItem label="快速筛选" class="smart-query-form-item">
          <SelectButton
            v-model="queryForm.lockFlag"
            :options="[
              { label: '全部', value: undefined },
              { label: '已锁定', value: true },
              { label: '未锁定', value: false },
            ]"
            option-label="label"
            option-value="value"
            @update:model-value="onSearch"
          />
        </FormItem>
      </GridCol>
      <GridCol>
        <FormItem label="锁定时间" class="smart-query-form-item">
          <DatePicker
            v-model="queryForm.loginLockBeginTime"
            selection-mode="range"
            :presets="defaultTimeRanges"
            style="width: 220px"
            placeholder="选择时间范围"
            @update:model-value="onChangeLoginLockBeginTime"
          />
        </FormItem>
      </GridCol>
      <GridCol>
        <FormItem class="smart-query-form-item">
          <ButtonGroup>
            <Button severity="primary" @click="onSearch">
              <template #icon>
                <Icon icon-type="SearchOutlined" />
              </template>
              查询
            </Button>
            <Button class="smart-margin-left10" @click="resetQuery">
              <template #icon>
                <Icon icon-type="ReloadOutlined" />
              </template>
              重置
            </Button>
          </ButtonGroup>
        </FormItem>
      </GridCol>
    </Grid>
  </Form>
  <!---------- 查询表单form end ----------->

  <Card>
    <!---------- 表格操作行 begin ----------->
    <div class="flex justify-between mb-4">
      <div>
        <Button severity="danger" :disabled="selectedRowKeyList.length === 0" @click="confirmBatchDelete">
          <template #icon>
            <Icon icon-type="DeleteOutlined" />
          </template>
          解除锁定
        </Button>
      </div>
      <div>
        <TableOperator v-model="columns" :table-id="null" :refresh="queryData" />
      </div>
    </div>
    <!---------- 表格操作行 end ----------->

    <!---------- 表格 begin ----------->
    <DataTable
      v-model:selection="selectedRowKeyList"
      :value="tableData"
      :loading="tableLoading"
      data-key="loginFailId"
      show-gridlines
      selection-mode="multiple"
    >
      <Column selection-mode="multiple" header-style="width: 3rem" />
      <Column field="loginName" header="登录名" />
      <Column field="userType" header="用户类型">
        <template #body="{ data }">
          <DictTag dict-name="USER_TYPE_ENUM" :value="data.userType" />
        </template>
      </Column>
      <Column field="loginFailCount" header="登录失败次数" />
      <Column field="lockFlag" header="锁定状态">
        <template #body="{ data }">
          <Tag
            :severity="data.lockFlag ? 'danger' : 'success'"
            :value="data.lockFlag ? '已锁定' : '未锁定'"
          />
        </template>
      </Column>
      <Column field="loginLockBeginTime" header="锁定开始时间" />
      <Column field="createTime" header="创建时间" />
      <Column field="updateTime" header="更新时间" />
    </DataTable>
    <!---------- 表格 end ----------->

    <Paginator
      :first="(queryForm.pageNum - 1) * queryForm.pageSize"
      :rows="queryForm.pageSize"
      :total-records="total"
      :rows-per-page-options="PAGE_SIZE_OPTIONS.map(Number)"
      template="FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink RowsPerPageDropdown CurrentPageReport"
      :current-page-report-template="`共 ${total} 条记录`"
      @page="(event: any) => { queryForm.pageNum = event.page + 1; queryForm.pageSize = event.rows; onSearch(); }"
    />
  </Card>
</template>
