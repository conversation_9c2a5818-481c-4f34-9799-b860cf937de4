<script setup lang="ts">
import SerialNumberGenerateFormModal from './serial-number-generate-form-modal.vue'
import SerialNumberRecordList from './serial-number-record-list.vue'
import { SerialNumberService } from './service/serialNumberService'

// 服务注入
const serialNumberService = new SerialNumberService()
serialNumberService.provide()

// 表格列配置 - CustomTable使用Ant Design格式
const columns = [
  {
    title: 'ID',
    dataIndex: 'serialNumberId',
    width: 80,
  },
  {
    title: '业务',
    dataIndex: 'businessName',
  },
  {
    title: '格式',
    dataIndex: 'format',
  },
  {
    title: '循环周期',
    dataIndex: 'ruleType',
  },
  {
    title: '初始值',
    dataIndex: 'initNumber',
  },
  {
    title: '随机增量',
    dataIndex: 'stepRandomRange',
  },
  {
    title: '备注',
    dataIndex: 'remark',
  },
  {
    title: '上次产生单号',
    dataIndex: 'lastNumber',
  },
  {
    title: '上次产生时间',
    dataIndex: 'lastTime',
  },
  {
    title: '操作',
    dataIndex: 'action',
    fixed: 'right',
    width: 140,
  },
]

// 生命周期
onMounted(() => {
  serialNumberService.querySerialNumbers()
})

// 表格操作方法
const generateForm = ref()
function generate(record: any) {
  generateForm.value.showModal(record)
}

const recordList = ref()
function showRecord(serialNumberId: number) {
  recordList.value.showModal(serialNumberId)
}
</script>

<template>
  <Card>
    <Message severity="info" :closable="false">
      <div class="flex flex-col gap-2">
        <h4>SerialNumber 单号生成器介绍：</h4>
        <pre class="text-sm">
简介：SerialNumber是一个可以根据不同的日期、规则生成一系列特别单号的功能，比如订单号、合同号、采购单号等等。
原理：内部有三种实现方式： 1) 基于内存锁实现 （不支持分布式和集群）；  2) 基于redis锁实现 ；  3) 基于Mysql 锁for update 实现
- 支持随机生成和查询生成记录
- 支持动态配置
</pre>
      </div>
    </Message>

    <CustomTable
      :data-source="serialNumberService.dataList.value"
      :columns="columns"
      :loading="serialNumberService.tableLoading.value"
      row-key="serialNumberId"
      class="mt-4"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'action'">
          <FlexRow justify="start">
            <IconAnchor
              v-privilege="'support:serialNumber:generate'"
              label="生成"
              icon-type="PlayCircleOutlined"
              @click="generate(record)"
            />
            <IconAnchor
              v-privilege="'support:serialNumber:record'"
              label="查看记录"
              icon-type="EyeOutlined"
              @click="showRecord(record.serialNumberId)"
            />
          </FlexRow>
        </template>
      </template>
    </CustomTable>
  </Card>

  <!-- 生成表单 -->
  <SerialNumberGenerateFormModal ref="generateForm" @refresh="serialNumberService.refresh" />
  <!-- 生成记录 -->
  <SerialNumberRecordList ref="recordList" />
</template>
