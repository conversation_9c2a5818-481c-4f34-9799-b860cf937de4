<script setup lang="ts">
import type { JobRecord } from '@/api/support/job/model/job'
import { AxLoading } from '@/components/base/ax-loading/index.js'
import SmartEnumSelect from '@/components/base/enum-select/index.vue'
import { TRIGGER_TYPE_ENUM } from '@/constants/support/job-const'
import { useConfirm } from 'primevue/useconfirm'
import { useToast } from 'primevue/usetoast'
import DeletedJobList from './components/deleted-job-list.vue'
import JobFormModal from './components/job-form-modal.vue'
import JobLogListModal from './components/job-log-list-modal.vue'
import { JobService } from './service/jobService'

// 服务注入
const jobService = new JobService()
jobService.provide()

// Tab 状态
const activeKey = ref('1')

// Hook功能
const confirm = useConfirm()
const toast = useToast()

const columns = ref([
  {
    title: 'id',
    width: 50,
    dataIndex: 'jobId',
  },
  {
    title: '任务名称',
    dataIndex: 'jobName',
    minWidth: 150,
    ellipsis: true,
  },
  {
    title: '执行类',
    dataIndex: 'jobClass',
    minWidth: 180,
    ellipsis: true,
  },
  {
    title: '触发类型',
    dataIndex: 'triggerType',
    width: 110,
  },
  {
    title: '触发配置',
    dataIndex: 'triggerValue',
    width: 150,
  },
  {
    title: '上次执行',
    width: 180,
    dataIndex: 'lastJob',
  },
  {
    title: '下次执行',
    width: 150,
    dataIndex: 'nextJob',
  },
  {
    title: '启用状态',
    dataIndex: 'enabledFlag',
    width: 100,
  },
  {
    title: '执行参数',
    dataIndex: 'param',
    ellipsis: true,
  },
  {
    title: '任务描述',
    dataIndex: 'remark',
    ellipsis: true,
  },
  {
    title: '排序',
    dataIndex: 'sort',
    width: 65,
  },
  {
    title: '更新人',
    dataIndex: 'updateName',
    width: 90,
  },
  {
    title: '更新时间',
    dataIndex: 'updateTime',
    width: 150,
  },
  {
    title: '操作',
    dataIndex: 'action',
    fixed: 'right',
    width: 170,
  },
])

// 生命周期
onMounted(() => {
  jobService.queryJobs()
})

// 查询方法
function resetQuery() {
  jobService.resetQuery()
}

function onSearch() {
  jobService.searchJobs()
}

// 处理执行类展示
function handleJobClass(jobClass: string) {
  return jobService.formatJobClass(jobClass)
}

// 上次处理结果展示
function handleExecuteResult(result: string) {
  return jobService.formatExecuteResult(result)
}

// 更新状态
async function handleEnabledUpdate(checked: boolean, record: JobRecord) {
  const success = await jobService.updateJobEnabled(record, checked)
  if (success) {
    toast.add({ severity: 'success', summary: '成功', detail: '更新成功', life: 3000 })
  }
  else {
    toast.add({ severity: 'error', summary: '失败', detail: '更新失败', life: 3000 })
  }
}

// ------------------------------------ 执行记录 -------------------------------------
const jobLogModal = ref()
function openJobLogModal(jobId: string, name: string) {
  jobLogModal.value.show(jobId, name)
}

// ------------------------------------ 删除操作 -------------------------------------

function confirmDelete(jobId: string, jobName: string) {
  confirm.require({
    message: `确定要删除【${jobName}】任务吗?`,
    header: '警告',
    icon: 'pi pi-exclamation-triangle',
    rejectProps: {
      label: '取消',
      severity: 'secondary',
      outlined: true,
    },
    acceptProps: {
      label: '删除',
      severity: 'danger',
    },
    accept: () => {
      deleteJob(jobId)
    },
  })
}

async function deleteJob(jobId: string) {
  try {
    AxLoading.show()
    const success = await jobService.deleteJob(jobId)
    if (success) {
      toast.add({ severity: 'success', summary: '成功', detail: '删除成功!', life: 3000 })
    }
    else {
      toast.add({ severity: 'error', summary: '失败', detail: '删除失败!', life: 3000 })
    }
  }
  catch (e) {
    console.error('删除Job任务失败:', e)
    toast.add({ severity: 'error', summary: '失败', detail: '删除失败!', life: 3000 })
  }
  finally {
    AxLoading.hide()
  }
}

// ------------------------------------ 表单操作 -------------------------------------
const jobFormModal = ref()

// 打开更新表单
function openUpdateModal(record?: JobRecord) {
  jobFormModal.value.openUpdateModal(record)
}
// 打开执行表单
function openExecuteModal(record: JobRecord) {
  jobFormModal.value.openExecuteModal(record)
}

// 分页处理
function handlePageChange(event: any) {
  const page = event.page + 1
  const pageSize = event.rows
  jobService.onPageChange(page, pageSize)
}
</script>

<template>
  <div>
    <Card>
      <BaseTabs v-model:value="activeKey">
        <BaseTabList>
          <BaseTab value="1">
            有效任务
          </BaseTab>
          <BaseTab value="2">
            已删除任务
          </BaseTab>
        </BaseTabList>
        <BaseTabPanels>
          <BaseTabsPanel value="1">
            <Form class="smart-query-form">
              <Grid :cols="4" :gap="4">
                <GridCol>
                  <FormItem label="关键字" class="smart-query-form-item">
                    <InputText v-model="jobService.queryForm.searchWord" style="width: 200px" placeholder="请输入关键字" :maxlength="30" />
                  </FormItem>
                </GridCol>
                <GridCol>
                  <FormItem label="触发类型" class="smart-query-form-item">
                    <SmartEnumSelect v-model="jobService.queryForm.triggerType" style="width: 155px" placeholder="请选择触发类型" enum-name="TRIGGER_TYPE_ENUM" />
                  </FormItem>
                </GridCol>
                <GridCol>
                  <FormItem label="状态" class="smart-query-form-item">
                    <Select
                      v-model="jobService.queryForm.enabledFlag" style="width: 150px" placeholder="请选择状态" show-clear :options="[
                        { label: '开启', value: 1 },
                        { label: '停止', value: 0 },
                      ]" option-label="label" option-value="value"
                    />
                  </FormItem>
                </GridCol>

                <GridCol>
                  <FormItem class="smart-query-form-item smart-margin-left10">
                    <ButtonGroup>
                      <Button v-privilege="'support:job:query'" severity="primary" @click="onSearch">
                        <template #icon>
                          <Icon icon-type="ReloadOutlined" />
                        </template>
                        查询
                      </Button>
                      <Button v-privilege="'support:job:query'" @click="resetQuery">
                        <template #icon>
                          <Icon icon-type="SearchOutlined" />
                        </template>
                        重置
                      </Button>
                    </ButtonGroup>

                    <Button v-privilege="'support:job:add'" class="smart-margin-left20" severity="primary" @click="openUpdateModal()">
                      <template #icon>
                        <Icon icon-type="PlusOutlined" />
                      </template>
                      添加任务
                    </Button>
                  </FormItem>
                </GridCol>
              </Grid>
            </Form>

            <CustomTable
              :data-source="jobService.dataList.value"
              :columns="columns"
              :loading="jobService.tableLoading.value"
              :total="jobService.total.value"
              :page-param="{ pageNum: jobService.queryForm.pageNum || 1, pageSize: jobService.queryForm.pageSize || 10 }"
              row-key="jobId"
              show-pagination
              :on-page-change="handlePageChange"
              scroll-height="600px"
            >
              <template #bodyCell="{ column, record }">
                <!-- 执行类列 -->
                <template v-if="column.dataIndex === 'jobClass'">
                  <span v-tooltip="record.jobClass">
                    {{ handleJobClass(record.jobClass) }}
                  </span>
                </template>

                <!-- 触发类型列 -->
                <template v-if="column.dataIndex === 'triggerType'">
                  <Tag v-if="record.triggerType === TRIGGER_TYPE_ENUM.CRON.value" severity="success">
                    {{ record.triggerTypeDesc }}
                  </Tag>
                  <Tag v-else-if="record.triggerType === TRIGGER_TYPE_ENUM.FIXED_DELAY.value" severity="info">
                    {{ record.triggerTypeDesc }}
                  </Tag>
                  <Tag v-else severity="warn">
                    {{ record.triggerTypeDesc }}
                  </Tag>
                </template>

                <!-- 上次执行列 -->
                <template v-if="column.dataIndex === 'lastJob'">
                  <div v-if="record.lastJobLog">
                    <span v-tooltip="handleExecuteResult(record.lastJobLog.executeResult)">
                      <Icon v-if="record.lastJobLog.successFlag" icon-type="CheckOutlined" style="color: #39c710" />
                      <Icon v-else icon-type="ExclamationOutlined" style="color: #f50" />
                      {{ record.lastJobLog.executeStartTime }}
                    </span>
                  </div>
                </template>

                <!-- 下次执行列 -->
                <template v-if="column.dataIndex === 'nextJob'">
                  <span v-if="record.enabledFlag && record.nextJobExecuteTimeList" v-tooltip="`下次执行(预估时间)\n${record.nextJobExecuteTimeList.join('\n')}`">
                    {{ record.nextJobExecuteTimeList[0] }}
                  </span>
                </template>

                <!-- 启用状态列 -->
                <template v-if="column.dataIndex === 'enabledFlag'">
                  <ToggleSwitch
                    :model-value="record.enabledFlag"
                    :loading="record.enabledLoading"
                    @update:model-value="(checked) => handleEnabledUpdate(checked, record)"
                  />
                </template>

                <!-- 操作列 -->
                <template v-if="column.dataIndex === 'action'">
                  <FlexRow justify="start">
                    <IconAnchor
                      v-privilege="'support:job:update'"
                      label="编辑"
                      icon-type="EditOutlined"
                      @click="openUpdateModal(record)"
                    />
                    <IconAnchor
                      v-privilege="'support:job:execute'"
                      label="执行"
                      icon-type="PlayCircleOutlined"
                      @click="openExecuteModal(record)"
                    />
                    <IconAnchor
                      v-privilege="'support:job:log:query'"
                      label="记录"
                      icon-type="FileTextOutlined"
                      @click="openJobLogModal(record.jobId, record.jobName)"
                    />
                    <IconAnchor
                      v-privilege="'support:job:log:delete'"
                      label="删除"
                      icon-type="DeleteOutlined"
                      color="red"
                      @click="confirmDelete(record.jobId, record.jobName)"
                    />
                  </FlexRow>
                </template>
              </template>
            </CustomTable>

            <!-- 分页已集成在CustomTable中 -->
          </BaseTabsPanel>
          <BaseTabsPanel value="2">
            <DeletedJobList />
          </BaseTabsPanel>
        </BaseTabPanels>
      </BaseTabs>
    </Card>

    <!-- 表单操作 -->
    <JobFormModal ref="jobFormModal" @reload-list="jobService.refresh" />

    <!-- 记录 -->
    <JobLogListModal ref="jobLogModal" />

    <!-- 确认对话框 -->
    <ConfirmDialog />

    <!-- 消息提示 -->
    <Toast />
  </div>
</template>
