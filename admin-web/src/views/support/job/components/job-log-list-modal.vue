<!--
  * job log列表
-->
<script setup lang="ts">
import { jobApi } from '@/api/support/job-api'
import { TABLE_ID_CONST } from '@/constants/support/table-id-const'
import { sentry } from '@/lib/sentry'
import { reactive, ref } from 'vue'

const showFlag = ref(false)
const title = ref('')

// ---------------- 查询数据 -----------------------
const queryFormState = {
  searchWord: '',
  jobId: null,
  successFlag: null,
  endTime: null,
  startTime: null,
  pageNum: 1,
  pageSize: 10,
}
const queryForm = reactive({ ...queryFormState })

function show(jobId, name) {
  queryForm.jobId = jobId
  queryLogList()
  showFlag.value = true
  title.value = name
}

defineExpose({ show })

const tableLoading = ref(false)
const tableData = ref([])
const total = ref(0)

// 日期选择
const searchDate = ref()

function dateChange(value: Date | Date[] | null) {
  if (Array.isArray(value) && value.length === 2 && value[0] && value[1]) {
    queryForm.startTime = value[0].toISOString().split('T')[0]
    queryForm.endTime = value[1].toISOString().split('T')[0]
  }
  else {
    queryForm.startTime = undefined
    queryForm.endTime = undefined
  }
}

function resetQuery() {
  Object.assign(queryForm, queryFormState)
  searchDate.value = undefined
  queryLogList()
}

function onSearch() {
  queryForm.pageNum = 1
  queryLogList()
}

function handlePageChange(event) {
  queryForm.pageNum = event.page + 1
  queryForm.pageSize = event.rows
  queryLogList()
}

async function queryLogList() {
  try {
    tableLoading.value = true
    const responseModel = await jobApi.queryJobLog(queryForm)
    const list = responseModel.data.list
    total.value = responseModel.data.total
    tableData.value = list
  }
  catch (e) {
    sentry.captureError(e)
  }
  finally {
    tableLoading.value = false
  }
}
</script>

<template>
  <Dialog v-model:visible="showFlag" :style="{ width: '1000px' }" :header="title" :closable="true">
    <Form class="mb-4">
      <Grid>
        <GridCol>
          <FormItem label="关键字">
            <InputText v-model="queryForm.searchWord" placeholder="请输入关键字" :maxlength="30" class="w-200px" />
          </FormItem>
        </GridCol>
        <GridCol>
          <FormItem label="执行结果">
            <Select v-model="queryForm.successFlag" placeholder="请选择" :options="[{ label: '成功', value: 1 }, { label: '失败', value: 0 }]" show-clear class="w-100px" />
          </FormItem>
        </GridCol>
        <GridCol>
          <FormItem label="执行时间">
            <DatePicker v-model="searchDate" selection-mode="range" date-format="yy-mm-dd" class="w-220px" @date-select="dateChange" />
          </FormItem>
        </GridCol>
        <GridCol>
          <FormItem>
            <ButtonGroup>
              <Button type="primary" @click="onSearch">
                <template #icon>
                  <Icon icon-type="ReloadOutlined" />
                </template>
                查询
              </Button>
              <Button @click="resetQuery">
                <template #icon>
                  <Icon icon-type="SearchOutlined" />
                </template>
                重置
              </Button>
            </ButtonGroup>
          </FormItem>
        </GridCol>
      </Grid>
    </Form>

    <Card>
      <div class="flex justify-end mb-2">
        <TableOperator :table-id="TABLE_ID_CONST.SUPPORT.JOB_LOG" :refresh="queryLogList" />
      </div>

      <DataTable :value="tableData" :loading="tableLoading" data-key="jobLogId" striped-rows>
        <Column field="createName" header="执行人" style="width: 100px" />
        <Column field="param" header="执行参数" style="width: 80px" />
        <Column field="executeStartTime" header="执行时间" style="width: 200px">
          <template #body="{ data }">
            <div>
              <Tag severity="success" value="始" />
              {{ data.executeStartTime }}
            </div>
            <div class="mt-1">
              <Tag severity="info" value="终" />
              {{ data.executeEndTime }}
            </div>
          </template>
        </Column>
        <Column field="executeTimeMillis" header="执行用时" style="width: 100px">
          <template #body="{ data }">
            {{ data.executeTimeMillis }} ms
          </template>
        </Column>
        <Column field="successFlag" header="结果" style="width: 80px">
          <template #body="{ data }">
            <div v-if="data.successFlag" class="text-green-600">
              <Icon icon-type="CheckOutlined" />
              成功
            </div>
            <div v-else class="text-red-500">
              <Icon icon-type="WarningOutlined" />
              失败
            </div>
          </template>
        </Column>
        <Column field="executeResult" header="执行结果" style="width: 100px" />
        <Column field="ip" header="ip" style="width: 110px" />
        <Column field="processId" header="进程id" style="width: 60px" />
        <Column field="programPath" header="程序目录" />
      </DataTable>

      <Paginator
        :first="(queryForm.pageNum - 1) * queryForm.pageSize"
        :rows="queryForm.pageSize"
        :total-records="total"
        :rows-per-page-options="[10, 20, 50]"
        class="mt-4"
        @page="handlePageChange"
      />
    </Card>
  </Dialog>
</template>
