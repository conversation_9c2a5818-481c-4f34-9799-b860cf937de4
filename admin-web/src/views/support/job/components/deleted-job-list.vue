<script setup lang="ts">
import SmartEnumSelect from '@/components/base/enum-select/index.vue'
import { TRIGGER_TYPE_ENUM } from '@/constants/support/job-const'
import { DeletedJobService } from '../service/deletedJobService'
import JobLogListModal from './job-log-list-modal.vue'

// 服务注入
const deletedJobService = new DeletedJobService()
deletedJobService.provide()

const columns = ref([
  {
    title: 'id',
    width: 50,
    dataIndex: 'jobId',
  },
  {
    title: '任务名称',
    dataIndex: 'jobName',
    minWidth: 150,
    ellipsis: true,
  },
  {
    title: '执行类',
    dataIndex: 'jobClass',
    minWidth: 180,
    ellipsis: true,
  },
  {
    title: '触发类型',
    dataIndex: 'triggerType',
    width: 110,
  },
  {
    title: '触发配置',
    dataIndex: 'triggerValue',
    width: 150,
  },
  {
    title: '上次执行',
    width: 180,
    dataIndex: 'lastJob',
  },
  {
    title: '下次执行',
    width: 150,
    dataIndex: 'nextJob',
  },
  {
    title: '启用状态',
    dataIndex: 'enabledFlag',
    width: 100,
  },
  {
    title: '执行参数',
    dataIndex: 'param',
    ellipsis: true,
  },
  {
    title: '任务描述',
    dataIndex: 'remark',
    ellipsis: true,
  },
  {
    title: '排序',
    dataIndex: 'sort',
    width: 65,
  },
  {
    title: '更新人',
    dataIndex: 'updateName',
    width: 90,
  },
  {
    title: '更新时间',
    dataIndex: 'updateTime',
    width: 150,
  },
  {
    title: '操作',
    dataIndex: 'action',
    fixed: 'right',
    width: 70,
  },
])

// 生命周期
onMounted(() => {
  deletedJobService.queryDeletedJobs()
})

// 查询方法
function resetQuery() {
  deletedJobService.resetQuery()
}

function onSearch() {
  deletedJobService.searchDeletedJobs()
}

// 处理执行类展示
function handleJobClass(jobClass: string) {
  return deletedJobService.formatJobClass(jobClass)
}

// 上次处理结果展示
function handleExecuteResult(result: string) {
  return deletedJobService.formatExecuteResult(result)
}

// ------------------------------------ 执行记录 -------------------------------------
const jobLogModal = ref()
function openJobLogModal(jobId: string, name: string) {
  jobLogModal.value.show(jobId, name)
}

// 分页处理
function handlePageChange(event: any) {
  const page = event.page + 1
  const pageSize = event.rows
  deletedJobService.onPageChange(page, pageSize)
}
</script>

<template>
  <div>
    <Form>
      <div class="grid grid-cols-12 gap-4 items-end">
        <div class="col-span-3">
          <FormItem label="关键字">
            <InputText v-model="deletedJobService.queryForm.searchWord" placeholder="请输入关键字" :maxlength="30" />
          </FormItem>
        </div>
        <div class="col-span-3">
          <FormItem label="触发类型">
            <SmartEnumSelect v-model="deletedJobService.queryForm.triggerType" placeholder="请选择触发类型" enum-name="TRIGGER_TYPE_ENUM" />
          </FormItem>
        </div>
        <div class="col-span-3">
          <FormItem label="状态">
            <Select v-model="deletedJobService.queryForm.enabledFlag" :options="[{ label: '开启', value: 1 }, { label: '停止', value: 0 }]" option-label="label" option-value="value" placeholder="请选择状态" show-clear />
          </FormItem>
        </div>
        <div class="col-span-3">
          <ButtonGroup>
            <Button v-privilege="'support:job:query'" severity="primary" @click="onSearch">
              <template #icon>
                <Icon icon-type="ReloadOutlined" />
              </template>
              查询
            </Button>
            <Button v-privilege="'support:job:query'" @click="resetQuery">
              <template #icon>
                <Icon icon-type="SearchOutlined" />
              </template>
              重置
            </Button>
          </ButtonGroup>
        </div>
      </div>
    </Form>

    <CustomTable
      :data-source="deletedJobService.dataList.value"
      :columns="columns"
      :loading="deletedJobService.tableLoading.value"
      :total="deletedJobService.total.value"
      :page-param="{ pageNum: deletedJobService.queryForm.pageNum || 1, pageSize: deletedJobService.queryForm.pageSize || 10 }"
      row-key="jobId"
      show-pagination
      :on-page-change="handlePageChange"
      scroll-height="60vh"
    >
      <template #bodyCell="{ column, record }">
        <!-- 任务名称列 -->
        <template v-if="column.dataIndex === 'jobName'">
          <div class="truncate" :title="record.jobName">
            {{ record.jobName }}
          </div>
        </template>

        <!-- 执行类列 -->
        <template v-if="column.dataIndex === 'jobClass'">
          <div class="truncate" :title="record.jobClass">
            {{ handleJobClass(record.jobClass) }}
          </div>
        </template>

        <!-- 触发类型列 -->
        <template v-if="column.dataIndex === 'triggerType'">
          <Tag v-if="record.triggerType === TRIGGER_TYPE_ENUM.CRON.value" severity="success">
            {{ record.triggerTypeDesc }}
          </Tag>
          <Tag v-else-if="record.triggerType === TRIGGER_TYPE_ENUM.FIXED_DELAY.value" severity="info">
            {{ record.triggerTypeDesc }}
          </Tag>
          <Tag v-else severity="warn">
            {{ record.triggerTypeDesc }}
          </Tag>
        </template>

        <!-- 上次执行列 -->
        <template v-if="column.dataIndex === 'lastJob'">
          <div v-if="record.lastJobLog" class="flex items-center gap-1" :title="handleExecuteResult(record.lastJobLog.executeResult)">
            <Icon
              v-if="record.lastJobLog.successFlag"
              icon-type="CheckOutlined"
              class="text-green-600"
            />
            <Icon
              v-else
              icon-type="WarningOutlined"
              class="text-red-500"
            />
            <span>{{ record.lastJobLog.executeStartTime }}</span>
          </div>
        </template>

        <!-- 下次执行列 -->
        <template v-if="column.dataIndex === 'nextJob'">
          <div
            v-if="record.enabledFlag && record.nextJobExecuteTimeList"
            :title="`下次执行(预估时间)\n${record.nextJobExecuteTimeList.join('\n')}`"
          >
            {{ record.nextJobExecuteTimeList[0] }}
          </div>
        </template>

        <!-- 启用状态列 -->
        <template v-if="column.dataIndex === 'enabledFlag'">
          <ToggleSwitch
            :model-value="record.enabledFlag"
            disabled
            :loading="record.enabledLoading"
          />
        </template>

        <!-- 执行参数列 -->
        <template v-if="column.dataIndex === 'param'">
          <div class="truncate" :title="record.param">
            {{ record.param }}
          </div>
        </template>

        <!-- 任务描述列 -->
        <template v-if="column.dataIndex === 'remark'">
          <div class="truncate" :title="record.remark">
            {{ record.remark }}
          </div>
        </template>

        <!-- 操作列 -->
        <template v-if="column.dataIndex === 'action'">
          <FlexRow justify="start">
            <IconAnchor
              v-privilege="'support:job:log:query'"
              label="记录"
              icon-type="FileTextOutlined"
              @click="openJobLogModal(record.jobId, record.jobName)"
            />
          </FlexRow>
        </template>
      </template>
    </CustomTable>

    <!-- 分页已集成在CustomTable中 -->

    <!-- 记录 -->
    <JobLogListModal ref="jobLogModal" />
  </div>
</template>
