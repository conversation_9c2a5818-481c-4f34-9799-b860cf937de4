<!--
  * 操作记录 列表
-->
<script setup lang="ts">
import { operateLogApi } from '@/api/support/operate-log-api'
import { TABLE_ID_CONST } from '@/constants/support/table-id-const'
import { defaultTimeRanges } from '@/lib/default-time-ranges'
import { sentry } from '@/lib/sentry'
// @ts-ignore
import uaparser from 'ua-parser-js'
import { onMounted, reactive, ref } from 'vue'
import OperateLogDetailModal from './operate-log-detail-modal.vue'

// 类型定义
interface OperateLogRecord {
  operateLogId: string
  operateUserId: string
  operateUserName: string
  operateUserType: string
  module: string
  content: string
  url: string
  method: string
  ip: string
  ipRegion: string
  userAgent: string
  successFlag: boolean
  createTime: string
  browser?: string
  os?: string
  device?: string
}

interface QueryForm {
  userName: string
  requestKeywords: string
  keywords: string
  successFlag?: boolean
  startDate?: string
  endDate?: string
  pageNum: number
  pageSize: number
}

const columns = ref([
  {
    title: '用户',
    dataIndex: 'operateUserName',
    width: 70,
  },
  {
    title: '类型',
    dataIndex: 'operateUserType',
    width: 50,
    ellipsis: true,
  },
  {
    title: '操作模块',
    dataIndex: 'module',
    ellipsis: true,
  },
  {
    title: '操作内容',
    dataIndex: 'content',
    ellipsis: true,
  },
  {
    title: '请求路径',
    dataIndex: 'url',
    ellipsis: true,
  },
  {
    title: 'IP',
    dataIndex: 'ip',
    ellipsis: true,
  },
  {
    title: 'IP地区',
    dataIndex: 'ipRegion',
    ellipsis: true,
  },
  {
    title: '客户端',
    dataIndex: 'userAgent',
    ellipsis: true,
  },
  {
    title: '请求方法',
    dataIndex: 'method',
    ellipsis: true,
  },
  {
    title: '请求结果',
    dataIndex: 'successFlag',
    width: 80,
  },
  {
    title: '时间',
    dataIndex: 'createTime',
    width: 150,
  },
  {
    title: '操作',
    dataIndex: 'action',
    fixed: 'right',
    width: 60,
  },
])

const queryFormState: QueryForm = {
  userName: '',
  requestKeywords: '',
  keywords: '',
  successFlag: undefined,
  startDate: undefined,
  endDate: undefined,
  pageNum: 1,
  pageSize: 10,
}
const queryForm = reactive({ ...queryFormState })
const createDateRange = ref<Date[] | null>(null)
const defaultChooseTimeRange = defaultTimeRanges

// 快速筛选选项
const successFlagOptions = [
  { label: '全部', value: undefined },
  { label: '成功', value: true },
  { label: '失败', value: false },
]

// 时间变动
function changeCreateDate(value: Date | Date[] | (Date | null)[] | null | undefined) {
  if (Array.isArray(value) && value.length === 2 && value[0] && value[1]) {
    queryForm.startDate = value[0].toISOString().split('T')[0]
    queryForm.endDate = value[1].toISOString().split('T')[0]
  }
  else {
    queryForm.startDate = undefined
    queryForm.endDate = undefined
  }
}

const tableLoading = ref(false)
const tableData = ref<OperateLogRecord[]>([])
const total = ref(0)

function resetQuery() {
  Object.assign(queryForm, queryFormState)
  createDateRange.value = null
  ajaxQuery()
}

function onSearch() {
  queryForm.pageNum = 1
  ajaxQuery()
}

async function ajaxQuery() {
  try {
    tableLoading.value = true
    const responseModel = await operateLogApi.queryList(queryForm)

    for (const e of responseModel.data.list) {
      if (!e.userAgent) {
        continue
      }
      const ua = uaparser(e.userAgent)
      e.browser = ua.browser.name
      e.os = ua.os.name
      e.device = ua.device.vendor ? ua.device.vendor + ua.device.model : ''
    }

    const list = responseModel.data.list
    total.value = responseModel.data.total
    tableData.value = list
  }
  catch (e) {
    sentry.captureError(e)
  }
  finally {
    tableLoading.value = false
  }
}

onMounted(() => {
  ajaxQuery()
})

// ---------------------- 详情 ----------------------
const detailModal = ref()
function showDetail(operateLogId: string) {
  detailModal.value.show(operateLogId)
}
</script>

<template>
  <Form v-privilege="'support:operateLog:query'" class="smart-query-form">
    <Grid :cols="6" :gap="4">
      <GridCol>
        <FormItem label="操作关键字" class="smart-query-form-item">
          <InputText v-model="queryForm.keywords" style="width: 150px" placeholder="模块/操作内容" />
        </FormItem>
      </GridCol>
      <GridCol>
        <FormItem label="请求关键字" class="smart-query-form-item">
          <InputText v-model="queryForm.requestKeywords" style="width: 220px" placeholder="请求地址/请求方法/请求参数" />
        </FormItem>
      </GridCol>
      <GridCol>
        <FormItem label="用户名称" class="smart-query-form-item">
          <InputText v-model="queryForm.userName" style="width: 100px" placeholder="用户名称" />
        </FormItem>
      </GridCol>

      <GridCol>
        <FormItem label="请求时间" class="smart-query-form-item">
          <DatePicker
            v-model="createDateRange"
            selection-mode="range"
            :presets="defaultChooseTimeRange"
            style="width: 240px"
            placeholder="选择时间范围"
            @update:model-value="changeCreateDate"
          />
        </FormItem>
      </GridCol>

      <GridCol>
        <FormItem label="快速筛选" class="smart-query-form-item">
          <SelectButton
            v-model="queryForm.successFlag"
            :options="successFlagOptions"
            option-label="label"
            option-value="value"
            @update:model-value="onSearch"
          />
        </FormItem>
      </GridCol>

      <GridCol>
        <FormItem class="smart-query-form-item smart-margin-left10">
          <ButtonGroup>
            <Button severity="primary" @click="onSearch">
              <template #icon>
                <Icon icon-type="SearchOutlined" />
              </template>
              查询
            </Button>
            <Button @click="resetQuery">
              <template #icon>
                <Icon icon-type="ReloadOutlined" />
              </template>
              重置
            </Button>
          </ButtonGroup>
        </FormItem>
      </GridCol>
    </Grid>
  </Form>

  <Card>
    <div class="flex justify-end mb-4">
      <TableOperator v-model="columns" class="smart-margin-bottom5" :table-id="TABLE_ID_CONST.SUPPORT.CONFIG" :refresh="ajaxQuery" />
    </div>
    <DataTable
      :value="tableData"
      :loading="tableLoading"
      data-key="operateLogId"
      show-gridlines
    >
      <Column field="operateUserName" header="用户" style="width: 70px" />
      <Column field="operateUserType" header="类型" style="width: 50px">
        <template #body="{ data }">
          <DictTag dict-name="USER_TYPE_ENUM" :value="data.operateUserType" />
        </template>
      </Column>
      <Column field="module" header="操作模块" />
      <Column field="content" header="操作内容" />
      <Column field="url" header="请求路径" />
      <Column field="ip" header="IP" />
      <Column field="ipRegion" header="IP地区" />
      <Column field="userAgent" header="客户端">
        <template #body="{ data }">
          <div>{{ data.browser }} / {{ data.os }} / {{ data.device }}</div>
        </template>
      </Column>
      <Column field="method" header="请求方法" />
      <Column field="successFlag" header="请求结果" style="width: 80px">
        <template #body="{ data }">
          <Tag
            :severity="data.successFlag ? 'success' : 'danger'"
            :value="data.successFlag ? '成功' : '失败'"
          />
        </template>
      </Column>
      <Column field="createTime" header="时间" style="width: 150px" />
      <Column header="操作" style="width: 60px">
        <template #body="{ data }">
          <div class="smart-table-operate">
            <Button
              v-privilege="'support:operateLog:detail'"
              text
              @click="showDetail(data.operateLogId)"
            >
              详情
            </Button>
          </div>
        </template>
      </Column>
    </DataTable>

    <Paginator
      :first="(queryForm.pageNum - 1) * queryForm.pageSize"
      :rows="queryForm.pageSize"
      :total-records="total"
      :rows-per-page-options="[5, 10, 15, 20, 30, 40, 50]"
      template="FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink RowsPerPageDropdown CurrentPageReport"
      :current-page-report-template="`共 ${total} 条记录`"
      @page="(event: any) => { queryForm.pageNum = event.page + 1; queryForm.pageSize = event.rows; ajaxQuery(); }"
    />

    <OperateLogDetailModal ref="detailModal" />
  </Card>
</template>
