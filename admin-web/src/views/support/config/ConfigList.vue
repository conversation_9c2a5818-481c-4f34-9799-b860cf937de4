<script lang="ts" setup>
import ConfigDetail from './components/ConfigDetail.vue'
import ConfigForm from './components/ConfigForm.vue'
import { configService } from './service/ConfigService'

// 提供 service
configService.provide()
</script>

<template>
  <!-- 查询参数 -->
  <Card>
    <Form v-privilege="'support:config:query'" layout="vertical">
      <Grid>
        <GridCol>
          <FormItem label="参数Key">
            <InputText v-model="configService.queryParam.configKey" placeholder="请输入参数Key" />
          </FormItem>
        </GridCol>
        <GridCol>
          <FormItem label="参数名称">
            <InputText v-model="configService.queryParam.configName" placeholder="请输入参数名称" />
          </FormItem>
        </GridCol>
      </Grid>
    </Form>
  </Card>

  <!-- 数据表格 -->
  <Card :hoverable="true">
    <!-- 表格操作行 -->
    <FlexRow>
      <FlexRow>
        <AxTableOperateButtons
          add-config="support:config:add"
          batch-delete-config="support:config:delete"
          import-config="support:config:import"
          export-config="support:config:export"
        />
      </FlexRow>
      <ButtonGroup>
        <IconButton label="查询" icon-type="SearchOutlined" type="primary" @click="configService.onSearch" />
        <IconButton label="重置" icon-type="ReloadOutlined" @click="configService.resetQuery" />
      </ButtonGroup>
    </FlexRow>

    <AxTable>
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'action'">
          <FlexRow justify="start">
            <IconAnchor
              v-privilege="'support:config:view'" label="查看" icon-type="EyeOutlined"
              @click="configService.openDetailView(record.configId!)"
            />
            <IconAnchor
              v-privilege="'support:config:update'" label="编辑" icon-type="EditOutlined"
              @click="configService.openEditForm(record.configId!)"
            />
            <IconAnchor
              v-privilege="'support:config:delete'" label="删除" icon-type="DeleteOutlined" color="red"
              @click="configService.deleteEntity(record.configId!)"
            />
          </FlexRow>
        </template>
      </template>
    </AxTable>
  </Card>

  <!-- 导入弹窗 -->
  <AxImportExcelModal />
  <!-- 表单组件 -->
  <ConfigForm />
  <!-- 详情组件 -->
  <ConfigDetail />
</template>
