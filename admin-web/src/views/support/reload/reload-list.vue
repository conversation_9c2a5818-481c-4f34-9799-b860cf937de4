<script setup lang="ts">
import DoReloadForm from './do-reload-form-modal.vue'
import ReloadResultList from './reload-result-list.vue'
import { ReloadService } from './service/reloadService'

// 服务注入
const reloadService = new ReloadService()
reloadService.provide()

// 表格列配置 - CustomTable使用Ant Design格式
const columns = [
  {
    title: '标签',
    dataIndex: 'tag',
    width: 200,
  },
  {
    title: '运行标识',
    dataIndex: 'identification',
  },
  {
    title: '参数',
    dataIndex: 'args',
  },
  {
    title: '更新时间',
    dataIndex: 'updateTime',
    width: 150,
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    width: 150,
  },
  {
    title: '操作',
    dataIndex: 'action',
    fixed: 'right',
    width: 150,
  },
]

// 生命周期
onMounted(() => {
  reloadService.queryReloadItems()
})

// 表格操作方法
const doReloadForm = ref()
function doReload(tag: string) {
  doReloadForm.value.showModal(tag)
}

const reloadResultList = ref()
function showResultList(tag: string) {
  reloadResultList.value.showModal(tag)
}
</script>

<template>
  <Card>
    <Message severity="info" :closable="false">
      <div class="flex flex-col gap-2">
        <h4>Smart-Reload 心跳服务介绍：</h4>
        <pre class="text-sm">
简介：SmartReload是一个可以在不重启进程的情况下动态重新加载配置或者执行某些预先设置的代码。

原理：
- Java后端会在项目启动的时候开启一个Daemon线程，这个Daemon线程会每隔几秒轮询t_smart_item表的状态。
- 如果【状态标识】与【上次状态标识】比较发生变化，会将参数传入SmartReload实现类，进行自定义操作。
用途：
· 用于刷新内存中的缓存
· 用于执行某些后门代码
· 用于进行Java热加载（前提是类结构不发生变化）
· 其他不能重启服务的应用
</pre>
      </div>
    </Message>

    <CustomTable
      :data-source="reloadService.dataList.value"
      :columns="columns"
      :loading="reloadService.tableLoading.value"
      row-key="tag"
      size="small"
      class="mt-4"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'action'">
          <FlexRow justify="start">
            <IconAnchor
              v-privilege="'support:reload:execute'"
              label="执行"
              icon-type="PlayCircleOutlined"
              @click="doReload(record.tag)"
            />
            <IconAnchor
              v-privilege="'support:reload:result'"
              label="查看结果"
              icon-type="EyeOutlined"
              @click="showResultList(record.tag)"
            />
          </FlexRow>
        </template>
      </template>
    </CustomTable>

    <!-- 执行表单 -->
    <DoReloadForm ref="doReloadForm" @refresh="reloadService.refresh" />
    <!-- 结果列表 -->
    <ReloadResultList ref="reloadResultList" />
  </Card>
</template>
