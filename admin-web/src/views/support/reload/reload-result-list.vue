<!--
  * reload 结果
-->
<script setup lang="ts">
import { reloadApi } from '@/api/support/reload-api'
import { sentry } from '@/lib/sentry'
import { reactive, ref } from 'vue'

const expandedRows = ref({})

function onRowToggle(event) {
  expandedRows.value = event.data
}

defineExpose({
  showModal,
})

// ----------------------- 表单 隐藏 与 显示 ------------------------
// 是否展示
const visible = ref(false)

function showModal(tag) {
  queryTag = tag
  ajaxQuery()
  visible.value = true
}

function onClose() {
  visible.value = false
}

// ------------------------ 表格查询 ---------------------
let queryTag = ''
const tableLoading = ref(false)
const tableData = ref([])

async function ajaxQuery() {
  try {
    tableLoading.value = true
    const res = await reloadApi.queryReloadResult(queryTag)
    let count = 1
    for (const item of res.data) {
      item.id = count++
    }
    tableData.value = res.data
  }
  catch (e) {
    sentry.captureError(e)
  }
  finally {
    tableLoading.value = false
  }
}

// ------------------------ 表格列 ---------------------

const columns = reactive([
  {
    title: '标签',
    dataIndex: 'tag',
  },
  {
    title: '参数',
    dataIndex: 'args',
  },
  {
    title: '运行结果',
    dataIndex: 'result',
  },
  {
    title: '异常',
    dataIndex: 'exception',
    ellipsis: true,
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
  },
])
</script>

<template>
  <Dialog v-model:visible="visible" header="reload结果列表" :style="{ width: '60vw' }" @hide="onClose">
    <div class="mb-3">
      <Button icon="" label="刷新" size="small" @click="ajaxQuery">
        <template #icon>
          <Icon icon="reload" />
        </template>
      </Button>
    </div>
    <DataTable
      :value="tableData"
      size="small"
      striped-rows
      row-key="id"
      :scrollable="true"
      scroll-height="350px"
      :expanded-rows="expandedRows"
      @row-toggle="onRowToggle"
    >
      <Column field="tag" header="标签" />
      <Column field="args" header="参数" />
      <Column field="result" header="运行结果">
        <template #body="{ data }">
          <Tag :severity="data.result ? 'success' : 'danger'">
            {{ data.result ? '成功' : '失败' }}
          </Tag>
        </template>
      </Column>
      <Column field="exception" header="异常" />
      <Column field="createTime" header="创建时间" />
      <Column :expander="true" header-style="width: 3rem" />
      <template #expansion="{ data }">
        <div class="p-3">
          <pre class="text-xs m-0">
            {{ data.exception }}
          </pre>
        </div>
      </template>
    </DataTable>
  </Dialog>
</template>
