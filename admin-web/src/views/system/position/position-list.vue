<!--
 * 职务管理
-->
<script setup lang="ts">
import type { Position } from '@/api/system/position/position-api'
import { AxLoading } from '@/components/base/ax-loading'
import { useConfirm } from 'primevue/useconfirm'
import { useToast } from 'primevue/usetoast'
import PositionFormModal from './components/position-form-modal.vue'
import { positionColumns } from './config/columns'
import { PositionService } from './service/positionService'

// 服务层注入
const positionService = new PositionService()
positionService.provide()

// PrimeVue hooks 初始化
const toast = useToast()
const { require: requireConfirmation } = useConfirm()

// 表单组件引用
const formRef = ref()

// 生命周期
onMounted(() => {
  positionService.queryPositionData()
})

// 查询操作
function handleSearch() {
  positionService.queryPositionData()
}

// 重置操作
function handleReset() {
  positionService.resetQuery()
}

// 新增操作
function handleAdd() {
  formRef.value.showAddModal()
}

// 编辑操作
function handleEdit(data: Position) {
  formRef.value.showEditModal(data)
}

// 删除确认
function handleDelete(data: Position) {
  requireConfirmation({
    message: '确定要删除这个职务吗？',
    header: '确认删除',
    acceptLabel: '删除',
    rejectLabel: '取消',
    accept: () => {
      requestDelete(data.positionId)
    },
  })
}

// 执行删除
async function requestDelete(positionId: number) {
  AxLoading.show()
  try {
    const success = await positionService.deletePosition(positionId)
    if (success) {
      toast.add({ severity: 'success', summary: '删除成功', life: 3000 })
      positionService.refresh()
    }
    else {
      toast.add({ severity: 'error', summary: '删除失败', life: 3000 })
    }
  }
  finally {
    AxLoading.hide()
  }
}

// 批量删除确认
function handleBatchDelete() {
  const selectedIds = positionService.getSelectedPositionIds()
  if (selectedIds.length === 0) {
    toast.add({ severity: 'warn', summary: '请选择要删除的数据', life: 3000 })
    return
  }
  requireConfirmation({
    message: '确定要批量删除这些职务吗？',
    header: '确认批量删除',
    acceptLabel: '删除',
    rejectLabel: '取消',
    accept: () => {
      requestBatchDelete(selectedIds)
    },
  })
}

// 执行批量删除
async function requestBatchDelete(positionIds: number[]) {
  AxLoading.show()
  try {
    const success = await positionService.batchDeletePosition(positionIds)
    if (success) {
      toast.add({ severity: 'success', summary: '删除成功', life: 3000 })
      positionService.clearSelection()
      positionService.refresh()
    }
    else {
      toast.add({ severity: 'error', summary: '删除失败', life: 3000 })
    }
  }
  finally {
    AxLoading.hide()
  }
}

// 分页处理 - CustomTable格式
function handlePageChange(pageNum: number, pageSize: number) {
  positionService.queryForm.pageNum = pageNum
  positionService.queryForm.pageSize = pageSize
  positionService.queryPositionData()
}

// 行选择处理
const selectedRowKeys = ref<number[]>([])

const rowSelection = {
  selectedRowKeys: selectedRowKeys.value,
  onChange: (keys: number[], rows: Position[]) => {
    selectedRowKeys.value = keys
    positionService.selectedItems.value = rows
  },
}
</script>

<template>
  <!-- 查询条件区域 -->
  <Card>
    <Form v-privilege="'system:position:query'">
      <Grid :cols="4" :gap="4">
        <GridCol>
          <FormItem label="关键字查询">
            <InputText
              v-model="positionService.queryForm.keywords"
              placeholder="关键字查询"
              class="w-50"
            />
          </FormItem>
        </GridCol>
      </Grid>
    </Form>
  </Card>

  <!-- 主数据区域 -->
  <Card>
    <!-- 操作按钮组 -->
    <FlexRow class="mb-4">
      <div class="flex gap-2">
        <Button v-privilege="'system:position:add'" @click="handleAdd">
          <template #icon>
            <Icon icon-type="PlusOutlined" />
          </template>
          新增职务
        </Button>
        <Button
          v-privilege="'system:position:delete'"
          severity="danger"
          :disabled="positionService.selectedItems.value.length === 0"
          @click="handleBatchDelete"
        >
          <template #icon>
            <Icon icon-type="DeleteOutlined" />
          </template>
          批量删除
        </Button>
      </div>
      <div>
        <ButtonGroup>
          <Button @click="handleSearch">
            <template #icon>
              <Icon icon-type="SearchOutlined" />
            </template>
            查询
          </Button>
          <Button @click="handleReset">
            <template #icon>
              <Icon icon-type="ReloadOutlined" />
            </template>
            重置
          </Button>
        </ButtonGroup>
      </div>
    </FlexRow>

    <!-- 数据表格 -->
    <CustomTable
      :columns="positionColumns"
      :data-source="positionService.dataList.value"
      :loading="positionService.tableLoading.value"
      :total="positionService.total.value"
      :page-param="{ pageNum: positionService.queryForm.pageNum, pageSize: positionService.queryForm.pageSize }"
      :row-selection="rowSelection"
      row-key="positionId"
      show-pagination
      :on-page-change="handlePageChange"
    >
      <template #bodyCell="{ column, record }">
        <!-- 操作列 -->
        <template v-if="column.dataIndex === 'action'">
          <FlexRow justify="start">
            <IconAnchor
              v-privilege="'system:position:edit'"
              label="编辑"
              icon-type="EditOutlined"
              @click="handleEdit(record)"
            />
            <IconAnchor
              v-privilege="'system:position:delete'"
              label="删除"
              icon-type="DeleteOutlined"
              color="red"
              @click="handleDelete(record)"
            />
          </FlexRow>
        </template>
      </template>
    </CustomTable>
  </Card>

  <!-- 表单弹窗 -->
  <PositionFormModal ref="formRef" @refresh="positionService.refresh" />
</template>
