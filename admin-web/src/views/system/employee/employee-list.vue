<!--
  * 员工管理 - 主列表页面
  * 基于组织架构规范的标准业务模块实现
-->
<script setup lang="ts">
// 仅需导入业务相关内容
import { useToast } from 'primevue/usetoast'
import EmployeeDepartmentFormModal from './components/employee-department-form-modal.vue'
import EmployeeFormModal from './components/employee-form-modal.vue'
import EmployeePasswordDialog from './components/employee-password-dialog/index.vue'
import { employeeColumns } from './config/columns'
import { EmployeeService } from './service/employeeService'

// ----------------------- 服务层注入 ---------------------
// 服务注入
const employeeService = new EmployeeService()
employeeService.provide()

// ----------------------- 组件引用 ---------------------
const employeeFormModal = ref()
const employeeDepartmentFormModal = ref()
const employeePasswordDialog = ref()

// ----------------------- 业务方法 ---------------------
// 显示账号密码信息
function showAccount(accountName: string, passWord: string) {
  employeePasswordDialog.value.showModal(accountName, passWord)
}

// 展示表单弹窗
function showFormModal(rowData?: any) {
  if (rowData) {
    // 编辑模式
    const formData = {
      ...rowData,
      disabledFlag: rowData.disabledFlag ? 1 : 0,
    }
    employeeFormModal.value.showEditModal(formData)
  }
  else {
    // 新增模式
    const defaultData = employeeService.getDefaultFormData()
    employeeFormModal.value.showAddModal(defaultData)
  }
}

// 批量调整部门
function updateEmployeeDepartment() {
  if (employeeService.selectedItems.value.length === 0) {
    const toast = useToast()
    toast.add({
      severity: 'warn',
      summary: '提示',
      detail: '请选择要调整部门的员工',
      life: 3000,
    })
    return
  }
  const employeeIdArray = employeeService.selectedItems.value.map(e => e.employeeId)
  employeeDepartmentFormModal.value.showModal(employeeIdArray)
}

// 重置密码处理
async function handleResetPassword(id: number, name: string) {
  const result = await employeeService.resetPassword(id, name)
  if (result) {
    showAccount(result.name, result.passWord)
  }
}

// ----------------------- 计算属性 ---------------------
// 是否有选中项：用于批量操作按钮状态
const hasSelected = computed(() => employeeService.selectedItems.value.length > 0)

// ----------------------- 行选择处理 ---------------------
const selectedRowKeys = ref<number[]>([])

const rowSelection = {
  selectedRowKeys: selectedRowKeys.value,
  onChange: (keys: number[], rows: any[]) => {
    selectedRowKeys.value = keys
    employeeService.selectedItems.value = rows
  },
}

// ----------------------- 分页处理 ---------------------
function handlePageChange(pageNum: number, pageSize: number) {
  employeeService.handlePageChange(pageNum, pageSize)
}

// ----------------------- 生命周期 ---------------------
onMounted(() => {
  employeeService.queryEmployeeData()
})
</script>

<template>
  <!-- 1. 查询条件区域 -->
  <Card class="mb-4">
    <Form v-privilege="'system:employee:query'" layout="vertical">
      <Grid>
        <GridCol>
          <FormItem label="部门选择">
            <AxDepartmentTree
              v-model="employeeService.queryParams.departmentId"
              @change="employeeService.handleDepartmentChange"
            />
          </FormItem>
        </GridCol>

        <GridCol>
          <FormItem label="关键字搜索">
            <InputText
              v-model.trim="employeeService.queryParams.keyword"
              placeholder="姓名/手机号/登录账号"
              @keyup.enter="employeeService.queryByKeyword(true)"
            />
          </FormItem>
        </GridCol>
        <GridCol>
          <FormItem label="员工状态" type="none">
            <SelectButton
              v-model="employeeService.queryParams.disabledFlag"
              :options="[
                { label: '全部', value: undefined },
                { label: '启用', value: false },
                { label: '禁用', value: true },
              ]"
              option-label="label"
              option-value="value"
              @change="employeeService.queryByKeyword(false)"
            />
          </FormItem>
        </GridCol>
      </Grid>
    </Form>
  </Card>

  <!-- 2. 主数据区域 -->
  <Card class="h-full">
    <!-- 操作按钮组 -->
    <FlexRow class="mb-4" justify="between">
      <div class="flex gap-2">
        <Button
          v-privilege="'system:employee:add'"
          severity="primary"
          @click="showFormModal()"
        >
          <Icon icon-type="PlusOutlined" />
          添加成员
        </Button>
        <Button
          v-privilege="'system:employee:department:update'"
          :disabled="!hasSelected"
          @click="updateEmployeeDepartment"
        >
          <Icon icon-type="SwapOutlined" />
          调整部门
        </Button>
        <Button
          v-privilege="'system:employee:delete'"
          severity="danger"
          :disabled="!hasSelected"
          @click="employeeService.batchDeleteEmployee"
        >
          <Icon icon-type="DeleteOutlined" />
          批量删除
        </Button>
      </div>

      <ButtonGroup>
        <Button
          severity="primary"
          @click="employeeService.queryByKeyword(true)"
        >
          <Icon icon-type="SearchOutlined" />
          查询
        </Button>
        <Button
          severity="secondary"
          outlined
          @click="employeeService.reset"
        >
          <Icon icon-type="ReloadOutlined" />
          重置
        </Button>
      </ButtonGroup>
    </FlexRow>

    <!-- 数据表格 -->
    <CustomTable
      :columns="employeeColumns"
      :data-source="employeeService.dataList.value"
      :loading="employeeService.tableLoading.value"
      :total="employeeService.total.value"
      :page-param="{ pageNum: employeeService.queryParams.pageNum, pageSize: employeeService.queryParams.pageSize }"
      :row-selection="rowSelection"
      row-key="employeeId"
      show-pagination
      :on-page-change="handlePageChange"
    >
      <template #bodyCell="{ column, record }">
        <!-- 性别列 -->
        <template v-if="column.dataIndex === 'gender'">
          <span>{{ ($smartEnumPlugin as any)?.getDescByValue('GENDER_ENUM', record.gender) }}</span>
        </template>

        <!-- 超管列 -->
        <template v-else-if="column.dataIndex === 'administratorFlag'">
          <Tag v-if="record.administratorFlag" severity="danger" value="超管" />
        </template>

        <!-- 状态列 -->
        <template v-else-if="column.dataIndex === 'disabledFlag'">
          <Tag :severity="record.disabledFlag ? 'danger' : 'success'" :value="record.disabledFlag ? '禁用' : '启用'" />
        </template>

        <!-- 操作列 -->
        <template v-else-if="column.dataIndex === 'action'">
          <FlexRow justify="start">
            <IconAnchor
              v-privilege="'system:employee:update'"
              label="编辑"
              icon-type="EditOutlined"
              @click="showFormModal(record)"
            />
            <IconAnchor
              v-privilege="'system:employee:password:reset'"
              label="重置密码"
              icon-type="KeyOutlined"
              @click="handleResetPassword(record.employeeId, record.loginName)"
            />
            <IconAnchor
              v-privilege="'system:employee:disabled'"
              :label="record.disabledFlag ? '启用' : '禁用'"
              :icon-type="record.disabledFlag ? 'CheckOutlined' : 'StopOutlined'"
              :color="record.disabledFlag ? 'green' : 'red'"
              @click="employeeService.updateDisabled(record.employeeId, record.disabledFlag)"
            />
          </FlexRow>
        </template>
      </template>
    </CustomTable>

    <!-- 3. 表单弹窗 -->
    <EmployeeFormModal
      ref="employeeFormModal"
      @refresh="employeeService.refresh"
      @show-account="showAccount"
    />
    <EmployeeDepartmentFormModal
      ref="employeeDepartmentFormModal"
      @refresh="employeeService.refresh"
    />
    <EmployeePasswordDialog ref="employeePasswordDialog" />
  </Card>
</template>
