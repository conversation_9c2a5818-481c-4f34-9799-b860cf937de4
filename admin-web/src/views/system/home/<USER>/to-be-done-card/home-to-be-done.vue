<!--
  * 待办工作
-->
<script setup lang="ts">
import localKey from '@/constants/local-storage-key-const'
import { useUserStore } from '@/store/modules/system/user.js'
import { localRead, localSave } from '@/utils/local-util'
import DefaultHomeCard from '@/views/system/home/<USER>/default-home-card.vue'
import { useConfirm } from 'primevue/useconfirm'
import { computed, onMounted, ref } from 'vue'
import ToBeDoneModal from './to-be-done-modal.vue'

const toBeDoneList = ref([])

onMounted(() => {
  initTaskList()
})

function initTaskList() {
  const localTaskList = localRead(localKey.TO_BE_DONE)
  if (localTaskList) {
    toBeDoneList.value = JSON.parse(localTaskList)
  }
}

const toDoList = computed(() => {
  return toBeDoneList.value.filter(e => !e.doneFlag)
})

const doneList = computed(() => {
  return toBeDoneList.value.filter(e => e.doneFlag)
})

function handleCheckbox() {
  localSave(localKey.TO_BE_DONE, JSON.stringify(toBeDoneList.value))
  useUserStore().toBeDoneCount = toDoList.value.length
}

function itemStar(data) {
  data.starFlag = !data.starFlag
  // 将取消 star 的删除掉
  const index = toBeDoneList.value.findIndex(item => item.title === data.title)
  toBeDoneList.value.splice(index, 1)
  if (data.starFlag) {
    // 最新添加标记star的移动到第一位
    toBeDoneList.value.unshift(data)
  }
  else {
    // 取消标记star的移动到最后一个标记 star 的后面添加
    const lastStarIndex = toBeDoneList.value.findLastIndex(item => item.starFlag)
    toBeDoneList.value.splice(lastStarIndex + 1, 0, data)
  }
  localSave(localKey.TO_BE_DONE, JSON.stringify(toBeDoneList.value))
}

// -------------------------任务新建-----------------------

const toBeDoneModalRef = ref()

function showAddToBeDone() {
  toBeDoneModalRef.value.showModal()
}

// 添加待办工作
function addToBeDone(data) {
  toBeDoneList.value.push(data)
  localSave(localKey.TO_BE_DONE, JSON.stringify(toBeDoneList.value))
  useUserStore().toBeDoneCount = toDoList.value.length
}

const { require: requireConfirmation } = useConfirm()

function toDelete(data) {
  if (!data.doneFlag) {
    requireConfirmation({
      message: '确定要删除吗?',
      header: '提示',
      icon: 'pi pi-exclamation-triangle',
      acceptLabel: '删除',
      rejectLabel: '取消',
      accept: () => {
        deleteToBeDone(data)
      },
    })
  }
  else {
    deleteToBeDone(data)
  }
}

// 删除待办工作
function deleteToBeDone(data) {
  const index = toBeDoneList.value.findIndex(item => item.title === data.title)
  toBeDoneList.value.splice(index, 1)
  localSave(localKey.TO_BE_DONE, JSON.stringify(toBeDoneList.value))
  useUserStore().toBeDoneCount = toDoList.value.length
}
</script>

<template>
  <DefaultHomeCard extra="添加" icon="StarTwoTone" title="待办工作" @extra-click="showAddToBeDone">
    <div class="h-70">
      <div class="flex justify-center h-full overflow-y-auto flex-col w-full px-2.5 justify-start">
        <div class="flex flex-col gap-2 w-full">
          <div v-if="$lodash.isEmpty(toBeDoneList)" class="flex flex-col items-center justify-center py-8 text-surface-500">
            <i class="pi pi-inbox text-4xl mb-2" />
            <span>暂无待办工作</span>
          </div>
          <div v-for="(item, index) in toDoList" :key="index" class="w-full border border-gray-300 rounded p-1 flex items-center" :class="[item.doneFlag && 'line-through text-gray-400']">
            <Checkbox v-model="item.doneFlag" @change="handleCheckbox">
              <span :class="[item.doneFlag && 'text-gray-400']">{{ item.title }}</span>
            </Checkbox>
            <div v-if="!item.doneFlag" class="ml-auto cursor-pointer" @click="itemStar(item)">
              <Icon v-if="item.starFlag" icon-type="StarFilled" style="color: #ff8c00" />
              <Icon v-else icon-type="StarOutlined" style="color: #c0c0c0" />
            </div>
            <Icon icon-type="CloseCircleOutlined" class="text-red-300 pl-2.5 -top-1.25 -right-1.25 float-right" @click="toDelete(item)" />
          </div>
          <div v-for="(item, index) in doneList" :key="index" class="w-full border border-gray-300 rounded p-1 flex items-center" :class="[item.doneFlag && 'line-through text-gray-400']">
            <Checkbox v-model="item.doneFlag" @change="handleCheckbox">
              <span :class="[item.doneFlag && 'text-gray-400']">{{ item.title }}</span>
            </Checkbox>
            <div v-if="!item.doneFlag" class="ml-auto cursor-pointer" @click="itemStar(item)">
              <Icon v-if="item.starFlag" icon-type="StarFilled" style="color: #ff8c00" />
              <Icon v-else icon-type="StarOutlined" style="color: #c0c0c0" />
            </div>
            <Icon icon-type="CloseCircleOutlined" class="text-red-300 pl-2.5 -top-1.25 -right-1.25 float-right" @click="toDelete(item)" />
          </div>
        </div>
      </div>
    </div>
  </DefaultHomeCard>
  <ToBeDoneModal ref="toBeDoneModalRef" @add-to-be-done="addToBeDone" />
</template>
