<script setup lang="ts">
import { computed } from 'vue'

const props = defineProps({
  icon: String,
  title: String,
  extra: String,
})
const emits = defineEmits(['extraClick'])

function extraClick() {
  emits('extraClick')
}

const primaryColor = computed(() => {
  return '#1677ff' // PrimeVue 默认主色
})
</script>

<template>
  <div class="card-container h-full">
    <Card class="h-full">
      <template #header>
        <div class="title flex items-center relative pl-4">
          <Icon v-if="props.icon" :icon-type="props.icon" class="text-lg mr-2" :style="{ color: primaryColor }" />
          <slot name="title" />
          <span v-if="!$slots.title" class="ml-2">{{ props.title }}</span>
          <div v-if="props.extra" class="ml-auto">
            <slot name="extra" />
            <a v-if="!$slots.extra" class="text-primary cursor-pointer" @click="extraClick">{{ props.extra }}</a>
          </div>
        </div>
      </template>
      <slot />
    </Card>
  </div>
</template>

<style scoped>
.title::before {
  content: '';
  position: absolute;
  top: 3px;
  left: 0;
  width: 3px;
  height: 30px;
  background-color: v-bind('primaryColor');
}
</style>
