<script setup lang="ts">
import { messageApi } from '@/api/support/message-api'
import SmartEnumSelect from '@/components/base/enum-select/index.vue'
import { PAGE_SIZE, PAGE_SIZE_OPTIONS } from '@/constants/common-const'
import { sentry } from '@/lib/sentry'
import { onMounted, reactive, ref } from 'vue'
import MessageDetail from './components/message-detail.vue'

const queryFormState = {
  searchWord: '',
  messageType: null,
  dataId: null,
  readFlag: null,
  endDate: null,
  startDate: null,
  pageNum: 1,
  pageSize: PAGE_SIZE,
  searchCount: true,
  receiverType: null,
  receiverId: null,
}
const queryForm = reactive({ ...queryFormState })
const tableLoading = ref(false)
const tableData = ref([])
const total = ref(0)

// 日期选择
const searchDate = ref<Date[]>()

function dateChange(value: Date | Date[] | null) {
  if (Array.isArray(value) && value.length === 2 && value[0] && value[1]) {
    queryForm.startDate = value[0].toISOString().split('T')[0]
    queryForm.endDate = value[1].toISOString().split('T')[0]
  }
  else {
    queryForm.startDate = undefined
    queryForm.endDate = undefined
  }
}

function resetQuery() {
  searchDate.value = []
  Object.assign(queryForm, queryFormState)
  ajaxQuery()
}

function quickQuery() {
  queryForm.pageNum = 1
  ajaxQuery()
}

// 查询
async function ajaxQuery() {
  try {
    tableLoading.value = true
    const responseModel = await messageApi.queryMessage(queryForm)
    const list = responseModel.data.list
    total.value = responseModel.data.total
    // Aniruf到此一游
    console.log(responseModel.data)
    tableData.value = list
  }
  catch (e) {
    sentry.captureError(e)
  }
  finally {
    tableLoading.value = false
  }
}

// -------------------- 详情 -----------------------------------

const messageDetailRef = ref()

function toDetail(message) {
  messageDetailRef.value.show(message)
}

onMounted(ajaxQuery)
</script>

<template>
  <Form class="mb-4">
    <Grid :cols="5" :gap="4">
      <GridCol>
        <FormItem label="关键字">
          <InputText v-model="queryForm.searchWord" placeholder="标题/内容" class="w-75" />
        </FormItem>
      </GridCol>

      <GridCol>
        <FormItem label="类型">
          <SmartEnumSelect v-model="queryForm.messageType" placeholder="消息类型" enum-name="MESSAGE_TYPE_ENUM" class="w-40" />
        </FormItem>
      </GridCol>

      <GridCol>
        <FormItem label="消息时间">
          <DateRangePicker v-model="searchDate" class="w-55" @update:model-value="dateChange" />
        </FormItem>
      </GridCol>

      <GridCol>
        <FormItem>
          <SelectButton
            v-model="queryForm.readFlag" :options="[
              { label: '全部', value: null },
              { label: '未读', value: false },
              { label: '已读', value: true },
            ]" option-label="label" option-value="value" @change="quickQuery"
          />
        </FormItem>
      </GridCol>

      <GridCol>
        <FormItem>
          <ButtonGroup>
            <Button severity="info" @click="quickQuery">
              <template #icon>
                <Icon icon-type="SearchOutlined" />
              </template>
              查询
            </Button>
            <Button severity="secondary" @click="resetQuery">
              <template #icon>
                <Icon icon-type="ReloadOutlined" />
              </template>
              重置
            </Button>
          </ButtonGroup>
        </FormItem>
      </GridCol>
    </Grid>
  </Form>

  <DataTable :value="tableData" :loading="tableLoading" size="small" striped-rows>
    <Column field="title" header="消息">
      <template #body="{ data }">
        <span v-if="data.readFlag">
          <a class="text-gray-400 cursor-pointer" @click="toDetail(data)">【{{ $smartEnumPlugin.getDescByValue('MESSAGE_TYPE_ENUM', data.messageType) }}】{{ data.title }}</a>
        </span>
        <span v-else>
          <a class="cursor-pointer" @click="toDetail(data)">【{{ $smartEnumPlugin.getDescByValue('MESSAGE_TYPE_ENUM', data.messageType) }}】{{ data.title }}</a>
        </span>
      </template>
    </Column>
    <Column field="readFlag" header="已读" class="w-20">
      <template #body="{ data }">
        <span v-if="data.readFlag">已读</span>
        <span v-else class="text-red-500">未读</span>
      </template>
    </Column>
    <Column field="createTime" header="时间" class="w-45" />
  </DataTable>

  <div class="flex justify-between items-center mt-4">
    <div class="text-sm text-gray-600">
      共 {{ total }} 条记录
    </div>
    <Paginator
      :first="(queryForm.pageNum - 1) * queryForm.pageSize"
      :rows="queryForm.pageSize"
      :total-records="total"
      :rows-per-page-options="PAGE_SIZE_OPTIONS.map(Number)"
      template="FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink RowsPerPageDropdown"
      @page="(event) => { queryForm.pageNum = event.page + 1; queryForm.pageSize = event.rows; ajaxQuery(); }"
    />
  </div>

  <MessageDetail ref="messageDetailRef" @refresh="ajaxQuery" />
</template>
