<!--
  * 登录、登出 日志
-->
<script setup lang="ts">
import { loginLogApi } from '@/api/support/login-log-api'
import { PAGE_SIZE_OPTIONS } from '@/constants/common-const'
import { LOGIN_RESULT_ENUM } from '@/constants/support/login-log-const'
import { defaultTimeRanges } from '@/lib/default-time-ranges'
import { sentry } from '@/lib/sentry'
// import { calcTableHeight } from '@/lib/table-auto-height'
import uaparser from 'ua-parser-js'
import { onMounted, reactive, ref } from 'vue'

const queryFormState = {
  userName: '',
  ip: '',
  startDate: undefined as string | undefined,
  endDate: undefined as string | undefined,
  pageNum: 1,
  pageSize: 10,
}
const queryForm = reactive({ ...queryFormState })
const createDateRange = ref<Date[] | null>(null)
const defaultChooseTimeRange = defaultTimeRanges
// 时间变动
function changeCreateDate(value: Date | Date[] | null) {
  if (Array.isArray(value) && value.length === 2 && value[0] && value[1]) {
    queryForm.startDate = value[0].toISOString().split('T')[0]
    queryForm.endDate = value[1].toISOString().split('T')[0]
  }
  else {
    queryForm.startDate = undefined
    queryForm.endDate = undefined
  }
}

const tableLoading = ref(false)
const tableData = ref([])
const total = ref(0)

function resetQuery() {
  Object.assign(queryForm, queryFormState)
  createDateRange.value = null
  ajaxQuery()
}

function onSearch() {
  queryForm.pageNum = 1
  ajaxQuery()
}

async function ajaxQuery() {
  try {
    tableLoading.value = true
    const responseModel = await loginLogApi.queryListLogin(queryForm)

    for (const e of responseModel.data.list) {
      if (!e.userAgent) {
        continue
      }
      const ua = uaparser(e.userAgent)
      e.browser = ua.browser.name
      e.os = ua.os.name
      e.device = ua.device.vendor ? ua.device.vendor + ua.device.model : ''
    }

    const list = responseModel.data.list
    total.value = responseModel.data.total
    tableData.value = list
    console.log(responseModel.data)
  }
  catch (e) {
    sentry.captureError(e)
  }
  finally {
    tableLoading.value = false
  }
}

// ----------------- 表格自适应高度 --------------------
// const scrollY = ref(100)
const queryFormRef = ref()

// function autoCalcTableHeight() {
//   calcTableHeight(scrollY, [queryFormRef], 10)
// }

// window.addEventListener('resize', autoCalcTableHeight)

onMounted(() => {
  ajaxQuery()
  // autoCalcTableHeight()
})

// onUnmounted(() => {
//   window.removeEventListener('resize', autoCalcTableHeight)
// })
</script>

<template>
  <Form ref="queryFormRef" class="mb-4">
    <Grid :cols="4" :gap="4">
      <GridCol>
        <FormItem label="时间">
          <DateRangePicker
            v-model="createDateRange"
            :presets="defaultChooseTimeRange"
            class="w-60"
            @update:model-value="changeCreateDate"
          />
        </FormItem>
      </GridCol>

      <GridCol>
        <FormItem>
          <ButtonGroup>
            <Button severity="primary" @click="onSearch">
              <template #icon>
                <Icon icon-type="SearchOutlined" />
              </template>
              查询
            </Button>
            <Button @click="resetQuery">
              <template #icon>
                <Icon icon-type="ReloadOutlined" />
              </template>
              重置
            </Button>
          </ButtonGroup>
        </FormItem>
      </GridCol>
    </Grid>
  </Form>

  <DataTable
    :value="tableData"
    :loading="tableLoading"
    size="small"
    class="border"
  >
    <Column field="createTime" header="时间" style="width: 150px" />
    <Column field="remark" header="登录方式" style="width: 90px" class="truncate" />
    <Column field="userAgent" header="登录设备" class="truncate">
      <template #body="{ data }">
        <div>{{ data.browser }} / {{ data.os }} / {{ data.device }}</div>
      </template>
    </Column>
    <Column field="loginIpRegion" header="IP地区" class="truncate" />
    <Column field="loginIp" header="IP" style="width: 120px" class="truncate" />
    <Column field="loginResult" header="结果" style="width: 90px">
      <template #body="{ data }">
        <Tag
          v-if="data.loginResult === LOGIN_RESULT_ENUM.LOGIN_SUCCESS.value"
          severity="success"
          value="登录成功"
        />
        <Tag
          v-else-if="data.loginResult === LOGIN_RESULT_ENUM.LOGIN_FAIL.value"
          severity="warn"
          value="登录失败"
        />
        <Tag
          v-else-if="data.loginResult === LOGIN_RESULT_ENUM.LOGIN_OUT.value"
          severity="info"
          value="退出登录"
        />
      </template>
    </Column>
  </DataTable>

  <div class="flex justify-center mt-4">
    <Paginator
      :first="(queryForm.pageNum - 1) * queryForm.pageSize"
      :rows="queryForm.pageSize"
      :total-records="total"
      :rows-per-page-options="PAGE_SIZE_OPTIONS.map(Number)"
      template="FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink RowsPerPageDropdown"
      @page="(event) => { queryForm.pageNum = event.page + 1; queryForm.pageSize = event.rows; ajaxQuery(); }"
    >
      <template #start>
        <span class="text-sm text-gray-600">共 {{ total }} 条</span>
      </template>
    </Paginator>
  </div>
</template>
