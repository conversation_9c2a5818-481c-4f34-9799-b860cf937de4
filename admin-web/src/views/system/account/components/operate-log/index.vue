<!--
  * 操作记录 列表
-->
<script setup lang="ts">
import { operateLogApi } from '@/api/support/operate-log-api'
import { PAGE_SIZE_OPTIONS } from '@/constants/common-const'
import { defaultTimeRanges } from '@/lib/default-time-ranges'
import { sentry } from '@/lib/sentry'
import OperateLogDetailModal from '@/views/support/operate-log/operate-log-detail-modal.vue'
import uaparser from 'ua-parser-js'
import { onMounted, reactive, ref } from 'vue'

const queryFormState = {
  userName: '',
  successFlag: undefined as boolean | undefined,
  startDate: undefined as string | undefined,
  endDate: undefined as string | undefined,
  pageNum: 1,
  pageSize: 10,
}
const queryForm = reactive({ ...queryFormState })
const createDateRange = ref<Date[] | null>(null)
const defaultChooseTimeRange = defaultTimeRanges
// 时间变动
function changeCreateDate(value: Date | Date[] | null) {
  if (Array.isArray(value) && value.length === 2 && value[0] && value[1]) {
    queryForm.startDate = value[0].toISOString().split('T')[0]
    queryForm.endDate = value[1].toISOString().split('T')[0]
  }
  else {
    queryForm.startDate = undefined
    queryForm.endDate = undefined
  }
}

const tableLoading = ref(false)
const tableData = ref([])
const total = ref(0)

function resetQuery() {
  Object.assign(queryForm, queryFormState)
  createDateRange.value = null
  ajaxQuery()
}

function onSearch() {
  queryForm.pageNum = 1
  ajaxQuery()
}

async function ajaxQuery() {
  try {
    tableLoading.value = true
    const responseModel = await operateLogApi.queryListLogin(queryForm)

    for (const e of responseModel.data.list) {
      if (!e.userAgent) {
        continue
      }
      const ua = uaparser(e.userAgent)
      e.browser = ua.browser.name
      e.os = ua.os.name
      e.device = ua.device.vendor ? ua.device.vendor + ua.device.model : ''
    }

    const list = responseModel.data.list
    total.value = responseModel.data.total
    tableData.value = list
  }
  catch (e) {
    sentry.captureError(e)
  }
  finally {
    tableLoading.value = false
  }
}

onMounted(ajaxQuery)

// ---------------------- 详情 ----------------------
const detailModal = ref()
function showDetail(operateLogId: string) {
  detailModal.value.show(operateLogId)
}
</script>

<template>
  <Form class="mb-4">
    <Grid :cols="4" :gap="4">
      <GridCol>
        <FormItem label="请求时间">
          <DateRangePicker
            v-model="createDateRange"
            :presets="defaultChooseTimeRange"
            class="w-60"
            @update:model-value="changeCreateDate"
          />
        </FormItem>
      </GridCol>

      <GridCol>
        <FormItem label="快速筛选">
          <SelectButton
            v-model="queryForm.successFlag"
            :options="[
              { label: '全部', value: undefined },
              { label: '成功', value: true },
              { label: '失败', value: false },
            ]"
            option-label="label"
            option-value="value"
            @change="onSearch"
          />
        </FormItem>
      </GridCol>

      <GridCol>
        <FormItem>
          <ButtonGroup>
            <Button severity="primary" @click="ajaxQuery">
              <template #icon>
                <Icon icon-type="ReloadOutlined" />
              </template>
              查询
            </Button>
            <Button @click="resetQuery">
              <template #icon>
                <Icon icon-type="SearchOutlined" />
              </template>
              重置
            </Button>
          </ButtonGroup>
        </FormItem>
      </GridCol>
    </Grid>
  </Form>

  <DataTable
    :value="tableData"
    :loading="tableLoading"
    size="small"
    class="border overflow-y-auto"
  >
    <Column field="module" header="操作模块" style="width: 120px" class="truncate" />
    <Column field="content" header="操作内容" class="truncate" />
    <Column field="ipRegion" header="IP地区" style="width: 120px" class="truncate" />
    <Column field="userAgent" header="客户端" style="width: 140px" class="truncate">
      <template #body="{ data }">
        <div>{{ data.browser }} / {{ data.os }} / {{ data.device }}</div>
      </template>
    </Column>
    <Column field="createTime" header="时间" style="width: 150px" />
    <Column field="successFlag" header="结果" style="width: 60px">
      <template #body="{ data }">
        <!-- 成功为绿色，失败为红色. 不知道unocss语法怎么搞，所以就先这样写了 Aniruf -->
        <Tag
          :severity="data.successFlag ? 'success' : 'danger'"
          :value="data.successFlag ? '成功' : '失败'"
        />
      </template>
    </Column>
    <Column header="操作" style="width: 60px">
      <template #body="{ data }">
        <Button
          text
          severity="info"
          size="small"
          @click="showDetail(data.operateLogId)"
        >
          详情
        </Button>
      </template>
    </Column>
  </DataTable>

  <div class="flex justify-center mt-4">
    <Paginator
      :first="(queryForm.pageNum - 1) * queryForm.pageSize"
      :rows="queryForm.pageSize"
      :total-records="total"
      :rows-per-page-options="PAGE_SIZE_OPTIONS.map(Number)"
      template="FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink RowsPerPageDropdown"
      @page="(event) => { queryForm.pageNum = event.page + 1; queryForm.pageSize = event.rows; ajaxQuery(); }"
    >
      <template #start>
        <span class="text-sm text-gray-600">共 {{ total }} 条</span>
      </template>
    </Paginator>
  </div>

  <OperateLogDetailModal ref="detailModal" />
</template>
