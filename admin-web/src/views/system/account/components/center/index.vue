<script setup lang="ts">
import { fileApi } from '@/api/support/file-api.js'
import { employeeApi } from '@/api/system/employee/employee-api'
import { loginApi } from '@/api/system/login-api.js'
import { AxLoading } from '@/components/base/ax-loading/index.js'
import DepartmentTreeSelect from '@/components/system/department-tree-select/index.vue'
import DictSelect from '@/components/utils/Dict/DictSelect.vue'
import { regular } from '@/constants/regular-const.js'
import { FILE_FOLDER_TYPE_ENUM } from '@/constants/support/file-const.js'
import { sentry } from '@/lib/sentry'
import { useUserStore } from '@/store/modules/system/user.js'
import { useToast } from 'primevue/usetoast'
import { onMounted, reactive, ref } from 'vue'

// 组件ref
const formRef = ref<{ submit: () => Promise<{ valid: boolean }> }>()
const departmentTreeSelectRef = ref<{ queryDepartmentTree: () => Promise<void> }>()
const toast = useToast()

const formDefault = {
  // 员工ID
  employeeId: undefined,
  // 头像
  avatar: undefined,
  // 登录账号
  loginName: '',
  // 员工名称
  actualName: '',
  // 性别
  gender: undefined,
  // 手机号码
  phone: '',
  // 部门id
  departmentId: undefined,
  // 是否启用
  disabledFlag: undefined,
  // 邮箱
  email: '',
  // 备注
  remark: '',
}
const form = reactive({ ...formDefault })
const rules = {
  actualName: [
    { required: true, message: '姓名不能为空' },
    { max: 30, message: '姓名不能大于30个字符', trigger: 'blur' },
  ],
  phone: [
    { required: true, message: '手机号不能为空' },
    { pattern: regular.phone, message: '请输入正确的手机号码', trigger: 'blur' },
  ],
  gender: [{ required: true, message: '性别不能为空' }],
  departmentId: [{ required: true, message: '部门不能为空' }],
  email: [
    { required: true, message: '邮箱不能为空' },
    { pattern: /^[\w.%+-]+@[a-z0-9.-]+\.[a-z]{2,}$/i, message: '请输入正确的邮箱地址', trigger: 'blur' },
  ],
}
// 头像地址
const avatarUrl = ref()

// 查询登录信息
async function getLoginInfo() {
  try {
    // 获取登录用户信息
    const res = await loginApi.getLoginInfo()
    const data = res.data
    // 更新用户信息到pinia
    useUserStore().setUserLoginInfo(data)
    // 当前form展示
    form.employeeId = data.employeeId
    form.loginName = data.loginName
    form.actualName = data.actualName
    form.email = data.email
    form.gender = data.gender
    form.phone = data.phone
    form.departmentId = data.departmentId
    form.disabledFlag = data.disabledFlag
    form.remark = data.remark
    // 头像展示
    avatarUrl.value = data.avatar

    // 手动加载部门数据
    if (departmentTreeSelectRef.value) {
      await departmentTreeSelectRef.value.queryDepartmentTree()
    }
  }
  catch (e) {
    sentry.captureError(e)
  }
}

// 头像上传
const accept = ref('.jpg,.jpeg,.png,.gif')
const maxSize = ref(10)
const folder = ref(FILE_FOLDER_TYPE_ENUM.COMMON.value)
const updateAvatarLoading = ref(false)
const fileInput = ref<HTMLInputElement>()

function handleFileChange(event: Event) {
  const file = (event.target as HTMLInputElement).files?.[0]
  if (file && beforeUpload(file)) {
    customRequest({ file })
  }
  // 清空input，允许重复上传同一文件
  ;(event.target as HTMLInputElement).value = ''
}

function beforeUpload(file: File) {
  const suffixIndex = file.name.lastIndexOf('.')
  const fileSuffix = file.name.substring(suffixIndex <= -1 ? 0 : suffixIndex)
  if (!accept.value.includes(fileSuffix)) {
    toast.add({ severity: 'error', summary: '文件格式错误', detail: `只支持上传 ${accept.value.split(',').join(' ')} 格式的文件`, life: 3000 })
    return false
  }

  const isLimitSize = file.size / 1024 / 1024 < maxSize.value
  if (!isLimitSize) {
    toast.add({ severity: 'error', summary: '文件大小超限', detail: `单个文件大小必须小于 ${maxSize.value} Mb`, life: 3000 })
    return false
  }
  return true
}

async function customRequest(options: { file: File }) {
  updateAvatarLoading.value = true
  try {
    const formData = new FormData()
    formData.append('file', options.file)
    const res = await fileApi.uploadFile(formData, folder.value)
    const file = res.data
    avatarUrl.value = file.fileUrl
    // 更新头像
    const updateAvatarForm = { avatar: file.fileKey }
    await employeeApi.updateEmployeeAvatar(updateAvatarForm)
    toast.add({ severity: 'success', summary: '操作成功', detail: '头像更新成功', life: 3000 })
    // 重新获取详情，刷新整体缓存
    await getLoginInfo()
  }
  catch (e) {
    sentry.captureError(e)
  }
  finally {
    updateAvatarLoading.value = false
  }
}

// 更新员工信息
async function updateEmployee() {
  AxLoading.show()
  try {
    // 构造符合API要求的表单数据
    const updateForm = {
      employeeId: form.employeeId,
      actualName: form.actualName,
      gender: form.gender,
      phone: form.phone,
      email: form.email || '', // 确保email不为undefined
      avatar: form.avatar,
      remark: form.remark,
    }
    await employeeApi.updateEmployeeCenter(updateForm)
    toast.add({ severity: 'success', summary: '操作成功', detail: '个人信息更新成功', life: 3000 })
    // 重新获取详情，刷新整体缓存
    await getLoginInfo()
  }
  catch (error) {
    sentry.captureError(error)
  }
  finally {
    AxLoading.hide()
  }
}

// 表单提交
async function onSubmit() {
  try {
    if (formRef.value) {
      const result = await formRef.value.submit()
      if (result.valid) {
        updateEmployee()
      }
      else {
        toast.add({ severity: 'error', summary: '验证失败', detail: '参数验证错误，请仔细填写表单数据!', life: 3000 })
      }
    }
  }
  catch {
    toast.add({ severity: 'error', summary: '验证失败', detail: '参数验证错误，请仔细填写表单数据!', life: 3000 })
  }
}

onMounted(() => {
  getLoginInfo()
})
</script>

<template>
  <div>
    <!--  页面标题 -->
    <div class="text-xl">
      个人中心
    </div>

    <!--  内容区域 -->
    <div class="mt-5">
      <div class="flex">
        <div class="flex-[0_0_350px]">
          <Form ref="formRef" :model="form" :rules="rules" layout="vertical">
            <FormItem label="登录账号" name="loginName">
              <InputText v-model="form.loginName" class="form-item" placeholder="请输入登录账号" disabled />
            </FormItem>
            <FormItem label="员工名称" name="actualName">
              <InputText v-model="form.actualName" class="form-item" placeholder="请输入员工名称" />
            </FormItem>
            <FormItem label="性别" name="gender">
              <DictSelect v-model="form.gender" class="form-item" placeholder="请选择性别" enum-name="GENDER_ENUM" />
            </FormItem>
            <FormItem label="手机号码" name="phone">
              <InputText v-model="form.phone" class="form-item" placeholder="请输入手机号码" />
            </FormItem>
            <FormItem label="邮箱" name="email">
              <InputText v-model="form.email" class="form-item" placeholder="请输入邮箱地址" />
            </FormItem>
            <FormItem label="部门" name="departmentId">
              <DepartmentTreeSelect ref="departmentTreeSelectRef" v-model="form.departmentId" class="form-item" :init="false" />
            </FormItem>
            <FormItem label="备注" name="remark">
              <CustomTextArea v-model="form.remark" class="form-item" placeholder="请输入备注" :rows="4" />
            </FormItem>
          </Form>
          <Button @click="onSubmit">
            更新个人信息
          </Button>
        </div>
        <div class="flex-auto">
          <Form class="pl-20" layout="vertical">
            <FormItem label="头像" name="avatar">
              <div class="mt-4">
                <input
                  ref="fileInput"
                  type="file"
                  :accept="accept"
                  class="hidden"
                  @change="handleFileChange"
                >
                <div
                  class="relative rounded-full overflow-hidden w-38 h-38 cursor-pointer border-2 border-dashed border-gray-300 hover:border-blue-400 transition-colors flex items-center justify-center"
                  @click="fileInput?.click()"
                >
                  <div v-if="avatarUrl" class="w-full h-full relative">
                    <img :src="avatarUrl" class="w-full h-full object-cover" alt="avatar">
                    <div class="absolute inset-0 bg-black bg-opacity-50 opacity-0 hover:opacity-100 transition-opacity flex items-center justify-center">
                      <span class="text-white text-base">更新头像</span>
                    </div>
                  </div>
                  <div v-else class="flex flex-col items-center justify-center">
                    <Icon v-if="updateAvatarLoading" icon-type="LoadingOutlined" class="text-2xl text-gray-400" />
                    <Icon v-else icon-type="PlusOutlined" class="text-2xl text-gray-400" />
                    <div class="mt-2 text-gray-600">
                      上传头像
                    </div>
                  </div>
                </div>
              </div>
            </FormItem>
          </Form>
        </div>
      </div>
    </div>
  </div>
</template>
