<!--
  * 菜单列表
-->
<script setup lang="ts">
import MenuForm from './components/MenuForm.vue'
import { menuService } from './service/MenuService'

menuService.provide()
</script>

<template>
  <FlexCol class="h-full">
    <!---------- 查询参数 begin ----------->
    <Card>
      <Form :ref="menuService.tableFormRef" v-privilege="'supplier:query'">
        <Grid>
          <!-- <GridCol>
            <FormItem label="关键字" name="menuNameKeyword">
              <InputText placeholder="菜单名称/路由地址/组件路径/权限字符串" />
            </FormItem>
          </GridCol> -->
          <GridCol>
            <FormItem label="类型" name="menuType">
              <EnumSelect enum-name="MENU_TYPE_ENUM" />
            </FormItem>
          </GridCol>
          <GridCol>
            <FormItem label="禁用" name="disabledFlag">
              <EnumSelect enum-name="FLAG_NUMBER_ENUM" />
            </FormItem>
          </GridCol>
          <GridCol>
            <FormItem label="外链" name="frameFlag">
              <EnumSelect enum-name="FLAG_NUMBER_ENUM" />
            </FormItem>
          </GridCol>
          <GridCol>
            <FormItem label="缓存" name="cacheFlag">
              <EnumSelect enum-name="FLAG_NUMBER_ENUM" />
            </FormItem>
          </GridCol>
          <GridCol>
            <FormItem label="显示" name="visibleFlag">
              <EnumSelect enum-name="FLAG_NUMBER_ENUM" />
            </FormItem>
          </GridCol>
        </Grid>
      </Form>
    </Card>
    <!---------- 查询参数 end ----------->

    <Card :hoverable="true">
      <!-- 操作按钮 -->
      <FlexRow>
        <FlexRow>
          <Button
            v-privilege="'system:menu:add'"
            icon="pi pi-plus"
            label="添加菜单"
            severity="info"
            @click="menuService.openAddForm()"
          />
          <Button
            v-privilege="'system:menu:batchDelete'"
            icon="pi pi-trash"
            label="批量删除"
            severity="danger"
          />
        </FlexRow>
        <ButtonGroup>
          <Button
            icon="pi pi-search"
            label="查询"
            severity="info"
            @click="menuService.onSearch"
          />
          <Button
            icon="pi pi-refresh"
            label="重置"
            severity="secondary"
            @click="menuService.resetQuery"
          />
        </ButtonGroup>
      </FlexRow>
      <br>

      <AxTreeTable>
        <template #bodyCell="{ column, record }">
          <!-- 只为操作列添加模板插槽 -->
          <template v-if="column.dataIndex === 'action'">
            <FlexRow justify="start">
              <IconAnchor
                v-privilege="'system:menu:update'" label="" icon-type="EditOutlined" @click="menuService.openEditForm(record.menuId)"
              />
              <IconAnchor
                v-privilege="'system:menu:delete'" label="" icon-type="DeleteOutlined" color="red" @click="menuService.deleteEntity(record.menuId)"
              />
            </FlexRow>
          </template>
        </template>
      </AxTreeTable>
    </Card>
  </FlexCol>
  <!-- 菜单表单 -->
  <MenuForm />
</template>
