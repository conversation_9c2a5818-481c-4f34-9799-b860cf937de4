import type { MenuResult } from '@/api/system/menu/model/menu-ex'
import type { DictColor } from '@/components/utils/Dict/type'
import type { TreeTableColumn } from '@/service/base/interface/TreeTable'
import Icon from '@/components/base/Icon/Icon.vue'
import EnumTag from '@/components/system/EnumTag/EnumTag.vue'
import { h } from 'vue'

export function getMenuTypeColor(type?: number): DictColor {
  switch (type) {
    case 1:
      return 'secondary'
    case 2:
      return 'default'
    case 3:
      return 'danger'
    default:
      return 'secondary'
  }
}

export function getPermsTypeColor(type?: number): DictColor {
  switch (type) {
    case 1:
      return 'success'
    case 2:
      return 'warning'
    default:
      return 'default'
  }
}

export function getStatusColor(flag?: boolean): DictColor {
  return flag ? 'success' : 'error'
}

export const columns: TreeTableColumn<MenuResult>[] = [
  {
    title: '菜单名称',
    dataIndex: 'menuName',
    expander: true,
    width: '12rem',
  },
  {
    title: '菜单类型',
    dataIndex: 'menuType',
    width: '6rem',
    customRender: (params) => {
      return h(EnumTag, {
        enumName: 'MENU_TYPE_ENUM',
        value: params.record.menuType,
        color: getMenuTypeColor(params.record.menuType),
      })
    },
  },
  {
    title: '菜单图标',
    dataIndex: 'icon',
    width: '6rem',
    customRender: (params) => {
      return h(Icon, {
        iconType: params.record.icon,
      })
    },
  },
  {
    title: '路由地址',
    dataIndex: 'path',
  },
  {
    title: '组件路径',
    dataIndex: 'component',
  },
  {
    title: '后端权限',
    dataIndex: 'apiPerms',
  },
  {
    title: '前端权限',
    dataIndex: 'webPerms',
  },
  {
    title: '显示顺序',
    dataIndex: 'sort',
  },
  {
    title: '操作',
    dataIndex: 'action',
    fixed: 'right',
    width: '12rem',
  },
]
