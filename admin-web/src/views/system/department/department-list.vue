<script lang="ts" setup>
import DepartmentForm from './components/DepartmentForm.vue'
import { columns } from './config/columns'
import { departmentService } from './service/departmentService'
import { employeeSearchServiceEX } from './service/employeeSearchService'

// 使用部门服务
departmentService.provide()
employeeSearchServiceEX.provide()

// 处理操作按钮点击
function handleActionClick(action: string, node: any) {
  const data = node.data
  switch (action) {
    case 'add':
      departmentService.openAddForm(data)
      break
    case 'edit':
      departmentService.openEditForm(data)
      break
    case 'delete':
      if (data.departmentId !== departmentService.topDepartmentId.value) {
        departmentService.deleteEntity(data.departmentId)
      }
      break
  }
}
</script>

<template>
  <Card :hoverable="true">
    <!---------- 表格操作行 begin ------------->
    <FlexRow>
      <div class="flex gap-2">
        <IconButton
          v-privilege="'system:department:add'"
          label="新建"
          icon-type="PlusOutlined"
          @click="() => departmentService.openAddForm()"
        />
      </div>
      <IconButton label="重置" icon-type="ReloadOutlined" @click="departmentService.resetQuery" />
    </FlexRow>
    <!---------- 表格操作行 end ------------->
    <br>

    <!---------- 部门树表格 begin ------------->
    <CustomTreeTable
      v-model="departmentService.departmentTreeData.value"
      :loading="departmentService.tableLoading.value"
      :expanded-keys="departmentService.expandedKeys.value"
      :columns="columns"
      @node-expand="departmentService.onNodeExpand"
      @node-collapse="departmentService.onNodeCollapse"
      @action-click="handleActionClick"
    >
      <template #actions="{ node, record, handleActionClick }">
        <FlexRow justify="start">
          <IconAnchor
            v-privilege="'system:department:add'"
            label="添加下级"
            icon-type="PlusOutlined"
            @click="handleActionClick('add', node)"
          />
          <IconAnchor
            v-privilege="'system:department:update'"
            label="编辑"
            icon-type="EditOutlined"
            @click="handleActionClick('edit', node)"
          />
          <IconAnchor
            v-if="record.departmentId !== departmentService.topDepartmentId.value"
            v-privilege="'system:department:delete'"
            label="删除"
            icon-type="DeleteOutlined"
            color="red"
            @click="handleActionClick('delete', node)"
          />
        </FlexRow>
      </template>
    </CustomTreeTable>
    <!---------- 部门树表格 end ------------->
  </Card>

  <!-- 部门表单弹窗 -->
  <DepartmentForm />
</template>
