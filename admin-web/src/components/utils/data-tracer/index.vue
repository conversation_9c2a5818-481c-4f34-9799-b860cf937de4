<!--
  *  数据变动记录 表格 组件
-->
<script setup lang="ts">
import { dataTracerApi } from '@/api/support/data-tracer-api'
import { PAGE_SIZE, PAGE_SIZE_OPTIONS } from '@/constants/common-const'
import { sentry } from '@/lib/sentry'
import * as Diff from 'diff'
import * as Diff2Html from 'diff2html'
import uaparser from 'ua-parser-js'
import { nextTick, ref, watch } from 'vue'
import DataTracerTimeline from './data-tracer-timeline.vue'
import 'diff2html/bundles/css/diff2html.min.css'

const props = defineProps({
  // 数据id
  dataId: {
    type: Number,
  },
  // 数据 类型
  type: {
    type: Number,
  },
})

// --------------- 查询表单、查询方法 ---------------

const queryForm = ref({
  pageNum: 1,
  pageSize: PAGE_SIZE,
  searchCount: true,
  keywords: undefined,
})
const tableLoading = ref(false)
const tableData = ref([])
const total = ref(0)

function onReload() {
  queryForm.value = {
    pageNum: 1,
    pageSize: PAGE_SIZE,
    searchCount: true,
    keywords: undefined,
  }
  onSearch()
}

function onSearch() {
  queryForm.value.pageNum = 1
  ajaxQuery()
}

async function ajaxQuery() {
  try {
    tableLoading.value = true
    const responseModel = await dataTracerApi.queryList({ ...queryForm.value, dataId: props.dataId, type: props.type })
    for (const e of responseModel.data.list) {
      if (!e.userAgent) {
        continue
      }
      // e.content = e.content.replaceAll('<br/>','；');
      const ua = uaparser(e.userAgent)
      e.browser = ua.browser.name
      e.os = ua.os.name
      e.device = ua.device.vendor ? ua.device.vendor + ua.device.model : ''
    }
    const list = responseModel.data.list
    total.value = responseModel.data.total
    tableData.value = list
  }
  catch (e) {
    sentry.captureError(e)
  }
  finally {
    tableLoading.value = false
  }
}

// ========= 定义 watch 监听 ===============
watch(
  () => props.dataId,
  (e) => {
    if (e) {
      queryForm.value.dataId = e
      onSearch()
    }
  },
  { immediate: true },
)

// --------------- diff 特效 ---------------
// diff
const visibleDiff = ref(false)
const prettyHtml = ref('')
function showDetail(record) {
  visibleDiff.value = true
  const diffOld = record.diffOld.replaceAll('<br/>', '\r\n')
  const diffNew = record.diffNew.replaceAll('<br/>', '\r\n')
  const args = ['', diffOld, diffNew, '变更前', '变更后']

  const diffPatch = Diff.createPatch(...args)
  const html = Diff2Html.html(diffPatch, {
    drawFileList: false,
    matching: 'words',
    diffMaxChanges: 1000,
    outputFormat: 'side-by-side',
  })

  prettyHtml.value = html
  nextTick(() => {
    const diffDiv = document.querySelectorAll('.d2h-file-side-diff')
    if (diffDiv.length > 0) {
      const left = diffDiv[0]
      const right = diffDiv[1]
      left.addEventListener('scroll', (e) => {
        if (left.scrollLeft !== right.scrollLeft) {
          right.scrollLeft = left.scrollLeft
        }
      })
      right.addEventListener('scroll', (e) => {
        if (left.scrollLeft !== right.scrollLeft) {
          left.scrollLeft = right.scrollLeft
        }
      })
    }
  })
}
</script>

<template>
  <Form class="mb-4">
    <div class="flex gap-4 items-end">
      <FormItem label="关键字">
        <InputText v-model="queryForm.value.keywords" placeholder="变更内容" class="w-300px" />
      </FormItem>

      <FormItem>
        <ButtonGroup>
          <Button type="primary" @click="onSearch">
            <template #icon>
              <Icon icon-type="SearchOutlined" />
            </template>
            查询
          </Button>
          <Button @click="onReload">
            <template #icon>
              <Icon icon-type="ReloadOutlined" />
            </template>
            重置
          </Button>
        </ButtonGroup>
      </FormItem>
    </div>
  </Form>

  <Card>
    <!---以表格形式 显示 -->
    <!-- <DataTracerTable :tableData="tableData" @showDetail="showDetail" /> -->

    <!---以 timeline 时间轴形式 显示 -->
    <DataTracerTimeline :table-data="tableData" @show-detail="showDetail" />

    <Paginator
      :first="(queryForm.value.pageNum - 1) * queryForm.value.pageSize"
      :rows="queryForm.value.pageSize"
      :total-records="total"
      :rows-per-page-options="PAGE_SIZE_OPTIONS"
      @page="(event) => { queryForm.value.pageNum = event.page + 1; queryForm.value.pageSize = event.rows; ajaxQuery(); }"
    />
    <Dialog v-model:visible="visibleDiff" modal :style="{ width: '90%' }" header="数据比对">
      <div v-html="prettyHtml" />
    </Dialog>
  </Card>
</template>
