<!--
  *  表格列设置
-->

<script setup lang="ts">
import { tableColumnApi } from '@/api/support/table-column-api'
import { sentry } from '@/lib/sentry'
import { useAppConfigStore } from '@/store/modules/system/app-config.js'
import { useToast } from 'primevue/usetoast'
import { nextTick, ref, watch } from 'vue'
import TableColumnManagerModal from './TableColumnManagerModal.vue'
import { mergeColumn } from './utils'

const props = defineProps({
  // 表格列数组
  modelValue: {
    type: Array,
    default: () => [],
  },
  // 刷新表格函数
  refresh: {
    type: Function,
    required: true,
  },
  // 表格id
  tableId: {
    type: Number,
    require: true,
  },
})

const emit = defineEmits(['update:modelValue'])

// 初始化 toast
const toast = useToast()

// 原始表格列数据（复制一份最原始的columns集合，以供后续各个地方使用）
let originalColumn = _cloneDeep(props.modelValue)

// 防止无限递归的标志
const isUpdating = ref(false)

// 构建用户的数据列
async function buildUserTableColumns() {
  if (!props.tableId || isUpdating.value) {
    return
  }

  let userTableColumnArray = []
  try {
    const res = await tableColumnApi.getColumns(props.tableId)
    if (res.data) {
      try {
        userTableColumnArray = JSON.parse(res.data)
      }
      catch (e1) {
        sentry.captureError(e1)
      }
    }
  }
  catch (e) {
    sentry.captureError(e)
  }
  updateColumn(userTableColumnArray)
}

// ----------------- 全屏 -------------------
const fullScreenFlag = ref(false)

function onFullScreen() {
  if (fullScreenFlag.value) {
    // 退出全屏
    handleExitFullScreen()
    exitElementFullscreen(document.body)
  }
  else {
    fullScreenFlag.value = true
    useAppConfigStore().startFullScreen()
    launchElementFullScreen(document.body)
  }
}

// 处理退出全屏
function handleExitFullScreen() {
  fullScreenFlag.value = false
  useAppConfigStore().exitFullScreen()
  document.removeEventListener('fullscreenchange', handleFullscreenChange)
  document.removeEventListener('mozfullscreenchange', handleFullscreenChange) // Firefox
  document.removeEventListener('webkitfullscreenchange', handleFullscreenChange) // Chrome, Safari and Opera
  document.removeEventListener('MSFullscreenChange', handleFullscreenChange) // Internet Explorer and Edge
}

// 判断各种浏览器 -全屏
function launchElementFullScreen(element) {
  if (element.requestFullscreen) {
    element.requestFullscreen()
  }
  else if (element.mozRequestFullScreen) {
    element.mozRequestFullScreen()
  }
  else if (element.webkitRequestFullScreen) {
    element.webkitRequestFullScreen()
  }
  else if (element.msRequestFullscreen) {
    element.msRequestFullscreen()
  }
  else {
    toast.add({ severity: 'error', summary: '当前浏览器不支持部分全屏！', life: 3000 })
  }
  document.addEventListener('fullscreenchange', handleFullscreenChange)
  document.addEventListener('mozfullscreenchange', handleFullscreenChange) // Firefox
  document.addEventListener('webkitfullscreenchange', handleFullscreenChange) // Chrome, Safari and Opera
  document.addEventListener('MSFullscreenChange', handleFullscreenChange) // Internet Explorer and Edge
}

function handleFullscreenChange() {
  if (!(document.fullscreenElement || document.mozFullScreenElement || document.webkitFullscreenElement || document.msFullscreenElement)) {
    handleExitFullScreen()
  }
}

// 判断各种浏览器 -退出全屏
function exitElementFullscreen(element) {
  if (document.exitFullscreen) {
    document.exitFullscreen()
  }
  else if (document.mozCancelFullScreen) {
    document.mozCancelFullScreen()
  }
  else if (document.webkitExitFullscreen) {
    document.webkitExitFullscreen()
  }
  else if (document.msExitFullscreen) {
    document.msExitFullscreen()
  }
}

// ----------------- 弹窗 修改表格列 -------------------

const tableColumnManagerModal = ref()
function showModal() {
  tableColumnManagerModal.value.show(originalColumn, props.tableId)
}

// 将弹窗修改的列数据，赋值给原表格 列数组
function updateColumn(changeColumnArray) {
  if (isUpdating.value) {
    return
  }

  isUpdating.value = true
  try {
    // 合并列
    const newColumns = mergeColumn(_cloneDeep(originalColumn), changeColumnArray)
    emit(
      'update:modelValue',
      newColumns.filter(e => e.showFlag),
    )
  }
  finally {
    // 使用 nextTick 确保在下一个事件循环中重置标志
    nextTick(() => {
      isUpdating.value = false
    })
  }
}

// ========= 定义 watch 监听 ===============
watch(
  () => props.tableId,
  (newTableId, oldTableId) => {
    if (newTableId && newTableId !== oldTableId) {
      originalColumn = _cloneDeep(props.modelValue)
      buildUserTableColumns()
    }
  },
  { immediate: true },
)

// 监听 modelValue 变化，更新 originalColumn
watch(
  () => props.modelValue,
  (newValue) => {
    if (newValue && !isUpdating.value) {
      originalColumn = _cloneDeep(newValue)
    }
  },
  { deep: true },
)
</script>

<template>
  <span>
    <Tooltip v-if="!fullScreenFlag" text="全屏">
      <Button variant="text" size="small" @click="onFullScreen">
        <template #icon>
          <Icon icon-type="FullscreenOutlined" />
        </template>
      </Button>
    </Tooltip>
    <Tooltip v-if="fullScreenFlag" text="取消全屏">
      <Button variant="text" size="small" @click="onFullScreen">
        <template #icon>
          <Icon icon-type="FullscreenExitOutlined" />
        </template>
      </Button>
    </Tooltip>
    <Tooltip text="刷新">
      <Button variant="text" size="small" @click="props.refresh">
        <template #icon>
          <Icon icon-type="RedoOutlined" />
        </template>
      </Button>
    </Tooltip>
    <Tooltip text="列设置">
      <Button variant="text" size="small" @click="showModal">
        <template #icon>
          <Icon icon-type="SettingOutlined" />
        </template>
      </Button>
    </Tooltip>
    <TableColumnManagerModal ref="tableColumnManagerModal" @change="updateColumn" />
  </span>
</template>
