<!--
  *  表格列设置
-->
<script setup lang="ts">
import { tableColumnApi } from '@/api/support/table-column-api'
import { AxLoading } from '@/components/base/ax-loading'
import { sentry } from '@/lib/sentry'
import { useConfirm } from 'primevue/useconfirm'
import { useToast } from 'primevue/usetoast'
import Sortable from 'sortablejs'
import { nextTick, ref } from 'vue'
import { mergeColumn } from './utils'

interface TableColumnData {
  columnKey: string
  title: string
  width?: number
  showFlag: boolean
  sort: number
  fixed?: boolean
}

interface ColumnData {
  columnKey: string
  sort: number
  width?: number
  showFlag: boolean
}

const emit = defineEmits(['change'])
const toast = useToast()
const { require: requireConfirmation } = useConfirm()

defineExpose({ show })

// --------------------- 表格渲染 --------------------------------
const tableData = ref<TableColumnData[]>([])

// ----------- table 批量操作 start -----------
const selectedRowKeyList = ref<string[]>([])
const selectedItems = ref<TableColumnData[]>([])

// -------------------------提交表单【恢复默认、保存、取消】 ------------------------
const submitLoading = ref(false)

// ---------------- 显示 / 隐藏 --------------------
let tableId = 1
const visible = ref(false)
// 显示
function show(columns: TableColumnData[], showTableId: number) {
  tableId = showTableId
  visible.value = true
  getUserTableColumns(tableId, cloneDeep(columns))
}

// 隐藏
function hide() {
  visible.value = false
}

// 获取用户的列数据
async function getUserTableColumns(tableId: number, columns: TableColumnData[]) {
  AxLoading.show()
  let userTableColumnArray = []
  try {
    const res = await tableColumnApi.getColumns(tableId)
    if (res.data) {
      try {
        userTableColumnArray = JSON.parse(res.data)
      }
      catch (e1) {
        sentry.captureError(e1)
      }
    }
  }
  catch (e) {
    sentry.captureError(e)
  }
  finally {
    AxLoading.hide()
  }

  // 根据前端列和后端列构建新的列数据
  tableData.value = mergeColumn(columns, userTableColumnArray)

  // 将已经显示的展示出来
  selectedRowKeyList.value = []
  selectedItems.value = []
  for (const item of tableData.value) {
    if (item.showFlag) {
      selectedRowKeyList.value.push(item.columnKey)
      selectedItems.value.push(item)
    }
  }

  await nextTick()
  initDrag()
}

// --------------------- 表格移动【拖拽移动、上移、下移】 --------------------------------
// 初始化拖拽
function initDrag() {
  const tbody = document.querySelector('#smartTableColumnModalTable tbody')
  Sortable.create(tbody, {
    animation: 300,
    dragClass: 'smart-ghost-class',
    ghostClass: 'smart-ghost-class',
    chosenClass: 'smart-ghost-class',
    handle: '.handle',
    onEnd({ newIndex, oldIndex }) {
      if (newIndex === oldIndex) {
        return
      }
      moveTableData(oldIndex, newIndex)
    },
  })
}

// 上移
function up(oldIndex) {
  const newIndex = oldIndex - 1
  if (newIndex < 0) {
    return
  }
  if (tableData.value[newIndex].fixed) {
    return
  }
  moveTableData(oldIndex, newIndex)
}

// 下移
function down(oldIndex) {
  const newIndex = oldIndex + 1
  if (newIndex >= tableData.value.length) {
    return
  }
  if (tableData.value[newIndex].fixed) {
    return
  }
  moveTableData(oldIndex, newIndex)
}

// 移动表格数据
function moveTableData(oldIndex, newIndex) {
  const currRow = tableData.value.splice(oldIndex, 1)[0]
  tableData.value.splice(newIndex, 0, currRow)
}

// 重置
function reset() {
  requireConfirmation({
    message: '确定恢复默认后，该信息将不可恢复',
    header: '确定要恢复默认吗？',
    accept: async () => {
      submitLoading.value = true
      try {
        await tableColumnApi.deleteColumns(tableId)
        toast.add({ severity: 'success', summary: '恢复默认成功', life: 3000 })
        emit('change', [])
        hide()
      }
      catch (e) {
        sentry.captureError(e)
      }
      finally {
        submitLoading.value = false
      }
    },
  })
}

// 保存
async function save() {
  submitLoading.value = true
  try {
    let columnList: ColumnData[] = []
    for (let index = 0; index < tableData.value.length; index++) {
      const item = tableData.value[index]
      const column: ColumnData = {
        columnKey: item.columnKey,
        sort: index + 1,
        showFlag: selectedItems.value.some(selected => selected.columnKey === item.columnKey),
      }
      if (item.width) {
        column.width = item.width
      }
      columnList.push(column)
    }

    columnList = sortBy(columnList, e => e.sort)

    await tableColumnApi.updateTableColumn({
      tableId,
      columnList,
    })

    toast.add({ severity: 'success', summary: '保存成功', life: 3000 })
    emit('change', columnList)
    hide()
  }
  catch (e) {
    sentry.captureError(e)
  }
  finally {
    submitLoading.value = false
  }
}
</script>

<template>
  <Dialog v-model:visible="visible" header="设置列" :style="{ width: '700px' }" :closable="false">
    <Message severity="info" class="mb-4">
      <template #icon>
        <Icon icon-type="SmileOutlined" />
      </template>
      可以通过拖拽行直接修改顺序哦；（ <Icon icon-type="PushpinOutlined" />为固定列，不可拖拽 ）
    </Message>

    <DataTable
      id="smartTableColumnModalTable"
      v-model:selection="selectedItems"
      :value="tableData"
      data-key="columnKey"
      selection-mode="multiple"
      :paginator="false"
      size="small"
      :scrollable="true"
      scroll-height="400px"
    >
      <Column selection-mode="multiple" header-style="width: 3rem" />
      <Column field="title" header="列" style="min-width: 200px">
        <template #body="{ data }">
          <Button
            text
            :class="data.fixed ? '' : 'handle'"
            size="small"
            class="w-full text-left"
          >
            <template #icon>
              <Icon :icon-type="data.fixed ? 'PushpinOutlined' : 'DragOutlined'" />
            </template>
            {{ data.title }}
          </Button>
        </template>
      </Column>
      <Column field="width" header="宽度(像素)" style="width: 150px">
        <template #body="{ data }">
          <div class="flex items-center gap-1">
            <InputNumber v-model="data.width" style="width: 90px" size="small" />
            <span>px</span>
          </div>
        </template>
      </Column>
      <Column header="操作" style="width: 150px">
        <template #body="{ data, index }">
          <div v-if="!data.fixed" class="flex gap-2">
            <Button
              v-show="index > 0"
              text
              size="small"
              class="handle"
              @click="up(index)"
            >
              上移
            </Button>
            <Button
              v-show="index !== tableData.length - 1"
              text
              size="small"
              class="handle"
              @click="down(index)"
            >
              下移
            </Button>
          </div>
        </template>
      </Column>
    </DataTable>

    <template #footer>
      <div class="flex justify-between">
        <Button severity="danger" :loading="submitLoading" @click="reset">
          恢复默认
        </Button>
        <div class="flex gap-2">
          <Button severity="secondary" @click="hide">
            取消
          </Button>
          <Button :loading="submitLoading" @click="save">
            保存
          </Button>
        </div>
      </div>
    </template>
  </Dialog>
</template>
