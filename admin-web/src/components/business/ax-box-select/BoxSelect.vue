<script setup lang="ts">
import type { BoxResult } from '@/api/business/box/model/box-form-model'
import { boxApi } from '@/api/business/box/box-api'
import { injectBoxSelectService } from '@/components/business/ax-box-select/service'
import { useConfirm } from 'primevue/useconfirm'
import { useToast } from 'primevue/usetoast'
import { onMounted, ref } from 'vue'
// 移除 PrimeVue 原生组件导入，使用项目封装的组件
import BoxFormModal from './box-form-modal.vue'
import { BoxFormService } from './box-form-service'

// 创建服务实例
const boxService = injectBoxSelectService()
// 使用静态方法创建并提供 BoxFormService 实例
const boxFormService = BoxFormService.createAndProvide()

// 初始化 PrimeVue hooks
const toast = useToast()
const { require: requireConfirmation } = useConfirm()

// 内部配置 - 不再通过props暴露
const config = {
  boxTypeCode: undefined,
  isActive: true, // 默认只显示活跃状态的纸箱
  disabled: false,
  placeholder: '请选择纸箱规格',
  allowClear: false,
  showSearch: true,
  defaultFirst: true, // 默认选择第一个
  showEditButton: true, // 默认显示编辑按钮
  showAddButton: true, // 默认显示新增按钮
  showDeleteButton: true, // 默认显示删除按钮
}

// 内部状态
const loading = ref(false)
const isAddCustom = ref(false)
const boxOptions = ref<BoxResult[]>([])

// 获取纸箱列表
async function fetchBoxList() {
  try {
    loading.value = true
    const param = {
      boxTypeCode: config.boxTypeCode,
      isActive: config.isActive,
    }

    const res = await boxApi.boxList(param)
    if (res.success && res.data) {
      boxOptions.value = res.data

      // 如果启用默认选择第一个选项，且当前未选择任何值
      if (config.defaultFirst && !boxService.selectedBox && boxOptions.value.length > 0) {
        const firstBox = boxOptions.value[0]

        // --- 修改点：直接使用列表中的对象，不再获取详情 ---
        boxService.selectedBox = firstBox
        // --- 移除获取详情的逻辑 ---
      }
    }
  }
  catch (error) {
    toast.add({ severity: 'error', summary: '获取纸箱列表失败', life: 3000 })
    console.error('获取纸箱列表失败', error)
  }
  finally {
    loading.value = false
  }
}

// 选项显示文本
function getOptionLabel(item: BoxResult) {
  return `${item.boxTypeName || '未命名'} (${item.boxTypeCode || '无编码'})`
}

// 处理选择变化的函数 (当绑定 ID 时) - 简化版 (假设 ID 必为 number)
function handleSelectChange(value: unknown) {
  let numericId: number | undefined

  if (value === undefined || value === null) {
    numericId = undefined // 清空
  }
  else if (typeof value === 'number') {
    numericId = value // 直接是数字
  }
  else {
    // 其他所有未预期类型，都视为无效
    console.error('Unexpected value type from @update:value (expected number, null or undefined):', value)
    numericId = undefined // 无效类型，视为清空
  }

  // 根据解析出的 numericId 更新状态
  if (numericId === undefined) {
    boxService.selectedBox = undefined
  }
  else {
    const selectedItem = boxOptions.value.find(item => item.id === numericId)
    boxService.selectedBox = selectedItem ? { ...selectedItem } : undefined
  }

  // 触发 onChange 钩子
  boxService.onChange()
}

// 处理新增按钮点击 - 调用服务方法
function handleAdd() {
  isAddCustom.value = true
  boxFormService.openAddForm()
}

// 处理删除按钮点击
function handleDelete(e: Event, boxId: number) {
  e.stopPropagation() // 阻止事件冒泡
  requireConfirmation({
    message: '确定要删除该纸箱规格吗？删除后不可恢复。',
    header: '确认删除',
    accept: async () => {
      try {
        const res = await boxApi.deleteBox(boxId)
        if (res.success) {
          toast.add({ severity: 'success', summary: '删除成功', life: 3000 })
          // 如果删除的是当前选中的纸箱，清空选择
          if (boxService.selectedBox?.id === boxId) {
            boxService.selectedBox = undefined
          }
          // 重新加载列表
          fetchBoxList()
        }
        else {
          toast.add({ severity: 'error', summary: res.msg || '删除失败', life: 3000 })
        }
      }
      catch (error) {
        console.error('删除纸箱失败', error)
        toast.add({ severity: 'error', summary: '删除失败', life: 3000 })
      }
    },
  })
}

// 在组件挂载后初始化数据
onMounted(() => {
  fetchBoxList()
})
</script>

<template>
  <div class="relative inline-block w-full">
    <Select
      :model-value="isAddCustom ? 'empty-option' : boxService.selectedBox?.id"
      :options="boxOptions"
      :loading="loading"
      :disabled="config.disabled"
      :placeholder="config.placeholder"
      :show-clear="config.allowClear"
      :filter="config.showSearch"
      :option-label="getOptionLabel"
      option-value="id"
      fluid
      @update:model-value="handleSelectChange"
    >
      <!-- 自定义新增选项 -->
      <template v-if="config.showAddButton" #header>
        <div class="p-3 border-b border-surface cursor-pointer hover:bg-surface-100" @click="handleAdd">
          <Icon icon-type="PlusOutlined" class="mr-2" /> 自定义 (custom)
        </div>
      </template>

      <!-- 动态选项模板 -->
      <template #option="{ option }">
        <div class="flex justify-between items-center w-full">
          <span>{{ getOptionLabel(option) }}</span>
          <div class="flex gap-1">
            <Button
              v-if="config.showEditButton"
              text
              size="small"
              class="!p-1"
              @click.stop="boxFormService.openEditForm(option)"
            >
              <Icon icon-type="EditOutlined" class="text-sm" />
            </Button>
            <Button
              v-if="config.showDeleteButton"
              text
              size="small"
              severity="danger"
              class="!p-1"
              @click.stop="handleDelete($event, option.id as number)"
            >
              <Icon icon-type="DeleteOutlined" class="text-sm" />
            </Button>
          </div>
        </div>
      </template>

      <template #empty>
        <span v-if="loading">加载中...</span>
        <span v-else>未找到符合条件的纸箱规格</span>
      </template>
    </Select>

    <!-- 编辑/新增弹窗 -->
    <BoxFormModal
      :refresh-callback="fetchBoxList"
      @update:add="isAddCustom = $event"
    />
  </div>
</template>
