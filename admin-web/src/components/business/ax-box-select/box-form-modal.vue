<script setup lang="ts">
import { ref } from 'vue'
import { injectBoxFormService } from './box-form-service'

const props = defineProps<{
  refreshCallback?: () => void | Promise<void>
  isAddCustom?: boolean
}>()

const emit = defineEmits<{
  'update:add': [value: boolean]
}>()

const boxFormService = injectBoxFormService()
const formRef = ref()

async function handleOk() {
  if (!boxFormService)
    return
  try {
    await formRef.value?.validate()
    await boxFormService.submitForm(props.refreshCallback)
  }
  catch (validationError) {
    console.error('表单验证失败或提交错误:', validationError)
  }
}

function handleCancel() {
  emit('update:add', false)
  boxFormService.closeForm()
}
</script>

<template>
  <Dialog
    v-if="boxFormService"
    v-model:visible="boxFormService.formOpen"
    :header="boxFormService.formTitle"
    :closable="!boxFormService.submitLoading"
    class="w-200"
    @hide="handleCancel"
  >
    <div v-if="boxFormService.formLoading" class="flex justify-center items-center py-8">
      <i class="pi pi-spin pi-spinner text-2xl" />
    </div>
    <Form
      v-else
      ref="formRef"
      :model="boxFormService.formData"
      class="pt-5"
    >
      <div class="grid grid-cols-12 gap-4">
        <div class="col-span-6">
          <FormItem
            label="箱型编码"
            name="boxTypeCode"
            :rules="[{ required: true, message: '请输入箱型编码' }]"
          >
            <InputText v-model="boxFormService.formData.boxTypeCode" placeholder="请输入箱型编码" />
          </FormItem>
        </div>
        <div class="col-span-6">
          <FormItem
            label="箱型名称"
            name="boxTypeName"
            :rules="[{ required: true, message: '请输入箱型名称' }]"
          >
            <InputText v-model="boxFormService.formData.boxTypeName" placeholder="请输入箱型名称" />
          </FormItem>
        </div>
      </div>

      <div class="grid grid-cols-12 gap-4">
        <div class="col-span-6">
          <FormItem label="排序" name="sort">
            <InputNumber v-model="boxFormService.formData.sort" placeholder="请输入排序" class="w-full" />
          </FormItem>
        </div>
        <div class="col-span-6">
          <FormItem label="状态" name="isActive">
            <ToggleSwitch v-model="boxFormService.formData.isActive" on-label="启用" off-label="禁用" />
          </FormItem>
        </div>
      </div>

      <FormItem label="描述" name="boxTypeDesc">
        <CustomTextArea v-model="boxFormService.formData.boxTypeDesc" placeholder="请输入描述" :rows="2" />
      </FormItem>

      <Divider>计算配置</Divider>

      <div class="grid grid-cols-12 gap-4">
        <div class="col-span-6">
          <FormItem label="需要模切" name="needDieCutting">
            <ToggleSwitch v-model="boxFormService.formData.boxConfig.needDieCutting" on-label="是" off-label="否" />
          </FormItem>
        </div>
        <div class="col-span-6">
          <FormItem label="需要印刷版" name="needPrintingPlate">
            <ToggleSwitch v-model="boxFormService.formData.boxConfig.needPrintingPlate" on-label="是" off-label="否" />
          </FormItem>
        </div>
      </div>

      <FormItem label="门幅公式" name="doorWidthFormula">
        <InputText v-model="boxFormService.formData.boxConfig.doorWidthFormula" placeholder="请输入门幅计算公式，例如 (长+高)*2+接口" />
      </FormItem>
      <FormItem label="总长公式" name="totalLengthFormula">
        <InputText v-model="boxFormService.formData.boxConfig.totalLengthFormula" placeholder="请输入总长计算公式，例如 (宽+高)*2+舌头长度" />
      </FormItem>
    </Form>
    <template #footer>
      <div class="flex justify-end gap-2">
        <Button label="取消" severity="secondary" :disabled="boxFormService.submitLoading" @click="handleCancel" />
        <Button label="确定" :loading="boxFormService.submitLoading" @click="handleOk" />
      </div>
    </template>
  </Dialog>
</template>
