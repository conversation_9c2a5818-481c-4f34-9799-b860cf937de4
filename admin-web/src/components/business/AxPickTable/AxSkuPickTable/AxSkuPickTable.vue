<script setup lang="ts">
import { AX_SKU_PICK_TABLE_KEY, AxSkuPickTableService } from './service/AxSkuPickTableService'

const service = inject<AxSkuPickTableService>(AX_SKU_PICK_TABLE_KEY, new AxSkuPickTableService())
</script>

<template>
  <PickTable
    :raw-table-data="service.sourceData"
    :raw-table-column="service.sourceColumns"
    :selected-table-data="service.destinationDataList"
    :selected-table-column="service.destinationColumns"

    :select-item="(item) => service.selectItem(item)"
    :remove-item-by-source="(item) => service.removeItemBySource(item)"
    :remove-item-by-destination="(item) => service.removeItemByDestination(item)"
    :clear-selected="() => service.clearSelected()"
    :is-item-selected="(item) => service.isItemSelected(item)"
  />
</template>
