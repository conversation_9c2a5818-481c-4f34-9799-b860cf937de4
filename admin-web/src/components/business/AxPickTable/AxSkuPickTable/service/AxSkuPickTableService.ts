import type { SalesOrderItem } from '@/api/business/sales-order-item/model/sales-order-item-types'
import type { SkuResult } from '@/api/business/sku/model/sku-types'
import { PickTableService } from '../../_base/service/PickTableService'
import { createSelectedSkuColumns, skuColumns } from '../config/columns'
import { skuResultToSalesOrderItem } from '../config/util'

import { skuTableService } from './SkuTableService'

export const AX_SKU_PICK_TABLE_KEY = Symbol('axSkuPickTableService')

export class AxSkuPickTableService extends PickTableService<SkuResult, SalesOrderItem> {
  destinationRowKey = 'skuId' as keyof SalesOrderItem

  constructor() {
    super(
      {
        sourceData: skuTableService.tableData,
        sourceColumns: skuColumns,
        destinationColumnsFunc: createSelectedSkuColumns,
      },
    )
  }

  provide() {
    provide(AX_SKU_PICK_TABLE_KEY, this)
  }

  updateProductQuantity(index: number, num: number) {
    console.log(index, num)
  }

  protected convertData(_data: SkuResult): SalesOrderItem {
    return skuResultToSalesOrderItem(_data)
  }
}
