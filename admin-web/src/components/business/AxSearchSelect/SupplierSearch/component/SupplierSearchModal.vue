<script setup lang="ts">
import type { SupplierSearchService } from '../service/SupplierSearchService'
import ToggleSwitch from '@/components/base/ToggleSwitch'
import { inject, ref } from 'vue'
import { SUPPLIER_SEARCH_KEY } from '../service/SupplierSearchService'
import { rules } from './rule'

// 使用辅助函数注入表单服务
const supplierFormService = inject(SUPPLIER_SEARCH_KEY) as SupplierSearchService

// 表单引用
const formRef = ref()

// 处理 Dialog.vue 确认事件
async function handleSubmit() {
  if (formRef.value)
    await formRef.value.validate()
  // 验证通过后提交（使用带自动刷新的提交方法）
  await supplierFormService.submitFormAndRefresh()
  supplierFormService.resetListQuery()
}
</script>

<template>
  <Dialog
    :header="supplierFormService.formTitle"
    v-model:visible="supplierFormService.formOpen"
    :mask-closable="false"
    :destroy-on-close="true"
    width="1200px"
  >
    <Form
      ref="formRef"
      layout="vertical"
      :model="supplierFormService.formData"
      :rules="rules"
    >
      <!-- 基本信息 -->
      <Grid :cols="3" :gap="4">
        <GridCol>
          <FormItem label="供应商编码" name="supplierCode">
            <InputText v-model="supplierFormService.formData.supplierCode" placeholder="请输入供应商编码" />
          </FormItem>
        </GridCol>
        <GridCol>
          <FormItem label="供应商名称" name="supplierName">
            <InputText v-model="supplierFormService.formData.supplierName" placeholder="请输入供应商名称" />
          </FormItem>
        </GridCol>
        <GridCol>
          <FormItem label="供应商类型" name="supplierType">
            <DictSelect v-model="supplierFormService.formData.supplierType" key-code="supplier_type" placeholder="请选择供应商类型" />
          </FormItem>
        </GridCol>
      </Grid>

      <Grid :cols="3" :gap="4">
        <GridCol>
          <FormItem label="营业执照号" name="businessLicense">
            <InputText v-model="supplierFormService.formData.businessLicense" placeholder="请输入营业执照号" />
          </FormItem>
        </GridCol>
        <GridCol>
          <FormItem label="税务登记号" name="taxId">
            <InputText v-model="supplierFormService.formData.taxId" placeholder="请输入税务登记号" />
          </FormItem>
        </GridCol>
        <GridCol>
          <FormItem label="状态" name="status">
            <DictSelect v-model="supplierFormService.formData.status" key-code="supplier_status" placeholder="请选择状态" />
          </FormItem>
        </GridCol>
      </Grid>

      <!-- 联系信息 -->
      <Divider label="联系信息" />

      <Grid :cols="3" :gap="4">
        <GridCol>
          <FormItem label="主要联系人" name="contactPerson">
            <InputText v-model="supplierFormService.formData.contactPerson" placeholder="请输入主要联系人" />
          </FormItem>
        </GridCol>
        <GridCol>
          <FormItem label="联系电话" name="contactPhone">
            <InputText v-model="supplierFormService.formData.contactPhone" placeholder="请输入联系电话" />
          </FormItem>
        </GridCol>
        <GridCol>
          <FormItem label="联系邮箱" name="contactEmail">
            <InputText v-model="supplierFormService.formData.contactEmail" placeholder="请输入联系邮箱" />
          </FormItem>
        </GridCol>
      </Grid>

      <Grid :cols="3" :gap="4">
        <GridCol>
          <FormItem label="联系人职位" name="contactPosition">
            <InputText v-model="supplierFormService.formData.contactPosition" placeholder="请输入联系人职位" />
          </FormItem>
        </GridCol>
        <GridCol>
          <FormItem label="备用联系人" name="alternativeContact">
            <InputText v-model="supplierFormService.formData.alternativeContact" placeholder="请输入备用联系人" />
          </FormItem>
        </GridCol>
        <GridCol>
          <FormItem label="备用联系电话" name="alternativePhone">
            <InputText v-model="supplierFormService.formData.alternativePhone" placeholder="请输入备用联系电话" />
          </FormItem>
        </GridCol>
      </Grid>

      <!-- 地址信息 -->
      <Divider label="地址信息" />

      <Grid :cols="3" :gap="4">
        <GridCol>
          <FormItem label="省份" name="province">
            <InputText v-model="supplierFormService.formData.province" placeholder="请输入省份" />
          </FormItem>
        </GridCol>
        <GridCol>
          <FormItem label="城市" name="city">
            <InputText v-model="supplierFormService.formData.city" placeholder="请输入城市" />
          </FormItem>
        </GridCol>
        <GridCol>
          <FormItem label="区县" name="district">
            <InputText v-model="supplierFormService.formData.district" placeholder="请输入区县" />
          </FormItem>
        </GridCol>
      </Grid>

      <Grid :cols="3" :gap="4">
        <GridCol :span="2">
          <FormItem label="详细地址" name="address">
            <InputText v-model="supplierFormService.formData.address" placeholder="请输入详细地址" />
          </FormItem>
        </GridCol>
        <GridCol>
          <FormItem label="邮政编码" name="postalCode">
            <InputText v-model="supplierFormService.formData.postalCode" placeholder="请输入邮政编码" />
          </FormItem>
        </GridCol>
      </Grid>

      <!-- 银行信息 -->
      <Divider label="银行信息" />

      <Grid :cols="3" :gap="4">
        <GridCol>
          <FormItem label="开户银行" name="bankName">
            <InputText v-model="supplierFormService.formData.bankName" placeholder="请输入开户银行" />
          </FormItem>
        </GridCol>
        <GridCol>
          <FormItem label="银行账号" name="bankAccount">
            <InputText v-model="supplierFormService.formData.bankAccount" placeholder="请输入银行账号" />
          </FormItem>
        </GridCol>
        <GridCol>
          <FormItem label="开户名称" name="bankAccountName">
            <InputText v-model="supplierFormService.formData.bankAccountName" placeholder="请输入开户名称" />
          </FormItem>
        </GridCol>
      </Grid>

      <!-- 商务信息 -->
      <Divider label="商务信息" />

      <Grid :cols="4" :gap="4">
        <GridCol :span="2">
          <FormItem label="付款条件" name="paymentTerms">
            <InputText v-model="supplierFormService.formData.paymentTerms" placeholder="请输入付款条件" />
          </FormItem>
        </GridCol>
        <GridCol>
          <FormItem label="信用额度" name="creditLimit">
            <InputNumber v-model="supplierFormService.formData.creditLimit" placeholder="请输入信用额度" :min="0" class="w-full" />
          </FormItem>
        </GridCol>
        <GridCol>
          <FormItem label="信用期(天)" name="creditPeriod">
            <InputNumber v-model="supplierFormService.formData.creditPeriod" placeholder="请输入信用期" :min="0" class="w-full" />
          </FormItem>
        </GridCol>
      </Grid>

      <Grid :cols="4" :gap="4">
        <GridCol>
          <FormItem label="折扣率(%)" name="discountRate">
            <InputNumber v-model="supplierFormService.formData.discountRate" placeholder="请输入折扣率" :min="0" :max="100" class="w-full" />
          </FormItem>
        </GridCol>
        <GridCol>
          <FormItem label="单瓦加工费" name="processingFeeSingle">
            <InputNumber v-model="supplierFormService.formData.processingFeeSingle" placeholder="元/m²" :min="0" class="w-full" />
          </FormItem>
        </GridCol>
        <GridCol>
          <FormItem label="双瓦加工费" name="processingFeeDouble">
            <InputNumber v-model="supplierFormService.formData.processingFeeDouble" placeholder="元/m²" :min="0" class="w-full" />
          </FormItem>
        </GridCol>
        <GridCol>
          <FormItem label="税率(%)" name="taxRate">
            <InputNumber v-model="supplierFormService.formData.taxRate" placeholder="请输入税率" :min="0" :max="100" class="w-full" />
          </FormItem>
        </GridCol>
      </Grid>

      <Grid :cols="3" :gap="4">
        <GridCol>
          <FormItem label="最小订单量" name="minOrderQuantity">
            <InputNumber v-model="supplierFormService.formData.minOrderQuantity" placeholder="请输入最小订单量" :min="0" class="w-full" />
          </FormItem>
        </GridCol>
        <GridCol>
          <FormItem label="交货提前期(天)" name="leadTime">
            <InputNumber v-model="supplierFormService.formData.leadTime" placeholder="请输入交货提前期" :min="0" class="w-full" />
          </FormItem>
        </GridCol>
        <GridCol>
          <FormItem label="战略供应商" name="isStrategic">
            <ToggleSwitch v-model="supplierFormService.formData.isStrategic" on-label="是" off-label="否" />
          </FormItem>
        </GridCol>
      </Grid>

      <!-- 合作信息 -->
      <Divider label="合作信息" />

      <Grid :cols="3" :gap="4">
        <GridCol>
          <FormItem label="合作开始日期" name="cooperationStartDate">
            <DatePicker v-model="supplierFormService.formData.cooperationStartDate" class="w-full" placeholder="请选择合作开始日期" />
          </FormItem>
        </GridCol>
        <GridCol>
          <FormItem label="合同到期日期" name="contractExpiryDate">
            <DatePicker v-model="supplierFormService.formData.contractExpiryDate" class="w-full" placeholder="请选择合同到期日期" />
          </FormItem>
        </GridCol>
        <GridCol>
          <FormItem label="累计订单金额" name="totalOrderAmount">
            <InputNumber v-model="supplierFormService.formData.totalOrderAmount" :disabled="true" class="w-full" />
          </FormItem>
        </GridCol>
      </Grid>

      <!-- 审批信息 -->
      <Divider label="审批信息" />

      <Grid :cols="3" :gap="4">
        <GridCol>
          <FormItem label="审批状态" name="approvalStatus">
            <DictSelect v-model="supplierFormService.formData.approvalStatus" key-code="approval_status" placeholder="请选择审批状态" />
          </FormItem>
        </GridCol>
        <GridCol>
          <FormItem label="审批人ID" name="approvalUserId">
            <InputNumber v-model="supplierFormService.formData.approvalUserId" placeholder="请输入审批人ID" :min="1" class="w-full" />
          </FormItem>
        </GridCol>
        <GridCol>
          <FormItem label="审批日期" name="approvalDate">
            <DatePicker v-model="supplierFormService.formData.approvalDate" class="w-full" placeholder="请选择审批日期" />
          </FormItem>
        </GridCol>
      </Grid>

      <!-- 备注信息 -->
      <FormItem label="审批备注" name="approvalNotes">
        <CustomTextArea v-model="supplierFormService.formData.approvalNotes" placeholder="请输入审批备注" :rows="2" />
      </FormItem>

      <FormItem label="备注" name="remark">
        <CustomTextArea v-model="supplierFormService.formData.remark" placeholder="请输入备注" :rows="3" />
      </FormItem>
    </Form>
    <template #footer>
      <Button label="取消" severity="danger" @click="supplierFormService.closeForm()" />
      <Button label="保存" @click="handleSubmit" />
    </template>
  </Dialog>
</template>
