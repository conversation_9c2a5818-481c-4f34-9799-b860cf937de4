<script setup lang="ts">
// ant-design-vue
import type { FormInstance } from 'ant-design-vue/es/form'
// 业务组件
import { CustomTextArea as TextArea } from '@/components/base/CustomText'
import { inject, ref } from 'vue'
import { CUSTOMER_SEARCH_KEY, type CustomerSearchService } from '../service/CustomerSearchService'
// 表单验证规则
import { rules } from './rule'

// 使用辅助函数注入表单服务
const customerFormService = inject(CUSTOMER_SEARCH_KEY) as CustomerSearchService

// 表单引用
const formRef = ref<FormInstance>()

// 处理 Dialog.vue 确认事件
async function handleSubmit() {
  if (formRef.value)
    await formRef.value.validate()
  // 验证通过后提交（使用带自动刷新的提交方法）
  await customerFormService.submitFormAndRefresh()
  customerFormService.resetListQuery()
}
</script>

<template>
  <Dialog
    :header="customerFormService.formTitle"
    v-model:visible="customerFormService.formOpen"
    :mask-closable="false"
    :destroy-on-close="true"
    width="1200px"
  >
    <Form
      ref="formRef"
      :model="customerFormService.formData"
      :rules="rules"
      :label-col="{ span: 8 }"
      :wrapper-col="{ span: 16 }"
    >
      <!-- 基本信息 -->
      <Grid :cols="2" :gap="4">
        <GridCol>
          <FormItem
            label="客户编码"
            name="customerCode"
          >
            <InputText v-model="customerFormService.formData.customerCode" placeholder="请输入客户编码" />
          </FormItem>
        </GridCol>
        <GridCol>
          <FormItem
            label="客户名称"
            name="customerName"
          >
            <InputText v-model="customerFormService.formData.customerName" placeholder="请输入客户名称" />
          </FormItem>
        </GridCol>
      </Grid>

      <Grid :cols="2" :gap="4">
        <GridCol>
          <FormItem label="客户类型" name="customerType">
            <DictSelect v-model="customerFormService.formData.customerType" key-code="customer_type" placeholder="请选择客户类型" />
          </FormItem>
        </GridCol>
        <GridCol>
          <FormItem label="信用等级" name="creditLevel">
            <DictSelect v-model="customerFormService.formData.creditLevel" key-code="credit_level" placeholder="请选择信用等级" />
          </FormItem>
        </GridCol>
      </Grid>

      <!-- 联系信息 -->
      <Divider label="联系信息" />

      <Grid :cols="2" :gap="4">
        <GridCol>
          <FormItem label="联系人姓名" name="contactName">
            <InputText v-model="customerFormService.formData.contactName" placeholder="请输入联系人姓名" />
          </FormItem>
        </GridCol>
        <GridCol>
          <FormItem label="联系人职位" name="contactPosition">
            <InputText v-model="customerFormService.formData.contactPosition" placeholder="请输入联系人职位" />
          </FormItem>
        </GridCol>
      </Grid>

      <Grid :cols="2" :gap="4">
        <GridCol>
          <FormItem label="联系人部门" name="contactDepartment">
            <InputText v-model="customerFormService.formData.contactDepartment" placeholder="请输入联系人部门" />
          </FormItem>
        </GridCol>
        <GridCol>
          <FormItem label="联系电话" name="contactPhone">
            <InputText v-model="customerFormService.formData.contactPhone" placeholder="请输入联系电话" />
          </FormItem>
        </GridCol>
      </Grid>

      <Grid :cols="2" :gap="4">
        <GridCol>
          <FormItem label="联系手机" name="contactMobile">
            <InputText v-model="customerFormService.formData.contactMobile" placeholder="请输入联系手机" />
          </FormItem>
        </GridCol>
        <GridCol>
          <FormItem label="联系邮箱" name="contactEmail">
            <InputText v-model="customerFormService.formData.contactEmail" placeholder="请输入联系邮箱" />
          </FormItem>
        </GridCol>
      </Grid>

      <!-- 地址信息 -->
      <Divider label="地址信息" />

      <Grid :cols="3" :gap="4">
        <GridCol>
          <FormItem label="省份" name="province">
            <InputText v-model="customerFormService.formData.province" placeholder="请输入省份" />
          </FormItem>
        </GridCol>
        <GridCol>
          <FormItem label="城市" name="city">
            <InputText v-model="customerFormService.formData.city" placeholder="请输入城市" />
          </FormItem>
        </GridCol>
        <GridCol>
          <FormItem label="区县" name="district">
            <InputText v-model="customerFormService.formData.district" placeholder="请输入区县" />
          </FormItem>
        </GridCol>
      </Grid>

      <Grid :cols="1" :gap="4">
        <GridCol>
          <FormItem label="详细地址" name="address">
            <InputText v-model="customerFormService.formData.address" placeholder="请输入详细地址" />
          </FormItem>
        </GridCol>
      </Grid>

      <Grid :cols="2" :gap="4">
        <GridCol>
          <FormItem label="收货地址" name="shippingAddress">
            <InputText v-model="customerFormService.formData.shippingAddress" placeholder="请输入收货地址" />
          </FormItem>
        </GridCol>
        <GridCol>
          <FormItem label="发票地址" name="billingAddress">
            <InputText v-model="customerFormService.formData.billingAddress" placeholder="请输入发票地址" />
          </FormItem>
        </GridCol>
      </Grid>

      <!-- 企业信息 -->
      <Divider label="企业信息" />

      <Grid :cols="2" :gap="4">
        <GridCol>
          <FormItem label="营业执照号" name="businessLicense">
            <InputText v-model="customerFormService.formData.businessLicense" placeholder="请输入营业执照号" />
          </FormItem>
        </GridCol>
        <GridCol>
          <FormItem label="税务登记号" name="taxId">
            <InputText v-model="customerFormService.formData.taxId" placeholder="请输入税务登记号" />
          </FormItem>
        </GridCol>
      </Grid>

      <Grid :cols="2" :gap="4">
        <GridCol>
          <FormItem label="开户银行" name="bankName">
            <InputText v-model="customerFormService.formData.bankName" placeholder="请输入开户银行" />
          </FormItem>
        </GridCol>
        <GridCol>
          <FormItem label="银行账号" name="bankAccount">
            <InputText v-model="customerFormService.formData.bankAccount" placeholder="请输入银行账号" />
          </FormItem>
        </GridCol>
      </Grid>

      <!-- 其他信息 -->
      <Divider label="其他信息" />

      <Grid :cols="2" :gap="4">
        <GridCol>
          <FormItem label="客户状态" name="status">
            <DictSelect v-model="customerFormService.formData.status" key-code="customer_status" placeholder="请选择客户状态" />
          </FormItem>
        </GridCol>
      </Grid>

      <Grid :cols="2" :gap="4">
        <GridCol>
          <FormItem label="备注" name="remark">
            <TextArea v-model:value="customerFormService.formData.remark" placeholder="请输入备注" :rows="3" />
          </FormItem>
        </GridCol>
      </Grid>
    </Form>
    <template #footer>
      <Button label="取消" severity="danger" @click="customerFormService.closeForm()" />
      <Button label="保存" @click="handleSubmit" />
    </template>
  </Dialog>
</template>
