import type { SkuResult } from '@/api/business/sku/model/sku-types'
import type { AxSkuTableSelectorService } from '../service/AxSkuTableSelectorService'
import { CustomColorText, CustomText } from '@/components/base/CustomText'
import { IconButton } from '@/components/base/IconButton'
import { h } from 'vue'

export function createSkuColumns(orderSkuService: AxSkuTableSelectorService) {
  const service = orderSkuService

  return [
    {
      title: 'SKU编码',
      dataIndex: 'skuCode',
      key: 'skuCode',
    },
    {
      title: '商品名称',
      dataIndex: 'productName',
      key: 'productName',
      width: 180,
    },
    {
      title: '商品编码',
      dataIndex: 'productCode',
      key: 'productCode',
      width: 120,
    },
    {
      title: '规格摘要',
      dataIndex: 'specSummary',
      key: 'specSummary',
      width: 150,
      customRender: ({ record }: { record: SkuResult }) => {
        return h(CustomText, { value: record.specSummary })
      },
    },
    {
      title: '库存',
      dataIndex: 'stock',
      key: 'stock',
      width: 80,
      customRender: ({ record }: { record: SkuResult }) => {
        const stock = record.stock || 0
        const color = stock > 0 ? '#52c41a' : '#ff4d4f'
        return h(CustomColorText, { value: stock, color })
      },
    },
    {
      title: '重量(kg)',
      dataIndex: 'weight',
      key: 'weight',
      width: 100,
      customRender: ({ record }: { record: SkuResult }) => {
        const weightStr = record.weight ? `${record.weight}kg` : undefined
        return h(CustomText, { value: weightStr })
      },
    },
    {
      title: '操作',
      key: 'action',
      width: 100,
      fixed: 'right' as const,
      customRender: ({ record }: { record: SkuResult }) => {
      // 这里可以使用 orderSkuService 进行操作
        const isSelected = service.isProductSelected(record)
        return h(IconButton, { label: isSelected ? '移除' : '添加', iconType: isSelected ? 'DeleteOutlined' : 'PlusOutlined', type: isSelected ? 'default' : 'primary', onClick: () => service.switchSku(record) })
      },
    },
  ]
}
