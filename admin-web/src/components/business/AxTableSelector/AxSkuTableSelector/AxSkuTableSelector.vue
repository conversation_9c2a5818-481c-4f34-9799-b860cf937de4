<script setup lang="ts">
import type {
  AxSkuTableSelectorService,
} from '@/components/business/AxTableSelector/AxSkuTableSelector/service/AxSkuTableSelectorService'
import {
  AX_SKU_TABLE_SELECTOR_SERVICE,
} from '@/components/business/AxTableSelector/AxSkuTableSelector/service/AxSkuTableSelectorService'
import { skuColumns } from '@/views/business/erp/product/config/productColumns'

const servive = inject<AxSkuTableSelectorService>(AX_SKU_TABLE_SELECTOR_SERVICE)!
</script>

<template>
  <!-- 商品搜索 -->
  <FlexRow class="mb-4 flex-shrink-0">
    <InputText
      v-model="servive.productSearchParam.skuNameExt"
      placeholder="搜索商品名称或SKU编码"
      class="w-72 mr-3"
    />
    <IconButton
      label="重置"
      icon-type="ReloadOutlined"
      @click="() => servive?.resetSearch()"
    />
  </FlexRow>

  <!-- Sku列表表格 -->
  <div class="flex-1 flex flex-col min-h-0">
    <Table
      :columns="skuColumns"
      :data-source="servive.productList"
      :loading="servive.productLoading"
      :scroll="{ y: 400, x: 400 }"
      :pagination="false"
      size="small"
      row-key="id"
    />

    <!-- 商品分页 -->
    <div class="mt-4 text-center flex-shrink-0">
      <Pagination
        v-model:current="servive.productSearchParam.pageParam.pageNum"
        v-model:page-size="servive.productSearchParam.pageParam.pageSize"
        size="small"
        :total="servive.productTotal"
        :show-size-changer="true"
        :show-quick-jumper="true"
        :show-total="(total: number, range: number[]) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`"
        @change="servive.onProductPageChange"
      />
    </div>
  </div>
</template>
