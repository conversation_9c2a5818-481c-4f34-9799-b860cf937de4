<script setup lang="ts">
import type { SupplierQueryParam, SupplierResult } from '@/api/business/supplier/model/supplier-form-model'
import { paramJsonApi } from '@/api/business/param-json/param-json-api'
import { supplierApi } from '@/api/business/supplier/supplier-api'
import { useToast } from 'primevue/usetoast'
import { onMounted, ref, watch } from 'vue'
// 移除 PrimeVue 原生组件导入，使用项目封装的组件
import { injectSupplierSelectService } from './service'
import SupplierModal from './supplier-modal.vue'

const supplierService = injectSupplierSelectService()
const toast = useToast()

// 内部写死的配置
const config = {
  disabled: false,
  placeholder: '请选择供应商',
  showClear: true,
  filter: true,
}

// 内部状态
const loading = ref(false)
const supplierOptions = ref<SupplierResult[]>([])
const selectedSupplierId = ref<number | undefined>(undefined)
const modalVisible = ref(false)

// 当selectedSupplierId变化时，更新supplierService
watch(selectedSupplierId, (newVal) => {
  if (newVal) {
    const selected = supplierOptions.value.find((item: SupplierResult) => item.id === newVal)
    if (selected) {
      supplierService.selectedSupplier = selected
    }
  }
  else {
    supplierService.selectedSupplier = undefined
  }
})

// 获取供应商列表
async function fetchSupplierList() {
  try {
    loading.value = true

    // 使用service中的查询参数
    const res = await supplierApi.supplierList(supplierService.queryParam)
    if (res.success && res.data) {
      supplierOptions.value = res.data
    }
  }
  catch (error) {
    toast.add({ severity: 'error', summary: '获取供应商列表失败', life: 3000 })
    console.error('获取供应商列表失败', error)
  }
  finally {
    loading.value = false
  }
}

// 处理选择变化
async function handleChange(event: { value: number | undefined }) {
  const value = event.value
  selectedSupplierId.value = value

  if (value) {
    const selected = supplierOptions.value.find((item: SupplierResult) => item.id === value)
    if (selected) {
      supplierService.selectedSupplier = selected

      // 当选择新供应商时，加载其配置信息
      await loadSupplierConfig(value)
    }
  }
  else {
    supplierService.selectedSupplier = undefined
  }
}

// 加载供应商配置
async function loadSupplierConfig(supplierId: number) {
  try {
    loading.value = true
    const res = await paramJsonApi.getSupplierConfig(supplierId)
    if (res.success && res.data) {
      supplierService.setSupplierConfig(res.data)
    }
    else {
      // 如果没有配置数据，初始化一个空的配置对象
      supplierService.setSupplierConfig({
        discountRate: 0,
        taxRate: 0,
        processingFee: [],
      })
    }
  }
  catch (error) {
    console.error('获取供应商配置失败', error)
    // 错误时设置默认值
    supplierService.setSupplierConfig({
      discountRate: 0,
      taxRate: 0,
      processingFee: [],
    })
  }
  finally {
    loading.value = false
  }
}

// 打开参数设置弹窗
function openParamModal(e: Event) {
  e.stopPropagation() // 阻止事件冒泡
  if (selectedSupplierId.value) {
    modalVisible.value = true
  }
  else {
    toast.add({ severity: 'warn', summary: '请先选择供应商', life: 3000 })
  }
}

// 设置查询参数
function setQueryParam(param: Partial<SupplierQueryParam>) {
  supplierService.setQueryParam(param)
  fetchSupplierList() // 参数变化时自动刷新列表
}

// 初始化
onMounted(() => {
  fetchSupplierList()
})

// 暴露方法和服务
defineExpose({
  fetchSupplierList,
  supplierOptions,
  supplierService,
  setQueryParam,
})
</script>

<template>
  <div class="relative w-full">
    <Select
      v-model="selectedSupplierId"
      :loading="loading"
      :disabled="config.disabled"
      :placeholder="config.placeholder"
      :show-clear="config.showClear"
      :filter="config.filter"
      :options="supplierOptions"
      option-label="supplierName"
      option-value="id"
      class="w-full"
      @change="handleChange"
    >
      <template #value="{ value }">
        <span v-if="value">
          {{ supplierOptions.find((item: SupplierResult) => item.id === value)?.supplierName }}
          ({{ supplierOptions.find((item: SupplierResult) => item.id === value)?.supplierCode || '无编码' }})
        </span>
      </template>
      <template #option="{ option }">
        <div>
          {{ option.supplierName }} ({{ option.supplierCode || '无编码' }})
        </div>
      </template>
      <template #empty>
        <span v-if="loading">加载中...</span>
        <span v-else>未找到符合条件的供应商</span>
      </template>
    </Select>
    <Button
      v-if="selectedSupplierId"
      text
      size="small"
      class="absolute right-2 top-1/2 transform -translate-y-1/2 z-10"
      @click="openParamModal"
    >
      <template #icon>
        <Icon icon-type="EyeOutlined" />
      </template>
    </Button>
    <SupplierModal
      v-model:visible="modalVisible"
    />
  </div>
</template>
