<!--
  * 员工 表格 弹窗 选择框
-->
<script setup lang="ts">
import { employeeApi } from '@/api/system/employee/employee-api'
import DepartmentTreeSelect from '@/components/system/department-tree-select/index.vue'
import { PAGE_SIZE, PAGE_SIZE_OPTIONS } from '@/constants/common-const'
import { sentry } from '@/lib/sentry'
import { useToast } from 'primevue/usetoast'
import { computed, reactive, ref } from 'vue'

// ----------------------- 以下是字段定义 emits props ---------------------
const emits = defineEmits(['selectData'])
defineExpose({
  showModal,
})

const toast = useToast()

// ----------------------- modal  显示与隐藏 ---------------------

const visible = ref(false)
async function showModal(selectEmployeeId) {
  originalRowKeyList.value = selectEmployeeId || []
  selectedRowKeyList.value = selectEmployeeId || []
  visible.value = true
  onSearch()
}
function closeModal() {
  Object.assign(params, defaultParams)
  selectedRowKeyList.value = []
  visible.value = false
}
// ----------------------- 员工查询表单与查询 ---------------------
const tableLoading = ref(false)
const departmentTreeSelect = ref()
const total = ref()

let defaultParams = {
  departmentId: undefined,
  disabledFlag: undefined,
  employeeIdList: undefined,
  keyword: undefined,
  searchCount: undefined,
  pageNum: 1,
  pageSize: PAGE_SIZE,
  sortItemList: undefined,
}
const params = reactive({ ...defaultParams })
function reset() {
  Object.assign(params, defaultParams)
  queryEmployee()
}

function onSearch() {
  params.pageNum = 1
  queryEmployee()
}

async function queryEmployee() {
  tableLoading.value = true
  try {
    const res = await employeeApi.queryEmployeePage(params)
    tableData.value = res.data.list
    total.value = res.data.total
  }
  catch (error) {
    sentry.captureError(error)
  }
  finally {
    tableLoading.value = false
  }
}

// ----------------------- 员工表格选择 ---------------------
const originalRowKeyList = ref([])
let selectedRowKeyList = ref([])
const hasSelected = computed(() => selectedRowKeyList.value.length !== originalRowKeyList.value.length)

function onSelectEmployee() {
  if (!hasSelected.value) {
    toast.add({ severity: 'warn', summary: '请选择角色人员', life: 3000 })
    return
  }
  // 过滤出新选择的人员id
  const newEmployeeIdList = selectedRowKeyList.value.filter(id => !originalRowKeyList.value.includes(id))
  emits('selectData', newEmployeeIdList)
  closeModal()
}

// ----------------------- 员工表格渲染 ---------------------
const tableData = ref([])
</script>

<template>
  <Dialog v-model:visible="visible" :style="{ width: '900px' }" header="选择人员" modal @hide="closeModal">
    <Form class="mb-4">
      <div class="grid grid-cols-12 gap-4">
        <FormItem label="关键字" class="col-span-3">
          <InputText v-model="params.keyword" placeholder="关键字" class="w-full" />
        </FormItem>
        <FormItem label="部门" class="col-span-3">
          <DepartmentTreeSelect ref="departmentTreeSelect" v-model="params.departmentId" class="w-full" />
        </FormItem>
        <FormItem label="状态" class="col-span-3">
          <Dropdown v-model="params.disabledFlag" :options="[{ label: '启用', value: 0 }, { label: '禁用', value: 1 }]" option-label="label" option-value="value" placeholder="请选择状态" show-clear class="w-full" />
        </FormItem>
        <div class="col-span-3 flex gap-2">
          <Button type="button" @click="onSearch">
            <template #icon>
              <Icon icon-type="SearchOutlined" />
            </template>
            查询
          </Button>
          <Button type="button" severity="secondary" @click="reset">
            <template #icon>
              <Icon icon-type="ReloadOutlined" />
            </template>
            重置
          </Button>
        </div>
      </div>
    </Form>

    <DataTable
      v-model:selection="selectedRowKeyList"
      :value="tableData"
      :loading="tableLoading"
      selection-mode="multiple"
      data-key="employeeId"
      size="small"
      scroll-height="300px"
      class="border border-gray-200"
    >
      <Column selection-mode="multiple" :selectable="(data) => !originalRowKeyList.includes(data.employeeId)" />
      <Column field="actualName" header="姓名" />
      <Column field="phone" header="手机号" />
      <Column field="gender" header="性别">
        <template #body="{ data }">
          <span>{{ $smartEnumPlugin.getDescByValue('GENDER_ENUM', data.gender) }}</span>
        </template>
      </Column>
      <Column field="loginName" header="登录账号" />
      <Column field="disabledFlag" header="状态">
        <template #body="{ data }">
          <Tag :severity="data.disabledFlag ? 'danger' : 'success'" :value="data.disabledFlag ? '禁用' : '启用'" />
        </template>
      </Column>
    </DataTable>

    <div class="flex justify-end mt-4">
      <Paginator
        :first="(params.pageNum - 1) * params.pageSize"
        :rows="params.pageSize"
        :total-records="total"
        :rows-per-page-options="PAGE_SIZE_OPTIONS.map(Number)"
        template="FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink RowsPerPageDropdown"
        @page="(event) => { params.pageNum = event.page + 1; params.pageSize = event.rows; queryEmployee(); }"
      />
    </div>

    <template #footer>
      <div class="flex justify-end gap-2">
        <Button label="取消" severity="danger" @click="closeModal" />
        <Button label="保存" @click="onSelectEmployee" />
      </div>
    </template>
  </Dialog>
</template>
