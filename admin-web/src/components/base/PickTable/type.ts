import type { BaseModel } from '@/api/base-model/base-model'
import type { TableColumn } from '@/service/base/interface/Table'

export interface PickTableProps {
  // 单向数据
  rawTableData: BaseModel[]
  rawTableColumn: TableColumn[]
  selectedTableColumn: TableColumn[]
  selectedTableData: BaseModel[]
  modelValue?: Record<string, unknown>[]

  selectItem?: (item: Record<string, unknown>) => void

  removeItemBySource?: (item: Record<string, unknown>) => void

  removeItemByDestination?: (item: Record<string, unknown>) => void

  clearSelected?: (item: Record<string, unknown>) => void

  isItemSelected?: (item: Record<string, unknown>) => boolean
}

export interface PickTableEmits {
  'update:modelValue': [value: Record<string, unknown>[]]
}
