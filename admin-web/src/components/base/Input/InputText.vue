<script setup lang="ts">
import type { FormFieldSlots } from '@primevue/forms'
import Icon from '@/components/base/Icon/Icon.vue'
import PInputText from 'primevue/inputtext'

const {
  placeholder = '',
  disabled = false,
  size = 'middle',
} = defineProps<{
  placeholder?: string
  disabled?: boolean
  size?: 'small' | 'middle' | 'large'
  fieldValue?: FormFieldSlots
}>()

const modelValue = defineModel<string | undefined>({ default: undefined })
const placeholderRef = ref('')

// 清空输入框
function handleClear() {
  modelValue.value = ''
}
// 失去焦点
function handleBlur() {
  placeholderRef.value = ''
}
// 聚焦
function handleFocus() {
  if (!modelValue.value) {
    placeholderRef.value = placeholder
  }
}

// 映射尺寸
const primeSize = computed(() => {
  switch (size) {
    case 'small':
      return 'small'
    case 'large':
      return 'large'
    case 'middle':
    default:
      return undefined
  }
})
</script>

<template>
  <div class="w-full relative">
    <PInputText
      v-model="modelValue"
      v-bind="$attrs"
      class="w-full pr-8!"
      :placeholder="placeholderRef"
      :disabled="disabled"
      :size="primeSize"
      @focus="handleFocus"
      @blur="handleBlur"
    />
    <Icon v-if="!!modelValue && !disabled" icon-type="CloseOutlined" class="w-4 h-4 absolute cursor-pointer text-gray-500 hover:text-gray-700 transition-colors duration-200 right-3 top-1/2 -translate-y-1/2" @click="handleClear" />
  </div>
</template>
