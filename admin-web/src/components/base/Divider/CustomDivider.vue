<script setup lang="ts">
import type { DividerProps } from './type'
import PrimeDivider from 'primevue/divider'

defineOptions({
  inheritAttrs: false,
})

// 使用 Vue 3.4+ 最新特性
const {
  type = 'solid',
  layout = 'horizontal',
  align = 'center',
  className = '',
  label = '',
} = defineProps<DividerProps>()
</script>

<template>
  <PrimeDivider
    :type="type"
    :layout="layout"
    :align="align"
    :class="className"
  >
    <span v-if="label">{{ label }}</span>
  </PrimeDivider>
</template>
