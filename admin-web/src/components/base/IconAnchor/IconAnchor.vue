<script setup lang="ts">
import type { IconAnchorProps } from './type'

defineOptions({
  inheritAttrs: false,
})

const {
  label = 'undefined',
  iconType = 'UserOutlined',
  spin = false,
  rotate = 0,
  color = 'inherit',
} = defineProps<IconAnchorProps>()

const style = computed(() => {
  return {
    color,
  }
})
</script>

<template>
  <a v-bind="$attrs" :style="style" class="cursor-pointer">
    <Icon :icon-type="iconType" :spin="spin" :rotate="rotate" />
    {{ label }}
  </a>
</template>
