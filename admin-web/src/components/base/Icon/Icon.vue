<script setup lang="ts">
import type { IconProps } from './type'
import { computed } from 'vue'

defineOptions({
  inheritAttrs: false,
})

const { iconType = 'UserOutlined', spin = false, rotate = 0 } = defineProps<IconProps>()

// 将 Ant Design 图标名称映射到 PrimeIcons 类名
const primeIconClass = computed(() => {
  const iconMap: Record<string, string> = {
    // 用户相关
    UserOutlined: 'pi-user',
    UserAddOutlined: 'pi-user-plus',
    UserDeleteOutlined: 'pi-user-minus',

    // 操作相关
    PlusOutlined: 'pi-plus',
    EditOutlined: 'pi-pencil',
    DeleteOutlined: 'pi-trash',
    SearchOutlined: 'pi-search',
    ReloadOutlined: 'pi-refresh',
    EyeOutlined: 'pi-eye',
    DownloadOutlined: 'pi-download',
    UploadOutlined: 'pi-upload',
    ImportOutlined: 'pi-file-import',
    ExportOutlined: 'pi-file-export',
    DragOutlined: 'pi-arrows-alt',

    // 方向相关
    LeftOutlined: 'pi-chevron-left',
    RightOutlined: 'pi-chevron-right',
    UpOutlined: 'pi-chevron-up',
    DownOutlined: 'pi-chevron-down',

    // 状态相关
    CheckOutlined: 'pi-check',
    CheckCircleOutlined: 'pi-check-circle',
    CloseOutlined: 'pi-times',
    CloseCircleOutlined: 'pi-times-circle',
    ExclamationOutlined: 'pi-exclamation-triangle',
    InfoOutlined: 'pi-info-circle',
    QuestionOutlined: 'pi-question-circle',
    PlayCircleOutlined: 'pi-play-circle',
    StopOutlined: 'pi-stop-circle',

    // 文件相关
    FileOutlined: 'pi-file',
    FolderOutlined: 'pi-folder',
    SaveOutlined: 'pi-save',
    CopyOutlined: 'pi-copy',

    // 设置相关
    SettingOutlined: 'pi-cog',
    MenuOutlined: 'pi-bars',
    MoreOutlined: 'pi-ellipsis-h',

    // 时间相关
    CalendarOutlined: 'pi-calendar',
    ClockCircleOutlined: 'pi-clock',

    // 警告相关
    WarningOutlined: 'pi-exclamation-triangle',
    ExclamationTriangleOutlined: 'pi-exclamation-triangle',

    // 其他常用
    HomeOutlined: 'pi-home',
    HeartOutlined: 'pi-heart',
    StarOutlined: 'pi-star',
    StarFilled: 'pi-star-fill',
    BellOutlined: 'pi-bell',
    MailOutlined: 'pi-envelope',
    PhoneOutlined: 'pi-phone',
    LockOutlined: 'pi-lock',
    UnlockOutlined: 'pi-unlock',
  }

  // 移除 Outlined 后缀并查找映射
  const baseIconName = iconType?.replace('Outlined', '') || ''
  const mappedIcon = iconMap[iconType] || iconMap[`${baseIconName}Outlined`]

  return mappedIcon || 'pi-question' // 默认图标
})

// 动态样式
const iconStyle = computed(() => {
  const styles: Record<string, string> = {}

  if (rotate !== 0) {
    styles.transform = `rotate(${rotate}deg)`
  }

  return styles
})

// 动态类名
const iconClasses = computed(() => {
  const classes = ['pi', primeIconClass.value]

  if (spin) {
    classes.push('pi-spin')
  }

  return classes
})
</script>

<template>
  <i
    :class="iconClasses"
    :style="iconStyle"
  />
</template>

<style scoped>
/* 旋转动画 */
.pi-spin {
  animation: pi-spin 1s linear infinite;
}

@keyframes pi-spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>
