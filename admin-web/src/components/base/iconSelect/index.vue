<!--
  * 图标选择
-->
<script setup lang="ts">
import PPopover from 'primevue/popover'
import { computed, ref } from 'vue'

// ------------ 对外抛出选择图标事件 ------------
const emit = defineEmits(['updateIcon'])

// PrimeIcons 图标列表（部分常用图标）
const primeIconsArray = [
  'pi-home',
  'pi-user',
  'pi-users',
  'pi-cog',
  'pi-search',
  'pi-plus',
  'pi-minus',
  'pi-times',
  'pi-check',
  'pi-pencil',
  'pi-trash',
  'pi-eye',
  'pi-eye-slash',
  'pi-heart',
  'pi-star',
  'pi-bookmark',
  'pi-flag',
  'pi-bell',
  'pi-envelope',
  'pi-phone',
  'pi-calendar',
  'pi-clock',
  'pi-download',
  'pi-upload',
  'pi-file',
  'pi-folder',
  'pi-copy',
  'pi-save',
  'pi-print',
  'pi-share',
  'pi-link',
  'pi-external-link',
  'pi-refresh',
  'pi-sync',
  'pi-undo',
  'pi-redo',
  'pi-lock',
  'pi-unlock',
  'pi-shield',
  'pi-key',
  'pi-question',
  'pi-info',
  'pi-exclamation-triangle',
  'pi-chevron-left',
  'pi-chevron-right',
  'pi-chevron-up',
  'pi-chevron-down',
  'pi-arrow-left',
  'pi-arrow-right',
  'pi-arrow-up',
  'pi-arrow-down',
  'pi-angle-left',
  'pi-angle-right',
  'pi-angle-up',
  'pi-angle-down',
  'pi-sort',
  'pi-sort-up',
  'pi-sort-down',
  'pi-bars',
  'pi-ellipsis-h',
  'pi-ellipsis-v',
  'pi-filter',
  'pi-list',
  'pi-th',
  'pi-th-large',
  'pi-table',
  'pi-chart',
  'pi-chart-bar',
  'pi-chart-line',
  'pi-chart-pie',
  'pi-image',
  'pi-video',
  'pi-music',
  'pi-microphone',
  'pi-volume-up',
  'pi-volume-down',
  'pi-volume-off',
  'pi-play',
  'pi-pause',
  'pi-stop',
  'pi-step-backward',
  'pi-step-forward',
  'pi-eject',
  'pi-shopping-cart',
  'pi-credit-card',
  'pi-money-bill',
  'pi-gift',
  'pi-tag',
  'pi-tags',
  'pi-map',
  'pi-map-marker',
  'pi-compass',
  'pi-globe',
  'pi-building',
  'pi-hospital',
  'pi-car',
  'pi-truck',
  'pi-plane',
  'pi-ship',
  'pi-train',
  'pi-wifi',
  'pi-bluetooth',
  'pi-desktop',
  'pi-mobile',
  'pi-tablet',
  'pi-laptop',
  'pi-database',
  'pi-server',
  'pi-code',
  'pi-bug',
  'pi-wrench',
  'pi-sliders-h',
  'pi-palette',
  'pi-paint-bucket',
]

// ------------ 显示/隐藏 ------------
const visible = ref(false)

// ------------ 展开更多 ------------
const SHOW_MORE_LENGTH = 35
const showMoreIndex = ref(SHOW_MORE_LENGTH)
function showMore() {
  showMoreIndex.value = -1
}

// ------------ 图标展示与搜索 ------------

const selectIconArray = ref([...primeIconsArray])

const iconLoopArray = computed(() => {
  return selectIconArray.value.slice(0, showMoreIndex.value === -1 ? undefined : showMoreIndex.value)
})

const searchValue = ref('')
function updateSelectIconArray() {
  if (!searchValue.value) {
    selectIconArray.value = primeIconsArray
  }
  else {
    selectIconArray.value = primeIconsArray.filter(e => e.toLowerCase().includes(searchValue.value.toLowerCase()))
  }

  if (selectIconArray.value.length > SHOW_MORE_LENGTH) {
    showMoreIndex.value = SHOW_MORE_LENGTH
  }
  else {
    showMoreIndex.value = -1
  }
}

function handleClick(icon: string) {
  visible.value = false
  // 移除 pi- 前缀，转换为 Ant Design 风格的名称
  const iconName = `${icon.replace('pi-', '').split('-').map(word =>
    word.charAt(0).toUpperCase() + word.slice(1),
  ).join('')}Outlined`

  emit('updateIcon', iconName)
}
</script>

<template>
  <div>
    <PPopover v-model:visible="visible">
      <template #header>
        <FormItem>
          <InputText
            v-model="searchValue"
            placeholder="输入英文关键词进行搜索"
            @input="updateSelectIconArray"
          >
            <template #suffix>
              <Icon icon-type="SearchOutlined" />
            </template>
          </InputText>
        </FormItem>
      </template>

      <div class="overflow-auto text-20px w-410px h-300px flex flex-wrap content-start">
        <div
          v-for="item in iconLoopArray"
          :key="item"
          class="w-45px h-40px m-5px cursor-pointer text-center rounded-6px border border-gray-300 hover:bg-blue-500 flex items-center justify-center"
          @click="handleClick(item)"
        >
          <i class="pi" :class="[item]" />
        </div>
        <div v-show="showMoreIndex > 0" class="w-full flex justify-center">
          <Button link @click="showMore">
            点击展开更多图标
          </Button>
        </div>
      </div>

      <slot name="iconSelect" />
    </PPopover>
  </div>
</template>
