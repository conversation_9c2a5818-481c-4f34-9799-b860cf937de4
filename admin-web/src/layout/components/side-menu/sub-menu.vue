<!--
  * 传统菜单-递归子菜单 (PrimeVue重构版)
-->
<script setup lang="ts">
import { computed } from 'vue'

interface MenuItem {
  menuId: number
  menuName: string
  menuTitle?: string
  parentId: number
  menuType: number
  path?: string
  frameUrl?: string
  visibleFlag: boolean
  disabledFlag: boolean
  icon?: string
  children?: MenuItem[]
}

const props = defineProps({
  menuInfo: {
    type: Object,
    default: () => ({}),
  },
  collapsed: {
    type: Boolean,
    default: false,
  },
  openKeys: {
    type: Array,
    default: () => [],
  },
  selectedKeys: {
    type: Array,
    default: () => [],
  },
})

const emits = defineEmits(['turnToPage', 'updateOpenKeys'])

function turnToPage(menu: MenuItem) {
  emits('turnToPage', menu)
}

// 是否展开子菜单
const isOpen = computed(() => props.openKeys.includes(props.menuInfo.menuId))

// 切换子菜单展开状态
function toggleSubMenu() {
  if (props.collapsed)
    return

  const newOpenKeys = [...props.openKeys]
  const index = newOpenKeys.indexOf(props.menuInfo.menuId)

  if (index > -1) {
    newOpenKeys.splice(index, 1)
  }
  else {
    newOpenKeys.push(props.menuInfo.menuId)
  }

  emits('updateOpenKeys', newOpenKeys)
}

// 动画处理函数
function onEnter(el: Element) {
  const element = el as HTMLElement
  element.style.height = '0'
  element.style.opacity = '0'
  element.style.transition = 'height 0.3s ease-out, opacity 0.3s ease-out'
  // 下一帧开始动画
  requestAnimationFrame(() => {
    element.style.height = `${element.scrollHeight}px`
    element.style.opacity = '1'
  })
}

function onAfterEnter(el: Element) {
  const element = el as HTMLElement
  element.style.height = ''
  element.style.transition = ''
}

function onLeave(el: Element) {
  const element = el as HTMLElement
  element.style.height = `${element.scrollHeight}px`
  element.style.opacity = '1'
  element.style.transition = 'height 0.3s ease-in, opacity 0.3s ease-in'
  // 强制重绘
  void element.offsetHeight
  element.style.height = '0'
  element.style.opacity = '0'
}

function onAfterLeave(el: Element) {
  const element = el as HTMLElement
  element.style.height = ''
  element.style.opacity = ''
  element.style.transition = ''
}
</script>

<template>
  <div>
    <!-- 子菜单标题 -->
    <div
      class="flex items-center py-3 px-4 cursor-pointer transition-all duration-300 rounded-md mx-2 my-0.5 hover:bg-black/5 dark:text-white/65 dark:hover:bg-white/8 dark:hover:text-white light:text-black/85 light:hover:bg-black/6"
      :class="{
        'justify-center px-0 mx-1': collapsed,
      }"
      @click="toggleSubMenu"
    >
      <Icon
        :icon-type="menuInfo.icon"
        class="text-base min-w-4"
        :class="collapsed ? 'mr-0' : 'mr-2'"
      />
      <span
        v-if="!collapsed"
        class="text-sm whitespace-nowrap overflow-hidden text-ellipsis flex-1"
      >
        {{ menuInfo.menuName }}
      </span>
      <i
        v-if="!collapsed"
        class="pi pi-chevron-down text-xs transition-transform duration-300"
        :class="{ 'rotate-180': isOpen }"
      />
    </div>

    <!-- 子菜单内容 -->
    <Transition
      name="submenu-expand"
      @enter="onEnter"
      @after-enter="onAfterEnter"
      @leave="onLeave"
      @after-leave="onAfterLeave"
    >
      <div v-show="isOpen && !collapsed" class="overflow-hidden">
        <template v-for="item in menuInfo.children" :key="item.menuId">
          <template v-if="item.visibleFlag && !item.disabledFlag">
            <template v-if="!item.children || item.children.length === 0">
              <div
                class="flex items-center ml-6 py-2 px-4 cursor-pointer transition-all duration-300 rounded-md mx-2 my-0.5 text-xs hover:bg-black/5 dark:text-white/65 dark:hover:bg-white/8 dark:hover:text-white light:text-black/85 light:hover:bg-black/6"
                :class="{
                  'bg-primary/10 text-primary dark:bg-primary/20 dark:text-primary-300 light:bg-primary/10 light:text-primary': selectedKeys.includes(item.menuId),
                }"
                @click="turnToPage(item)"
              >
                <Icon :icon-type="item.icon" class="text-sm min-w-3.5 mr-2" />
                <span class="text-xs whitespace-nowrap overflow-hidden text-ellipsis flex-1">{{ item.menuName }}</span>
              </div>
            </template>
            <template v-else>
              <SubMenu
                :key="item.menuId"
                :menu-info="item"
                :collapsed="collapsed"
                :open-keys="openKeys"
                :selected-keys="selectedKeys"
                @turn-to-page="turnToPage"
                @update-open-keys="emits('updateOpenKeys', $event)"
              />
            </template>
          </template>
        </template>
      </div>
    </Transition>
  </div>
</template>
