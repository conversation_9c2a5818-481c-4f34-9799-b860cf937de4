<!--
  * 第一列菜单
-->
<script setup lang="ts">
import logoImg from '@/assets/images/logo/system-logo.png'
import { HOME_PAGE_NAME } from '@/constants/system/home-const'
import { MENU_TYPE_ENUM } from '@/constants/system/menu-const'
import { router } from '@/router'
import { useAppConfigStore } from '@/store/modules/system/app-config'
import { useUserStore } from '@/store/modules/system/user'
import { find, isEmpty } from 'lodash-es'
import { computed, ref } from 'vue'
import menuEmitter from './side-expand-menu-mitt'

const websiteName = computed(() => useAppConfigStore().websiteName)
const theme = computed(() => useAppConfigStore().$state.sideMenuTheme)
const menuTree = computed(() => useUserStore().getMenuTree || [])

// 展开菜单的顶级目录名字适配，只展示两个字为好
function menuNameAdapter(name: string) {
  return name.substring(0, 2)
}

// 选中的顶级菜单
const selectedKeys = ref<string[]>([])

// 菜单项数据转换
const menuItems = computed(() => {
  return menuTree.value
    .filter(item => item.visibleFlag)
    .map(item => ({
      id: item.menuId.toString(),
      label: item.menuName,
      icon: item.icon || 'HomeOutlined',
      menuType: item.menuType,
      children: item.children,
      visibleFlag: item.visibleFlag,
    }))
})

// 选中菜单，页面跳转
function onSelectMenuItem(menuItem: any) {
  selectedKeys.value = [menuItem.id]
  const originalItem = menuTree.value.find(item => item.menuId.toString() === menuItem.id)
  if (originalItem) {
    if (originalItem.menuType === MENU_TYPE_ENUM.MENU.value && (isEmpty(originalItem.children) || originalItem.children?.every((e: any) => !e.visibleFlag))) {
      useUserStore().deleteKeepAliveIncludes(originalItem.menuId.toString())
      router.push({ name: originalItem.menuId.toString() })
    }
    menuEmitter.emit('selectTopMenu', originalItem)
  }
}

// 更新选中的菜单
function updateSelectKey(key: string) {
  selectedKeys.value = [key]
  const selectMenu = find(menuTree.value, { menuId: Number(key) })
  if (selectMenu) {
    menuEmitter.emit('selectTopMenu', selectMenu)
  }
}

// 点击logo回到首页
function onGoHome() {
  router.push({ name: HOME_PAGE_NAME })
}

defineExpose({ updateSelectKey })
</script>

<template>
  <div class="h-full">
    <!-- 顶部logo区域 -->
    <div
      class="h-14 leading-14 px-4 w-full z-100 flex justify-between items-center cursor-pointer"
      :style="{ color: theme === 'light' ? '#001529' : '#ffffff' }"
      @click="onGoHome"
    >
      <img class="w-8 h-8" :src="logoImg">
      <div class="text-base font-semibold overflow-hidden break-words whitespace-nowrap">
        {{ websiteName }}
      </div>
    </div>
    <!-- 一级菜单展示 -->
    <div class="w-full">
      <div
        v-for="item in menuItems"
        :key="item.id"
        class="flex items-center p-3 cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
        :class="{ 'bg-blue-50 dark:bg-blue-900 border-r-2 border-blue-500': selectedKeys.includes(item.id) }"
        @click="onSelectMenuItem(item)"
      >
        <Icon :icon-type="item.icon" class="mr-2" />
        <span>{{ menuNameAdapter(item.label) }}</span>
      </div>
    </div>
  </div>
</template>
