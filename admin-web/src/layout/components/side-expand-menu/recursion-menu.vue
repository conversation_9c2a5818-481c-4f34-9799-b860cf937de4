<!--
  * 递归菜单
-->
<script setup lang="ts">
import Menu from '@/components/base/Menu'
import { router } from '@/router'
import { useUserStore } from '@/store/modules/system/user'
import { computed, ref } from 'vue'
import menuEmitter from './side-expand-menu-mitt'

interface MenuItem {
  menuId: number
  menuName: string
  icon?: string
  visibleFlag: boolean
  children?: MenuItem[]
}

interface MenuData {
  menuName?: string
  children?: MenuItem[]
}

interface PrimeMenuItem {
  key: string
  label: string
  icon?: string
  command?: () => void
  items?: PrimeMenuItem[]
}

// 选中的顶级菜单
const topMenu = ref<MenuData>({})
menuEmitter.on('selectTopMenu', onSelectTopMenu)

// 监听选中顶级菜单事件
function onSelectTopMenu(selectedTopMenu: any) {
  topMenu.value = selectedTopMenu as MenuData
  selectedKeys.value = []
}

// 展开的菜单
const selectedKeys = ref<string[]>([])

function updateSelectKeyAndOpenKey(parentList: any, currentSelectKey: string) {
  if (!parentList) {
    return
  }
  selectedKeys.value = [currentSelectKey]
}

// 转换菜单数据为 PrimeVue Menu 格式
const menuItems = computed(() => {
  if (!topMenu.value.children || topMenu.value.children.length === 0) {
    return []
  }
  return convertMenuData(topMenu.value.children)
})

function convertMenuData(menuList: MenuItem[]): PrimeMenuItem[] {
  return menuList
    .filter((item: MenuItem) => item.visibleFlag)
    .map((item: MenuItem) => {
      const menuItem: PrimeMenuItem = {
        key: item.menuId.toString(),
        label: item.menuName,
        icon: item.icon,
        command: () => turnToPage(item),
      }

      if (item.children && item.children.length > 0) {
        menuItem.items = convertMenuData(item.children)
        delete menuItem.command // 有子菜单的项不设置 command
      }

      return menuItem
    })
}

// 页面跳转
function turnToPage(route: MenuItem) {
  useUserStore().deleteKeepAliveIncludes(route.menuId.toString())
  router.push({ name: route.menuId.toString() })
}

defineExpose({ updateSelectKeyAndOpenKey })
</script>

<template>
  <div v-show="topMenu.children && topMenu.children.length > 0" class="h-screen bg-white">
    <!-- 顶部顶级菜单名称 -->
    <div class="flex items-center justify-center h-[var(--header-user-height)] text-base text-slate-600 border-b border-r border-gray-100">
      <span>{{ topMenu.menuName }}</span>
    </div>
    <!-- 次级菜单展示 -->
    <div class="flex h-[90%] overflow-auto text-slate-600">
      <Menu :model="menuItems" class="w-full border-0">
        <template #item="{ item, props }">
          <a v-ripple class="flex items-center" v-bind="props.action">
            <Icon v-if="item.icon" :icon-type="item.icon" class="mr-2" />
            <span>{{ item.label }}</span>
          </a>
        </template>
      </Menu>
    </div>
  </div>
</template>
