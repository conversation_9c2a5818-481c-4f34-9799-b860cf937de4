<!--
  * 头部一整行
-->
<script setup lang="ts">
import HeaderAvatar from './header-avatar.vue'
import HeaderMessage from './header-message.vue'
import HeaderSettingDrawer from './header-setting.vue'

// 设置
const headerSetting = ref()
function showSetting() {
  headerSetting.value.show()
}
</script>

<template>
  <div class="inline-flex items-center gap-2 pr-3">
    <div class="h-full flex items-center">
      <HeaderMessage />
      <Button variant="text" rounded @click="showSetting">
        <template #icon>
          <Icon icon-type="SettingOutlined" />
        </template>
      </Button>
    </div>
    <div class="h-full cursor-pointer flex items-center hover:text-primary hover:bg-gray-50">
      <HeaderAvatar />
    </div>
    <HeaderSettingDrawer ref="headerSetting" />
  </div>
</template>
