<!--
  * 消息通知
-->

<script setup lang="ts">
import { messageApi } from '@/api/support/message-api.js'
import localKey from '@/constants/local-storage-key-const'
import { sentry } from '@/lib/sentry'
import { useUserStore } from '@/store/modules/system/user.js'
import { localRead } from '@/utils/local-util'
import dayjs from 'dayjs'
import Badge from 'primevue/badge'
import Popover from 'primevue/popover'
import TabPanel from 'primevue/tabpanel'
import TabView from 'primevue/tabview'
import Tag from 'primevue/tag'
import { computed, ref } from 'vue'
import { useRouter } from 'vue-router'
import MessageDetailModal from './header-message-detail-modal.vue'

const loading = ref(false)

const op = ref()

function toggle(event: Event) {
  op.value.toggle(event)
  queryMessage()
  loadToBeDoneList()
}

function closeMessage() {
  op.value.hide()
}

// ------------------------- 查询消息  -------------------------

// 未读消息
const unreadMessageCount = computed(() => {
  return useUserStore().unreadMessageCount
})

// 消息列表
const messageList = ref([])

// 查询我的未读消息
async function queryMessage() {
  try {
    loading.value = true
    const responseModel = await messageApi.queryMessage({
      pageNum: 1,
      pageSize: 3,
      readFlag: false,
    })
    messageList.value = responseModel.data.list
    // 若中途有新消息了 打开列表也能及时更新未读数量
    useUserStore().queryUnreadMessageCount()
  }
  catch (e) {
    sentry.captureError(e)
  }
  finally {
    loading.value = false
  }
}

const messageDetailModalRef = ref()

function showMessageDetail(data?: unknown) {
  messageDetailModalRef.value.show(data)
  closeMessage()
}

const router = useRouter()

function gotoMessage() {
  closeMessage()
  router.push({ path: '/account', query: { menuId: 'message' } })
}

// ------------------------- 待办工作  -------------------------

// 待办工作数
const toBeDoneCount = computed(() => {
  return useUserStore().toBeDoneCount
})

// 待办工作列表
const toBeDoneList = ref([])

async function loadToBeDoneList() {
  try {
    loading.value = true
    const localToBeDoneList = localRead(localKey.TO_BE_DONE)
    if (localToBeDoneList) {
      toBeDoneList.value = JSON.parse(localToBeDoneList).filter((e: Recordable) => !e.doneFlag)
    }
  }
  catch (err) {
    sentry.captureError(err)
  }
  finally {
    loading.value = false
  }
}

// ------------------------- 时间计算  -------------------------
function timeago(dateStr: string) {
  const dateTimeStamp = dayjs(dateStr).toDate().getTime()
  let result = ''
  const minute = 1000 * 60 // 把分，时，天，周，半个月，一个月用毫秒表示
  const hour = minute * 60
  const day = hour * 24
  const week = day * 7
  const month = day * 30
  const now = new Date().getTime() // 获取当前时间毫秒
  const diffValue = now - dateTimeStamp // 时间差

  if (diffValue < 0) {
    return '刚刚'
  }
  const minC = diffValue / minute // 计算时间差的分，时，天，周，月
  const hourC = diffValue / hour
  const dayC = diffValue / day
  const weekC = diffValue / week
  const monthC = diffValue / month
  if (monthC >= 1 && monthC <= 3) {
    result = ` ${Number.parseInt(monthC)}月前`
  }
  else if (weekC >= 1 && weekC <= 3) {
    result = ` ${Number.parseInt(weekC)}周前`
  }
  else if (dayC >= 1 && dayC <= 6) {
    result = ` ${Number.parseInt(dayC)}天前`
  }
  else if (hourC >= 1 && hourC <= 23) {
    result = ` ${Number.parseInt(hourC)}小时前`
  }
  else if (minC >= 1 && minC <= 59) {
    result = ` ${Number.parseInt(minC)}分钟前`
  }
  else if (diffValue >= 0 && diffValue <= minute) {
    result = '刚刚'
  }
  else {
    const datetime = new Date()
    datetime.setTime(dateTimeStamp)
    const year = datetime.getFullYear()
    const month = datetime.getMonth() + 1 < 10 ? `0${datetime.getMonth() + 1}` : datetime.getMonth() + 1
    const date = datetime.getDate() < 10 ? `0${datetime.getDate()}` : datetime.getDate()
    result = `${year}-${month}-${date}`
  }
  return result
}
</script>

<template>
  <div>
    <OverlayBadge :visible="unreadMessageCount + toBeDoneCount > 0" :value="unreadMessageCount + toBeDoneCount" severity="danger">
      <Button variant="text" rounded class="p-1" @click="toggle">
        <template #icon>
          <Icon icon-type="BellOutlined" class="text-base" />
        </template>
      </Button>
    </OverlayBadge>

    <Popover ref="op" class="w-80">
      <div v-if="loading" class="flex justify-center p-4">
        <i class="pi pi-spin pi-spinner text-2xl" />
      </div>
      <div v-else>
        <TabView>
          <TabPanel>
            <template #header>
              <span class="flex items-center gap-2">
                未读消息
                <Badge v-if="unreadMessageCount > 0" :value="unreadMessageCount" severity="danger" size="small" />
              </span>
            </template>
            <div class="space-y-3">
              <div v-for="item in messageList" :key="item.messageId" class="flex items-start gap-3 p-2 hover:bg-gray-50 rounded cursor-pointer" @click="showMessageDetail(item)">
                <div class="w-2 h-2 bg-red-500 rounded-full mt-2 flex-shrink-0" />
                <div class="flex-1 min-w-0">
                  <div class="text-sm font-medium text-gray-900 truncate">
                    {{ item.title }}
                  </div>
                  <div class="text-xs text-gray-500">
                    {{ timeago(item.createTime) }}
                  </div>
                </div>
              </div>
              <div v-if="unreadMessageCount > 3" class="text-center pt-2">
                <Button variant="text" size="small" @click="gotoMessage">
                  查看更多
                </Button>
              </div>
              <div v-if="messageList.length === 0" class="text-center py-4 text-gray-500">
                暂无未读消息
              </div>
            </div>
          </TabPanel>
          <TabPanel>
            <template #header>
              <span class="flex items-center gap-2">
                待办工作
                <Badge v-if="toBeDoneCount > 0" :value="toBeDoneCount" severity="danger" size="small" />
              </span>
            </template>
            <div class="space-y-3">
              <div v-for="(item, index) in toBeDoneList" :key="index" class="flex items-start gap-3 p-2 hover:bg-gray-50 rounded">
                <div class="w-2 h-2 bg-red-500 rounded-full mt-2 flex-shrink-0" />
                <div class="flex-1 min-w-0">
                  <div class="flex items-center gap-2 mb-1">
                    <Tag v-if="item.starFlag" severity="danger" class="text-xs px-1 py-0.5">
                      重要
                    </Tag>
                  </div>
                  <div class="text-sm text-gray-900">
                    {{ item.title }}
                  </div>
                </div>
              </div>
              <div v-if="toBeDoneList.length === 0" class="text-center py-4 text-gray-500">
                暂无待办
              </div>
            </div>
          </TabPanel>
        </TabView>
      </div>
    </Popover>
    <MessageDetailModal ref="messageDetailModalRef" @refresh="queryMessage" />
  </div>
</template>
