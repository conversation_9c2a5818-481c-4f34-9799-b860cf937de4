<!--
  * 标签页 - 重构为PrimeVue实现
-->
<script setup lang="ts">
import { HOME_PAGE_NAME } from '@/constants/system/home-const'
import { usePageTab } from '../composables/usePageTab'

const { pageTagFlag, tagNav, selectedKey, selectTab, closeTag, handleContextMenu } = usePageTab()
</script>

<template>
  <!-- 标签页，共两部分：1、标签 ；2、标签操作区 -->
  <div v-show="pageTagFlag" class="w-full flex border-b border-gray-200 relative bg-white shadow-sm">
    <div
      class="w-full"
      @contextmenu="handleContextMenu"
    >
      <BaseTabs
        :value="selectedKey as string"
        class="w-full"
        scrollable
        @update:value="selectTab"
      >
        <BaseTabList class="flex">
          <BaseTab
            v-for="item in tagNav"
            :key="item.menuTitle"
            :value="item.menuName"
            class="smart-page-tag-item"
          >
            <span class="flex items-center">
              <Icon
                v-if="item.menuName === HOME_PAGE_NAME"
                icon-type="HomeOutlined"
                class="mr-1 text-xs flex-shrink-0"
              />
              <span class="tab-title">{{ item.menuTitle }}</span>
              <Icon
                v-if="item.menuName !== HOME_PAGE_NAME"
                icon-type="CloseOutlined"
                class="smart-page-tag-close ml-1 text-xs text-gray-500 hover:text-blue-500 cursor-pointer flex-shrink-0"
                @click.stop="closeTag(item, false)"
              />
            </span>
          </BaseTab>
        </BaseTabList>
      </BaseTabs>
    </div>
  </div>
</template>

<style scoped>
.smart-page-tag-item {
  padding: 5px 8px 3px 15px;
  margin: 8px 0 0 5px;
  min-width: 60px;
  height: 32px;
  border-radius: 6px 6px 0 0;
  border-bottom: 0;
}

.smart-page-tag-close {
  font-size: 10px;
}

/* 覆盖 PrimeVue Tabs 的默认样式 */
:deep(.base-tabs .p-tabs-nav) {
  margin: 0;
  border-bottom: none;
}

:deep(.base-tabs .p-tabs-header) {
  border-bottom: none;
}

:deep(.base-tab) {
  background: #f8f9fa;
  border: 1px solid #e5e7eb;
  margin-right: 2px;
}

:deep(.base-tab.p-tab-active) {
  background: white;
  border-bottom-color: white;
}

:deep(.base-tab:hover:not(.p-tab-active)) {
  background: white;
}

.tab-title {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-weight: normal;
  padding: 0 4px;
}
</style>
