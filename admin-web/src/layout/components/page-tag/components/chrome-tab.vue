<!--
  * chrome样式 - 简化版PrimeVue实现
-->
<script setup lang="ts">
import { HOME_PAGE_NAME } from '@/constants/system/home-const'
import { usePageTab } from '../composables/usePageTab'

const { pageTagFlag, tagNav, selectedKey, selectTab, closeTag, handleContextMenu } = usePageTab()
</script>

<template>
  <!-- 标签页，共两部分：1、标签 ；2、标签操作区 -->
  <div v-show="pageTagFlag" class="w-full flex border-b border-gray-200 bg-white min-h-12">
    <div
      class="w-full flex items-center px-4 overflow-x-auto"
      @contextmenu="handleContextMenu"
    >
      <BaseTabs
        :value="selectedKey as string"
        class="w-full"
        scrollable
        @update:value="selectTab"
      >
        <BaseTabList class="flex">
          <BaseTab
            v-for="item in tagNav"
            :key="item.menuTitle"
            :value="item.menuName"
            class="relative px-4 py-2 mx-1 rounded-t-lg border border-b-0 bg-gray-50 hover:bg-blue-50 cursor-pointer transition-colors duration-200"
            :class="{
              'bg-blue-100 border-blue-300 text-blue-700': selectedKey === item.menuName,
              'border-gray-300': selectedKey !== item.menuName,
            }"
          >
            <div class="flex items-center space-x-2 min-w-0">
              <Icon
                v-if="item.menuName === HOME_PAGE_NAME"
                icon-type="HomeOutlined"
                class="text-sm flex-shrink-0"
              />
              <span class="text-sm truncate">{{ item.menuTitle }}</span>
              <Icon
                v-if="item.menuName !== HOME_PAGE_NAME"
                icon-type="CloseOutlined"
                class="text-xs text-gray-500 hover:text-red-500 flex-shrink-0 ml-1"
                @click.stop="closeTag(item, false)"
              />
            </div>
          </BaseTab>
        </BaseTabList>
      </BaseTabs>
    </div>
  </div>
</template>
