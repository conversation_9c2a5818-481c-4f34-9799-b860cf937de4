# ERP业务CRUD开发规范

## 概述

本规范基于ax-admin系统中供应商模块的最佳实践，定义了ERP业务CRUD模块的标准架构、开发模式和代码规范。

## 目录结构规范

### 标准目录结构
```
{ModuleName}/
├── {ModuleName}List.vue          # 主列表页面（必需）
├── components/                   # 组件目录（必需）
│   ├── {ModuleName}Detail.vue   # 详情组件（必需）
│   └── {ModuleName}Form.vue     # 表单组件（必需）
├── config/                      # 配置目录（必需）
│   ├── columns.ts               # 表格列配置（必需）
│   └── rule.ts                  # 表单验证规则（必需）
└── service/                     # 服务目录（必需）
    └── {ModuleName}Service.ts   # 业务服务（必需）
```

### 命名约定
- **文件名**：使用PascalCase，如 `SupplierList.vue`
- **组件名**：与文件名保持一致
- **服务类**：以Service后缀，如 `SupplierService`
- **配置文件**：小写，如 `columns.ts`、`rule.ts`

## 架构设计模式

### 1. 分层架构
```
┌─────────────────┐
│   视图层 (View)   │  List.vue, Detail.vue, Form.vue
├─────────────────┤
│  组件层 (Component) │  可复用的业务组件
├─────────────────┤
│  服务层 (Service)  │  CrudService继承，业务逻辑
├─────────────────┤
│  配置层 (Config)   │  columns, rules, utils
├─────────────────┤
│   API层 (API)    │  HTTP请求封装
└─────────────────┘
```

### 2. 依赖注入模式
```typescript
// 服务提供者（List.vue）
import { moduleService } from './service/ModuleService'
moduleService.provide()

// 服务消费者（Detail.vue, Form.vue）
const moduleService = inject<ModuleService>(CRUD_KEY)!
```

### 3. 组件导入规则
- ✅ **自动导入**：`@/components/base/`、`@/components/utils/`等基础组件
- ✅ **手动导入**：业务组件（同级目录下的.vue组件）和本地.ts文件
- ❌ **禁止导入**：PrimeVue原生组件（使用封装版本）

#### 导入规则说明
```vue
<script setup lang="ts">
// ✅ 手动导入：业务组件
import ModuleDetail from './components/ModuleDetail.vue'
import ModuleForm from './components/ModuleForm.vue'

// ✅ 手动导入：本地TypeScript文件

// ✅ 自动导入：基础组件（无需手动导入）
// import Button from '@/components/base/Button' // 不需要
// import Dialog from '@/components/base/Dialog' // 不需要
</script>

<template>
  <!-- 基础组件自动导入 -->
  <Button>提交</Button>
  <Dialog>内容</Dialog>

  <!-- 业务组件必须手动导入 -->
  <ModuleDetail />
  <ModuleForm />
</template>
```

## 文件开发规范

### 1. 主列表页面 (`{ModuleName}List.vue`)

#### 基本结构
```vue
<script setup lang="ts">
// 1. 导入区域
import ModuleDetail from './components/ModuleDetail.vue'
import ModuleForm from './components/ModuleForm.vue'
import { moduleService } from './service/ModuleService'

// 2. 服务初始化
moduleService.provide()
</script>

<template>
  <!---------- 查询参数 begin ----------->
  <Card>
    <Form :ref="moduleService.tableFormRef" v-privilege="'module:query'">
      <Grid>
        <GridCol>
          <FormItem label="字段名称" name="fieldName">
            <InputText allow-clear />
          </FormItem>
        </GridCol>
        <!-- 更多查询表单项 -->
      </Grid>
    </Form>
  </Card>
  <!---------- 查询参数 end ----------->

  <Card :hoverable="true">
    <!---------- 表格操作行 begin ----------->
    <FlexRow>
      <FlexRow>
        <AxTableOperateButtons add-config="module:add" batch-delete-config="module:delete" import-config="module:import" export-config="module:export" />
      </FlexRow>
      <ButtonGroup>
        <IconButton label="查询" icon-type="SearchOutlined" type="primary" @click="moduleService.onSearch" />
        <IconButton label="重置" icon-type="ReloadOutlined" @click="moduleService.resetQuery" />
      </ButtonGroup>
    </FlexRow>
    <!---------- 表格操作行 end ----------->
    <br>
    <!-- 使用组件化的表格 -->
    <AxTable min-width="180rem">
      <template #bodyCell="{ column, record }">
        <!-- 只为操作列添加模板插槽 -->
        <template v-if="column.dataIndex === 'action'">
          <FlexRow justify="start">
            <IconAnchor
              v-privilege="'module:view'" label="查看" icon-type="EyeOutlined"
              @click="moduleService.openDetailView(record.id)"
            />
            <IconAnchor
              v-privilege="'module:edit'" label="编辑" icon-type="EditOutlined"
              @click="moduleService.openEditForm(record.id)"
            />
            <IconAnchor
              v-privilege="'module:delete'" label="删除" icon-type="DeleteOutlined" color="red"
              @click="moduleService.deleteEntity(record.id)"
            />
          </FlexRow>
        </template>
      </template>
    </AxTable>
  </Card>

  <!-- 导入弹窗 -->
  <AxImportExcelModal />
  <!-- 弹窗组件 -->
  <ModuleDetail />
  <ModuleForm />
</template>
```

#### 关键规范
- **查询区域**：使用`Grid`组件，默认4列布局，表单项使用`FormItem`包装并设置`name`属性
- **表格区域**：固定模板结构，包含操作行和表格两部分
  - 操作行：左侧功能按钮(`AxTableOperateButtons`)，右侧查询/重置按钮(`ButtonGroup`)
  - 表格：使用`AxTable`，设置`min-width="150rem"`
- **操作列**：统一使用`IconAnchor`组件，调用service的标准方法
- **权限控制**：使用`v-privilege`指令控制按钮显示
- **导入功能**：使用`AxImportExcelModal`组件

### 2. 详情组件 (`{ModuleName}Detail.vue`)

#### 基本结构
```vue
<script setup lang="ts">
import type { ModuleService } from '../service/ModuleService'
import { CRUD_KEY } from '@/service/composite/TableCrudService'

// 获取服务实例
const moduleService = inject<ModuleService>(CRUD_KEY)!

// 当前激活的标签页
const activeTab = ref('basic')

// 详情页无需错误状态计算
</script>

<template>
  <Dialog v-model:visible="moduleService.detailOpen" :header="moduleService.detailTitle" :show-footer="false" width="1200px">
    <!-- 详情分页容器 -->
    <BaseTabs v-model:value="activeTab">
      <!-- 标签页导航（无错误指示） -->
      <BaseTabList>
        <BaseTab value="basic">
          📋 基本信息
        </BaseTab>
        <BaseTab value="contact">
          📞 联系信息
        </BaseTab>
        <BaseTab value="business">
          🏢 企业信息
        </BaseTab>
        <BaseTab value="system">
          ⚙️ 系统信息
        </BaseTab>
      </BaseTabList>

      <!-- 标签页内容 -->
      <BaseTabPanels>
        <!-- 基本信息 -->
        <BaseTabsPanel value="basic">
          <Descriptions :column="2" :colon="false" bordered>
            <DescriptionsItem label="编码">
              <Text :value="moduleService.detailData.code" />
            </DescriptionsItem>
            <DescriptionsItem label="名称">
              <Text :value="moduleService.detailData.name" />
            </DescriptionsItem>
            <!-- 更多基本信息字段 -->
          </Descriptions>
        </BaseTabsPanel>
        <!-- 更多面板 -->
      </BaseTabPanels>
    </BaseTabs>
  </Dialog>
</template>
```

#### 关键规范
- **Script部分**：
  - 获取服务实例：`inject<ModuleService>(CRUD_KEY)!`
  - 添加当前激活标签页：`const activeTab = ref('basic')`
  - 添加注释："详情页无需错误状态计算"
- **Template部分**：
  - **Dialog属性**：`:visible`、`:title`、`:show-footer="false"`、`width="1200px"`、`@cancel`
  - **组件使用**：使用`BaseTab{xxx}`组件（不是`Tab{xxx}`）
  - **数据绑定**：使用`moduleService.detailData.fieldName`（不是computed record）
  - **文本显示**：使用`Text`组件（不是`CustomText`）
  - **Descriptions属性**：`:column="2"`、`:colon="false"`、`bordered`
- **标签页组织**：建议不超过6个标签，使用emoji图标
- **字段显示**：时间字段使用`formatDateTime`格式化，字典字段使用`DictTag`

### 3. 表单组件 (`{ModuleName}Form.vue`)

#### 基本结构
```vue
<script setup lang="ts">
import type { ModuleService } from '../service/ModuleService'
import { CRUD_KEY } from '@/service/composite/TableCrudService'
// 表单验证规则
import { rules } from '../config/rule'

// 获取服务实例
const moduleService = inject<ModuleService>(CRUD_KEY)!
// 当前激活的标签页
const activeTab = ref('basic')
</script>

<template>
  <Dialog
    v-model:visible="moduleService.formOpen" :header="moduleService.formTitle"
  >
    <Form
      :ref="moduleService.formRef" :form-data="moduleService.formData" :rules="rules"
    >
      <!-- 标签页导航 -->
      <BaseTabs v-model:value="activeTab">
        <BaseTabList>
          <BaseTab value="basic">
            📋 基本信息
          </BaseTab>
          <BaseTab value="contact">
            📞 联系信息
          </BaseTab>
          <!-- 更多标签页 -->
        </BaseTabList>

        <BaseTabPanels>
          <!-- 基本信息标签页 -->
          <BaseTabsPanel value="basic">
            <Grid :cols="2" :responsive="false">
              <GridCol>
                <FormItem label="编码 (自动生成)" name="code">
                  <InputText disabled />
                </FormItem>
              </GridCol>
              <GridCol>
                <FormItem label="名称" name="name">
                  <InputText placeholder="请输入名称" allow-clear />
                </FormItem>
              </GridCol>
              <!-- 更多表单项 -->
            </Grid>
          </BaseTabsPanel>
          <!-- 更多面板 -->
        </BaseTabPanels>
      </BaseTabs>
    </Form>

    <template #footer>
      <Button label="取消" severity="danger" @click="moduleService.closeForm()" />
      <Button label="保存" @click="moduleService.submitFormAndRefresh()" />
    </template>
  </Dialog>
</template>
```

#### 关键规范
- **Script部分**：
  - inject对应的service
  - 创建表单引用和当前激活标签页ref
  - 创建检查标签页验证错误的动态值
  - 实现表单提交方法
- **Template部分**：
  - 使用`BaseTab{xxx}`标签组件（不是`Tab{xxx}`）
  - Dialog只传三个属性：`visible`、`@confirm`和`@cancel`
  - 无footer部分（由Dialog的confirm/cancel处理）
  - 表单使用2列`Grid`布局，设置`:responsive="false"`
- **自动生成字段处理**：
  - Label格式：`编码 (自动生成)`
  - 输入框：`<InputText disabled />`（无条件禁用）
  - 无placeholder、allow-clear等多余属性

### 4. 表格列配置 (`columns.ts`)

#### 基本结构
```typescript
import type { ModuleResult } from '@/api/business/module/model/module-ex'
import type { DictColor } from '@/components/utils/Dict/type'
import type { TableColumn } from '@/service/base/interface/Table'
import { CustomText } from '@/components/base/CustomText'
import { DictTag } from '@/components/utils/Dict'
import { h } from 'vue'

// 获取状态对应的颜色
export function getStatusColor(status?: string): DictColor {
  switch (status) {
    case 'active':
      return 'success'
    case 'inactive':
      return 'warning'
    case 'deleted':
      return 'error'
    default:
      return 'default'
  }
}

// 列定义
export const columns: TableColumn<ModuleResult>[] = [
  {
    title: '字段名称',
    dataIndex: 'fieldName',
    width: 120,
    customRender: ({ record }: { record: ModuleResult }) => {
      return h(CustomText, {
        value: record.fieldName,
        copyable: true
      })
    }
  },
  {
    title: '状态',
    dataIndex: 'status',
    width: 100,
    customRender: ({ record }: { record: ModuleResult }) => {
      return h(DictTag, {
        color: getStatusColor(record.status),
        keyCode: 'status_type',
        valueCode: record.status
      })
    }
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    width: 160,
    sorter: true
  },
  {
    title: '操作',
    dataIndex: 'action',
    fixed: 'right',
    width: 200
  }
]
```

#### 关键规范
- **必须手动导入组件**：CustomText、DictTag等customRender中使用的组件需要手动导入
- **导入Record类型**：customRender参数需要正确的类型注解`{ record: ModuleResult }`
- **导入DictColor类型**：颜色函数返回类型使用`DictColor`
- 所有列必须设置`width`属性
- 使用`h`函数创建组件渲染
- 字典字段统一使用`DictTag`组件，并传入`color`属性
- 状态字段提供颜色映射函数，返回`DictColor`类型
- 长文本使用`CustomText`组件，属性名为`value`而不是`text`
- 长文本列设置`ellipsis: true`
- 创建时间列设置`sorter: true`
- 操作列设置`fixed: 'right'`

### 5. 验证规则 (`rule.ts`)

#### 基本结构
```typescript
import type { Rules } from '@/components/base/Form/type'

export const rules: Rules = {
  // 必填字段（basic标签页）- 只有必填字段才添加到rules中
  name: [
    { type: 'string', required: true, message: '请输入名称', trigger: 'blur' },
    { max: 100, message: '名称长度不能超过100个字符', trigger: 'blur' }
  ],
  type: [
    { type: 'string', required: true, message: '请选择类型', trigger: 'change' }
  ],
  status: [
    { type: 'string', required: true, message: '请选择状态', trigger: 'change' }
  ]
}

// 重要说明：
// 1. 🚨 选填字段不要放入rules中（会自动变成必填项）
// 2. 只有必填字段才添加到rules，并设置required: true
// 3. id、createTime、updateTime等自动生成字段不需要验证规则
// 4. 每个字段都必须指定type属性，类型参考对应的Model定义
```

#### 关键规范
- **🚨 核心原则**：
  - **选填字段不要放入rules中**（会自动变成必填项）
  - **只有必填字段才添加到rules中**，并设置`required: true`
  - 每个字段都必须指定`type`属性，类型参考对应的Model定义
  - 必填字段仅限于`basic`标签页
- **触发时机**：`blur`（失焦）或`change`（变化）
- **字段排除**：`id`、`createTime`、`updateTime`等自动生成字段无需验证规则

#### 表单分组验证策略
```typescript
// ✅ 正确：只添加必填字段到rules中
const correctRules = {
  name: [
    { type: 'string', required: true, message: '请输入名称', trigger: 'blur' }
  ],
  type: [
    { type: 'string', required: true, message: '请选择类型', trigger: 'change' }
  ],
  status: [
    { type: 'string', required: true, message: '请选择状态', trigger: 'change' }
  ]
}

// ❌ 错误：不要为选填字段添加rules（会变成必填项）
const wrongRules = {
  creditLevel: [], // 错误：选填字段不应添加
  contactPhone: [{ type: 'string' }], // 错误：选填字段不应添加
  contactEmail: [{ type: 'string' }], // 错误：选填字段不应添加
  businessLicense: [{ type: 'string' }], // 错误：选填字段不应添加
  remark: [{ type: 'string' }], // 错误：选填字段不应添加
  id: [{ required: true }], // 错误：ID由后端生成
  createTime: [{ required: true }] // 错误：创建时间由系统生成
}
```

### 6. 业务服务 (`{ModuleName}Service.ts`)

#### 基本结构
```typescript
import type { ModuleForm, ModulePageParam, ModuleResult } from '@/api/business/module/model/module-ex'
import { moduleApi } from '@/api/business/module/module-api'
import { TableCrudService as CrudService } from '@/service/composite/TableCrudService'
import { columns } from '../config/columns'

/**
 * 模块服务
 * 提供模块相关的业务逻辑和数据管理
 */
export class ModuleService extends CrudService<ModuleResult, ModuleForm, ModulePageParam> {
  constructor() {
    // 初始化服务
    super(
      '模块', // 业务名称
      columns,
      {
        // 使用已有的API
        add: moduleApi.addModule,
        queryPage: moduleApi.modulePage,
        getDetail: moduleApi.moduleDetail,
        update: moduleApi.updateModule,
        import: moduleApi.importModule,
        export: moduleApi.exportModule,
        delete: moduleApi.deleteModule,
        batchDelete: moduleApi.batchDeleteModule,
      }
    )
  }
}

// 单例模式
export const moduleService = new ModuleService()
```

#### 关键规范
- 继承`CrudService`基类
- 泛型参数：`<ResultType, FormType, QueryType>`
- 构造函数传入：业务名称、列配置、API对象映射
- API对象映射包含：add、queryPage、getDetail、update、import、export、delete、batchDelete
- 导出单例实例，不导出类
- **不要添加自定义业务方法**：CrudService已提供完整功能，避免过度设计

## 数据流规范

### 1. 标准CRUD流程
```
查询: List组件 → Service.queryList() → API → 更新table状态
新增: List组件 → Service.openAddView() → 显示Form弹窗
编辑: List组件 → Service.openEditView(id) → 获取详情 → 显示Form弹窗
详情: List组件 → Service.openDetailView(id) → 获取详情 → 显示Detail弹窗
删除: List组件 → Service.deleteRecord(id) → API → 刷新列表
```

### 2. 表单提交流程差异

#### List页面查询表单提交流程
```
用户输入查询条件 → 点击查询按钮 → Service.onSearch()
    ↓
页面显示筛选结果（不关闭表单，不修改数据库）
```

#### Form组件数据表单提交流程
```
用户填写表单数据 → 点击确定按钮 → Service.submitFormAndRefresh()
    ↓
关闭弹窗 → 刷新列表 → 显示最新数据
```

#### 核心差异对比

| 对比项 | List页面查询表单 | Form组件数据表单 |
|-------|----------------|-----------------|
| **表单用途** | 筛选查询数据 | 新增/编辑数据 |
| **最终操作** | `onSearch()` | `submitFormAndRefresh()` |
| **是否修改数据库** | ❌ 否 | ✅ 是 |
| **提交后行为** | 更新表格显示 | 关闭弹窗+刷新列表 |
| **表单保持状态** | ✅ 保持打开 | ❌ 自动关闭 |
| **加载状态** | `queryForm.loading` | `form.loading` |

### 3. 服务状态管理
```typescript
// CrudService提供的标准状态
{
  // 表格状态
  table: {
    data: [], // 数据数组
    loading: false, // 加载状态
    pagination: {}, // 分页信息
    selection: [] // 选中项
  },

  // 查询状态
  queryForm: {
    model: {}, // 查询参数
    loading: false // 查询加载状态
  },

  // 表单状态
  form: {
    modal: { visible: false }, // 弹窗状态
    model: {}, // 表单数据
    loading: false, // 提交加载状态
    rules: {} // 验证规则
  },

  // 详情状态
  detail: {
    modal: { visible: false }, // 弹窗状态
    record: {}, // 详情数据
    loading: false // 加载状态
  }
}
```

## 样式规范

### 1. 布局规范
- **查询表单**：使用`Grid`组件，默认4列响应式布局
- **表单页面**：使用`Grid`组件，默认2列布局
- **按钮组**：使用`flex`布局，间距`gap-2`
- **卡片间距**：上下卡片间距`mb-4`

### 2. 组件规范
- **表格**：最小宽度`min-width="150rem"`
- **弹窗**：宽度根据内容自适应，最大宽度`80vw`
- **表单项**：统一使用`FormItem`包装
- **按钮**：主要按钮在右，次要按钮在左

### 3. 响应式规范
```vue
<!-- 查询表单：4列响应式 -->
<Grid :cols="4" :gap="4">

<!-- 表单页面：2列响应式 -->
<Grid :cols="2" :gap="4">

<!-- 移动端适配 -->
<Grid :cols="{ xs: 1, sm: 2, md: 3, lg: 4 }" :gap="4">
```

## 权限控制规范

### 1. 权限指令使用
```vue
<!-- 查看权限 -->
<IconAnchor v-privilege="'module:view'" />

<!-- 编辑权限 -->
<IconAnchor v-privilege="'module:edit'" />

<!-- 删除权限 -->
<IconAnchor v-privilege="'module:delete'" />

<!-- 新增权限 -->
<Button v-privilege="'module:add'">
新增
</Button>
```

### 2. 权限命名规范
- 模块权限：`{module}:{action}`
- 基础动作：`view`、`add`、`edit`、`delete`
- 扩展动作：`export`、`import`、`audit`、`approve`

## 错误处理规范

### 1. 表单验证错误
```typescript
// 标签页错误指示
const tabErrors = computed(() => ({
  basic: hasFieldErrors(['name', 'code']),
  contact: hasFieldErrors(['phone', 'email'])
}))

// 字段错误检查
function hasFieldErrors(fields: string[]): boolean {
  return fields.some(field =>
    form.errors?.[field] && form.errors[field].length > 0
  )
}
```

### 2. 网络请求错误
```typescript
// CrudService自动处理以下错误：
// - 网络连接错误
// - 服务器错误
// - 权限错误
// - 参数验证错误

// 自定义错误处理
try {
  await customBusinessMethod()
}
catch (error) {
  ElMessage.error('操作失败，请重试')
  console.error('Business error:', error)
}
```

## 性能优化规范

### 1. 组件懒加载
```typescript
// 大型表单组件使用异步加载
const ModuleForm = defineAsyncComponent(() =>
  import('./components/ModuleForm.vue')
)
```

### 2. 计算属性优化
```typescript
// 使用计算属性缓存复杂计算
const filteredData = computed(() =>
  tableData.value.filter(item => item.status === 'active')
)
```

### 3. 表格虚拟化
```vue
<!-- 大数据量表格启用虚拟滚动 -->
<AxTable virtual :scroll="{ y: 400 }" />
```

## 代码质量规范

### 1. TypeScript规范
- 所有API接口必须有类型定义
- 使用泛型提高代码复用性
- 避免使用`any`类型
- 导入类型使用`type`关键字

### 2. ESLint规范
- 遵循`@antfu/eslint-config`规则
- 使用`defineEmits`和`defineProps`
- 组件名使用PascalCase
- 函数使用camelCase

### 3. 注释规范
```typescript
/**
 * 获取模块详情
 * @param id 模块ID
 * @returns 模块详情数据
 */
async function getModuleDetail(id: number): Promise<ModuleResult> {
  // 实现逻辑
}
```

## 未知对象数组字段处理规范

### ⚠️ 重要规则：`Record<string, unknown>[]` 类型字段处理

当模型中存在 `Record<string, unknown>[]` 类型的字段时，**不要生成任何相关的UI代码**：

```typescript
// ❌ 这些字段不要生成UI代码
addressInfoList?: Record<string, unknown>[] // 地址信息列表(jsonb数组)
paymentInfoList?: Record<string, unknown>[] // 支付账户信息列表(jsonb数组)
configData?: Record<string, unknown>       // 配置数据
extendedFields?: Record<string, unknown>[] // 扩展字段
```

### 处理原则

1. **跳过未知结构字段**：不为 `Record<string, unknown>[]` 类型生成任何UI组件
2. **避免错误假设**：不要假设未知对象的内部结构
3. **保持代码简洁**：专注于已知字段的UI实现
4. **后期扩展**：等明确字段结构后再添加相应UI

### 正确做法示例

```vue
<!-- ✅ 正确：只处理已知字段 -->
<BaseTab value="basic">
📋 基本信息
</BaseTab>

<BaseTab value="contact">
📞 联系信息
</BaseTab>

<BaseTab value="business">
🏢 企业信息
</BaseTab>

<!-- ❌ 错误：不要为未知字段生成UI -->
<!-- <BaseTab value="address">📍 地址信息</BaseTab> -->
<!-- <BaseTab value="payment">💳 支付信息</BaseTab> -->
```

### 标签页数量建议

- **最少3个标签页**：基本信息、联系信息、其他信息
- **最多6个标签页**：避免界面过于复杂
- **只包含已知字段**：确保每个字段都有明确的类型定义

## 最佳实践指导

### 表单提交最佳实践

#### 1. 明确区分表单类型
```typescript
// ❌ 错误：混用查询和数据表单逻辑
async function handleSubmit() {
  // 不明确是查询还是数据提交
  await someService.submit()
}

// ✅ 正确：明确区分查询表单和数据表单
// 查询表单
async function handleQuerySubmit() {
  if (formRef.value) {
    const res = await formRef.value.submit()
    moduleService.updateQueryParam(res.values)
  }
  moduleService.onSearch()
}

// 数据表单
async function handleDataSubmit() {
  if (formRef.value) {
    const res = await formRef.value.submit()
    moduleService.updateFormData(res.values)
  }
  await moduleService.submitFormAndRefresh()
}
```

#### 2. 正确的表单引用类型
```typescript
// List页面查询表单
const queryFormRef = ref<FormExpose<ModuleQueryParam>>()

// Form组件数据表单
const dataFormRef = ref<FormExpose<ModuleForm>>()
```

#### 3. 统一的错误处理
```typescript
async function handleSubmit() {
  try {
    if (formRef.value) {
      const res = await formRef.value.submit()
      // 验证成功后的逻辑
      moduleService.updateFormData(res.values)
    }
    await moduleService.submitFormAndRefresh()
  }
  catch (error) {
    // 表单验证失败时，formRef.submit() 会抛出异常
    console.log('表单验证失败:', error)
  }
}
```

#### 4. 加载状态管理
```vue
<!-- 查询按钮显示查询加载状态 -->
<Button type="submit" :loading="moduleService.queryForm.loading">
  查询
</Button>

<!-- 数据提交按钮显示表单加载状态 -->
<Button type="submit" :loading="moduleService.form.loading">
  确定
</Button>
```

### 表单验证最佳实践

#### 1. 验证规则分层原则
```typescript
// ✅ 正确：基础信息必填，其他信息选填
export const rules: Rules = {
  // basic标签页 - 必填字段
  supplierName: [
    { type: 'string', required: true, message: '请输入供应商名称', trigger: 'blur' },
    { max: 100, message: '供应商名称长度不能超过100个字符', trigger: 'blur' }
  ],
  supplierType: [
    { type: 'string', required: true, message: '请选择供应商类型', trigger: 'change' }
  ],
  status: [
    { type: 'string', required: true, message: '请选择状态', trigger: 'change' }
  ],

  // 其他标签页 - 选填字段（仅格式验证）
  contactPhone: [
    { type: 'string', pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ],
  contactEmail: [
    { type: 'string', pattern: /\S[^\s@]*@\S+\.\S+/, message: '请输入正确的邮箱地址', trigger: 'blur' }
  ]

  // 注意：supplierCode、id等自动生成字段不设置验证规则
}
```

#### 2. 标签页错误指示策略
```vue
<template>
  <BaseTabs>
    <BaseTabList>
      <!-- 只有basic标签页显示错误指示器 -->
      <BaseTab value="basic">
        📋 基本信息
      </BaseTab>

      <!-- 其他标签页不显示错误指示器（无必填字段） -->
      <BaseTab value="contact">
        📞 联系信息
      </BaseTab>

      <BaseTab value="financial">
        💰 财务信息
      </BaseTab>
    </BaseTabList>
  </BaseTabs>
</template>
```

#### 3. 自动生成字段处理
```vue
<template>
  <!-- ✅ 正确：自动生成字段设置为disabled -->
  <FormItem label="供应商编码 (自动生成)" name="supplierCode">
    <InputText disabled />
  </FormItem>

  <!-- ✅ 正确：ID字段通常隐藏，不在表单中显示 -->
  <!-- <FormItem label="ID" name="id"> 不需要显示 -->
</template>
```

### 开发流程建议

1. **先理解业务需求**：明确是查询筛选还是数据操作
2. **确定表单结构**：基础信息放在basic标签页，设置为必填
3. **配置验证规则**：只对basic标签页字段设置required，其他仅格式验证
4. **排除自动字段**：ID、编码等后端生成字段不设置验证规则
5. **处理自动生成字段**：Label添加"(自动生成)"，输入框设置为`disabled`
6. **修正Detail组件**：使用`BaseTab{xxx}`组件，Dialog属性参考supplier模板，数据绑定使用`service.detailData`
7. **选择正确模板**：查询用List页面模板，数据操作用Form组件模板
8. **配置正确类型**：使用对应的TypeScript类型定义
9. **测试完整流程**：验证表单验证、提交、响应等完整流程
10. **检查用户体验**：确保加载状态、错误提示、成功反馈正确显示

## 菜单配置规范

### 1. 菜单SQL生成规范

完成前端页面开发后，需要在 `.sql/menu/` 目录下创建对应的菜单SQL文件，用于在数据库中创建菜单项和权限配置。

#### 菜单文件命名规范
```
.sql/menu/{模块中文名}.sql
```

例如：
- `供应商管理.sql`

#### 标准菜单SQL模板

```sql
-- 删除重复的{模块名称}菜单数据
DELETE FROM t_menu WHERE menu_name = '{模块中文名}' AND deleted_flag = FALSE;
DELETE FROM t_menu WHERE api_perms LIKE '{moduleCode}:%' AND deleted_flag = FALSE;

-- {模块名称}菜单
-- 插入{模块名称}主菜单并获取ID
WITH {moduleCode}_menu AS (
    INSERT INTO t_menu (menu_name, menu_type, parent_id, path, component, perms_type, visible_flag, disabled_flag, deleted_flag, create_user_id)
    VALUES ('{模块中文名}', 2, 0, '/{module-path}/list', '/business/erp/{module-path}/{ModuleName}List.vue', 1, TRUE, FALSE, FALSE, 1)
    RETURNING menu_id
)
-- 批量插入{模块名称}权限菜单
INSERT INTO t_menu (menu_name, menu_type, parent_id, perms_type, api_perms, context_menu_id, visible_flag, disabled_flag, deleted_flag, create_user_id)
SELECT
    unnest(ARRAY['查询', '新增', '编辑', '删除', '导入', '导出']) as menu_name,
    3 as menu_type,
    {moduleCode}_menu.menu_id as parent_id,
    1 as perms_type,
    unnest(ARRAY['{moduleCode}:query', '{moduleCode}:add', '{moduleCode}:update', '{moduleCode}:delete', '{moduleCode}:import', '{moduleCode}:export']) as api_perms,
    260 as context_menu_id,
    TRUE as visible_flag,
    FALSE as disabled_flag,
    FALSE as deleted_flag,
    1 as create_user_id
FROM {moduleCode}_menu;
```

### 2. 菜单配置参数说明

#### 主菜单配置
| 参数 | 说明 | 示例值 |
|------|------|--------|
| `menu_name` | 菜单显示名称 | `'供应商管理'` |
| `menu_type` | 菜单类型：2=页面菜单 | `2` |
| `parent_id` | 父菜单ID：0=顶级菜单 | `0` |
| `path` | 前端路由路径 | `'/supplier/list'` |
| `component` | Vue组件路径 | `'/business/erp/supplier/SupplierList.vue'` |
| `perms_type` | 权限类型：1=API权限 | `1` |
| `visible_flag` | 是否可见：TRUE=可见 | `TRUE` |
| `disabled_flag` | 是否禁用：FALSE=启用 | `FALSE` |
| `deleted_flag` | 是否删除：FALSE=未删除 | `FALSE` |
| `create_user_id` | 创建用户ID | `1` |

#### 权限菜单配置
| 参数 | 说明 | 示例值 |
|------|------|--------|
| `menu_name` | 权限名称 | `'查询'`, `'新增'`, `'编辑'`, `'删除'`, `'导入'`, `'导出'` |
| `menu_type` | 菜单类型：3=权限菜单 | `3` |
| `parent_id` | 父菜单ID（主菜单ID） | `动态获取` |
| `api_perms` | API权限标识 | `'supplier:query'`, `'supplier:add'` |
| `context_menu_id` | 上下文菜单ID | `260` |

### 3. 实际配置示例

#### 供应商管理菜单配置
```sql
-- 删除重复的供应商管理菜单数据
DELETE FROM t_menu WHERE menu_name = '供应商管理' AND deleted_flag = FALSE;
DELETE FROM t_menu WHERE api_perms LIKE 'supplier:%' AND deleted_flag = FALSE;

-- 供应商管理菜单
-- 插入供应商管理主菜单并获取ID
WITH supplier_menu AS (
    INSERT INTO t_menu (menu_name, menu_type, parent_id, path, component, perms_type, visible_flag, disabled_flag, deleted_flag, create_user_id)
    VALUES ('供应商管理', 2, 0, '/supplier/list', '/business/erp/supplier/SupplierList.vue', 1, TRUE, FALSE, FALSE, 1)
    RETURNING menu_id
)
-- 批量插入供应商管理权限菜单
INSERT INTO t_menu (menu_name, menu_type, parent_id, perms_type, api_perms, context_menu_id, visible_flag, disabled_flag, deleted_flag, create_user_id)
SELECT
    unnest(ARRAY['查询', '新增', '编辑', '删除', '导入', '导出']) as menu_name,
    3 as menu_type,
    supplier_menu.menu_id as parent_id,
    1 as perms_type,
    unnest(ARRAY['supplier:query', 'supplier:add', 'supplier:update', 'supplier:delete', 'supplier:import', 'supplier:export']) as api_perms,
    260 as context_menu_id,
    TRUE as visible_flag,
    FALSE as disabled_flag,
    FALSE as deleted_flag,
    1 as create_user_id
FROM supplier_menu;
```

### 4. 菜单配置最佳实践

#### 权限标识命名规范
```typescript
// 权限标识格式：{moduleCode}:{action}
const permissions = {
  query: 'supplier:query', // 查询权限
  add: 'supplier:add', // 新增权限
  edit: 'supplier:update', // 编辑权限（注意：后端使用update）
  delete: 'supplier:delete', // 删除权限
  import: 'supplier:import', // 导入权限
  export: 'supplier:export', // 导出权限
  view: 'supplier:view' // 查看权限（可选）
}
```

#### 路由路径规范
```typescript
// 路由路径格式：/{module-path}/list
const routePaths = {
  supplier: '/supplier/list',
}
```

#### 组件路径规范
```typescript
// 组件路径格式：/business/erp/{module-path}/{ModuleName}List.vue
const componentPaths = {
  supplier: '/business/erp/supplier/SupplierList.vue',
}
```

### 5. 菜单配置检查清单

#### 开发完成后检查项
- [ ] 创建了对应的菜单SQL文件
- [ ] 菜单名称与模块功能一致
- [ ] 路由路径与前端路由配置一致
- [ ] 组件路径与实际文件路径一致
- [ ] 权限标识与前端权限指令一致
- [ ] 包含了所有必要的权限项（查询、新增、编辑、删除、导入、导出）
- [ ] SQL语法正确，可以正常执行
- [ ] 删除重复菜单的语句正确

#### 权限配置验证
```sql
-- 验证菜单是否正确创建
SELECT menu_name, menu_type, path, component, api_perms
FROM t_menu
WHERE menu_name = '{模块中文名}'
   OR api_perms LIKE '{moduleCode}:%'
ORDER BY menu_type, menu_name;
```

### 6. 常见问题及解决方案

#### 问题1：菜单重复创建
**解决方案**：在SQL开头添加删除重复菜单的语句
```sql
DELETE FROM t_menu WHERE menu_name = '{模块中文名}' AND deleted_flag = FALSE;
DELETE FROM t_menu WHERE api_perms LIKE '{moduleCode}:%' AND deleted_flag = FALSE;
```

#### 问题2：权限标识不匹配
**解决方案**：确保前端权限指令与菜单SQL中的api_perms一致
```vue
<!-- 前端权限指令 -->
<Button v-privilege="'supplier:add'">
新增
</Button>
```
```sql
-- 对应的菜单权限
'supplier:add'
```

#### 问题3：路由路径不匹配
**解决方案**：确保菜单path与前端路由配置一致
```typescript
// 前端路由配置
{
  path: '/supplier/list',
  component: () => import('@/views/business/erp/supplier/SupplierList.vue')
}
```
```sql
-- 对应的菜单路径
path: '/supplier/list'
component: '/business/erp/supplier/SupplierList.vue'
```

## 总结

本规范涵盖了ERP业务CRUD模块开发的各个方面，包括架构设计、文件组织、代码实现、样式规范、权限控制、错误处理和性能优化。特别强调了List页面查询表单与Form组件数据表单的差异化处理，以及表单验证的分层策略。遵循此规范可以确保：

1. **代码一致性**：统一的架构和命名规范
2. **开发效率**：标准化的开发模式和工具
3. **维护性**：清晰的分层和职责分离
4. **扩展性**：灵活的服务层和组件设计
5. **用户体验**：统一的交互模式和视觉效果
6. **表单规范性**：明确区分查询表单和数据表单的不同处理方式
7. **验证合理性**：基础信息必填，扩展信息选填，自动字段排除验证

开发团队应严格遵循此规范，确保项目的长期可维护性和代码质量。
